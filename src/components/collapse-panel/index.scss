.collapse-panel {
  background-color: #F5F8FD;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    cursor: pointer;
    background-color: #fafafa;
    border-bottom: 1px solid #e6e6e6;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }

  &-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
    flex: 1;
  }

  &-icon {
    font-size: 16px;
    color: #666;
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    flex-shrink: 0;

    &.expanded {
      transform: rotate(180deg);
    }
  }

  &-content-wrapper {
    overflow: hidden;
    transition: height 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    background-color: #fff;
  }

  &-content {
    padding: 20px;
  }
}