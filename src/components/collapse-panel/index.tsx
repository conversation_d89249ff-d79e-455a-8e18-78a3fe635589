import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Icon } from '@alifd/next';
import './index.scss';

interface CollapsePanelProps {
  title: React.ReactNode;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  className?: string;
}

const CollapsePanel: React.FC<CollapsePanelProps> = ({ title, children, defaultExpanded = false, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [contentHeight, setContentHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);

  const updateHeight = useCallback(() => {
    if (contentRef.current) setContentHeight(contentRef.current.scrollHeight);
  }, []);

  useEffect(() => {
    updateHeight();
    const resizeObserver = new ResizeObserver(updateHeight);
    if (contentRef.current) resizeObserver.observe(contentRef.current);
    return () => resizeObserver.disconnect();
  }, [updateHeight, children]);

  const toggleExpanded = useCallback(() => setIsExpanded(prev => !prev), []);

  return (
    <div className={`collapse-panel ${className}`}>
      <div className="collapse-panel-header" onClick={toggleExpanded}>
        <h3 className="collapse-panel-title">{title}</h3>
        <Icon type="arrow-down" className={`collapse-panel-icon ${isExpanded ? 'expanded' : ''}`} />
      </div>
      <div className="collapse-panel-content-wrapper" style={{ height: isExpanded ? `${contentHeight}px` : '0px' }}>
        <div ref={contentRef} className="collapse-panel-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default CollapsePanel;
