import upload from '@ali/filebroker';
import { <PERSON><PERSON>, <PERSON>alog, Icon, Message } from '@alifd/next';
import { AddressSelector, useValueConvert } from '@alife/asc-components';
import * as i18n from '@alife/lazada-i18n';
import { ASCTools, axios, getCookie, request } from '@alife/workstation-utils';
import { DatePicker, Input, Radio, Select, Upload } from '@formily/next';
import { connect, mapProps } from '@formily/react';
import { useMemoizedFn } from 'ahooks';
import last from 'lodash/last';
import moment from 'moment';
import React from 'react';
import commonLocale from '../../constants/common-locale';
import {
  useUpdateLoginContact,
  useUpdateLoginPassword,
  useUpdateSellerContact,
  useUpdateWhatsAppContact,
} from '../../hooks/update-contact';
import BankSelector from '../bank-selector';
import './index.scss';
import { ContactTextList } from './ui-types/contact-text-list';
import dayjs from 'dayjs';

const noop = () => {};

const modificationText = i18n.formatMessage({
  id: 'page.account.setting.modification',
  defaultMessage: 'Modification',
});

const country = ASCTools.getCountry();
const { RangePicker } = DatePicker;
const ContactText = props => {
  const [ContactDialog, { onContactEdit }] = useUpdateSellerContact({
    onOk: () => {
      props.reloadPage?.();
    },
  });

  const prefix = props.prefix ? `${props.prefix} ` : '';
  const contactValue = props.value ? `${prefix}${props.value}` : '';
  return (
    <div>
      {contactValue ? (
        <span className="mr5">{contactValue}</span>
      ) : (
        <span className="color-#999 m-r-5">{commonLocale['page.common.tips.notSet']}</span>
      )}

      {!props.disabled ? (
        <Button
          onClick={() => onContactEdit(props, { title: `${props.fieldLabel} ${modificationText}` }).catch(noop)}
          data-spm="d_modify_seller_contact"
          className="aplus-auto-exp"
          text
        >
          {commonLocale['page.common.btn.modify']}
        </Button>
      ) : null}
      <ContactDialog />
    </div>
  );
};

const LoginContactText = props => {
  const [LoginContactDialog, { onContactEdit: onLoginContactEdit }] = useUpdateLoginContact({
    onOk: () => {
      props.reloadPage?.();
    },
  });

  const prefix = props.prefix ? `${props.prefix} ` : '';
  const contactValue = props.value ? `${prefix}${props.value}` : '';
  return (
    <div>
      {contactValue ? (
        <span className="mr5">{contactValue}</span>
      ) : (
        <span className="not-set-text">{commonLocale['page.common.tips.notSet']}</span>
      )}

      {!props.disabled ? (
        <Button
          onClick={() => onLoginContactEdit(props, { title: `${props.fieldLabel} ${modificationText}` }).catch(noop)}
          data-spm="d_modify_login_contact"
          className="aplus-auto-exp"
          text
        >
          {commonLocale['page.common.btn.modify']}
        </Button>
      ) : null}
      <LoginContactDialog />
    </div>
  );
};

const PasswordText = props => {
  const [PasswordDialog, { onPasswordEdit }] = useUpdateLoginPassword({
    onOk: () => {
      // redirect to login page after update password success
      location.replace(`/apps/seller/login?redirect_url=${encodeURIComponent(location.href)}`);
    },
  });
  return (
    <div>
      <span className="not-set-text">{props.hasPassword ? '******' : commonLocale['page.common.tips.notSet']}</span>
      {!props.disabled ? (
        <Button
          onClick={() =>
            onPasswordEdit(props, {
              title: i18n.formatMessage({
                id: 'page.account.setting.passwordModification',
                defaultMessage: 'Password Modification',
              }),
            }).catch(noop)
          }
          className="aplus-auto-exp"
          data-spm="d_modify_password"
          text
        >
          {commonLocale['page.common.btn.modify']}
        </Button>
      ) : null}
      <PasswordDialog />
    </div>
  );
};

const ShopNameText = props => (
  <div>
    <span className="mr5">{props.value}</span>
    <a data-spm="d_modify_shopname" className="aplus-auto-exp" href="/site/profile/infoSettings" target="_blank">
      {commonLocale['page.common.btn.modify']}
    </a>
  </div>
);
const WhatsAppContactText = props => {
  const [WhatsAppContactDialog, { onContactEdit: onWhatsAppContactEdit }] = useUpdateWhatsAppContact({
    onOk: () => {
      props.reloadPage?.();
    },
  });

  const prefix = props.prefix ? `${props.prefix} ` : '';
  const contactValue = props.value ? `${prefix}${props.value}` : '';
  return (
    <div>
      {contactValue ? (
        <span className="mr5">{contactValue}</span>
      ) : (
        <span className="not-set-text">{commonLocale['page.common.tips.notSet']}</span>
      )}

      {!props.disabled ? (
        <Button
          onClick={() => onWhatsAppContactEdit(props, { title: `${props.fieldLabel} ${modificationText}` }).catch(noop)}
          data-spm="d_modify_whats_app_contact"
          className="aplus-auto-exp"
          text
        >
          {commonLocale['page.common.btn.modify']}
        </Button>
      ) : null}
      <WhatsAppContactDialog />
    </div>
  );
};

const AddressFormily = connect(
  props => {
    const [value, onChange] = useValueConvert<string[], string>(props, {
      convert(val) {
        return last(val);
      },
      reverse(v, item, extra) {
        let newValue: string[] = [];
        if (extra.selectedPath?.length) {
          newValue = extra.selectedPath.map(sitem => sitem.value);
        }
        if (props.iso === 'SG' && newValue.length && newValue[0] !== '*********') {
          // SG first level fix it as *********
          newValue.unshift('*********');
        }
        return newValue;
      },
    });
    // const value = props.value?.length ? props.value[props.value.length - 1] : null;
    const newProps = { ...props, value, onChange };

    // if (props.iso === 'SG') {
    //   // show the postcode for SG only
    //   newProps.maxLevel = 6;
    // }
    const loadPathDataService = props => {
      const { level, id } = props;
      return axios({
        url: 'mtop.lazada.merchant.onboard.task.address.list',
        method: 'POST',
        data: {
          queryLevel: level,
          locationId: id,
        },
      }).then(res =>
        res.data.data.map(({ value: id, label, isLeaf, parentId }) => ({
          id: id,
          iso: props.iso || country,
          leaf: isLeaf,
          isLeaf,
          language: 'EN',
          level: level,
          names: { EN: label },
          parentId,
        })),
      );
    };
    return (
      <AddressSelector
        maxLevel={6}
        loadEachData={async record => {
          const { level, id } = record;
          const { data: res = [] } = await request({
            url: 'mtop.lazada.merchant.onboard.task.address.list',
            method: 'POST',
            data: {
              queryLevel: level,
              locationId: id,
            },
          });
          return res.map(({ value: leafId, label, isLeaf }) => ({
            id: leafId,
            leaf: isLeaf,
            language: 'EN',
            names: { EN: label },
          }));
        }}
        loadRegionChain={async () => {
          const { value: address = [] } = props;
          return Promise.all(
            address.map((id, idx) =>
              loadPathDataService({ level: idx + 1, id: address[idx - 1] }).then(data =>
                data?.find(item => item.id == id),
              ),
            ),
          );
        }}
        {...newProps}
      />
    );
  },
  mapProps({}, (props, field) => {
    return {
      ...props,
      disabled: field.readPretty || props.disabled,
    };
  }),
);

const BankSelectorFormily = connect(
  BankSelector,
  mapProps({}, (props, field) => {
    return {
      ...props,
      disabled: field.readPretty || props.disabled,
    };
  }),
);
// for our scenario, we only need to support 1 file mode
const UploadFormily = connect(
  props => {
    const onRemove = useMemoizedFn(() => {
      props.onChange(null);
      return true;
    });

    const value = props.value?.uploadId ? props.value : null;
    const uploadValues = value
      ? [
          {
            name: value?.name,
            url: value?.downloadUrl || value?.downloadURL,
            downloadURL: value?.downloadUrl || value?.downloadURL,
            imgURL: value?.downloadUrl || value?.downloadURL,
            uploadId: value?.uploadId,
            mime: value?.mime,
          },
        ]
      : undefined;

    const formatter = useMemoizedFn((res, file) => {
      return {
        success: parseInt(res.code) === 0,
        ...res,
        mime: file.type,
      };
    });

    const onSuccess = useMemoizedFn(info => {
      const res = info.response || {};
      props.onChange({
        name: res.imgName,
        url: res.downloadUrl,
        downloadURL: res.downloadUrl,
        imgURL: res.downloadUrl,
        uploadId: res.fileUrl,
        mime: res.mime,
      });
    });

    const beforeUpload = useMemoizedFn((file, options) => {
      if (options.data) {
        options.data.name = file.name;
      } else {
        options.data = {
          name: file.name,
        };
      }
      return options;
    });

    const showImg = url => {
      Dialog.show({
        title: 'img preview',
        content: <img src={url} style={{ width: 400, height: 400 }} />,
        footer: false,
      });
    };
    const actionRender = file => {
      return (
        <span style={{ marginRight: 20 }}>
          <Button
            text
            onClick={e => {
              e.preventDefault();
              showImg(value?.downloadUrl || value?.downloadURL);
            }}
            size="large"
          >
            <Icon type="eye" style={{ marginRight: 8, cursor: 'pointer' }} />
          </Button>

          <Button text component="a" href={value?.downloadUrl || value?.downloadURL} target="_blank">
            <Icon type="download" style={{ cursor: 'pointer' }} />
          </Button>
        </span>
      );
    };
    const onError = res => {
      const { message, success } = res?.response ?? {};
      if (!success) {
        Message.error(message);
      }
    };

    const csrfToken = window.csrfToken || {
      tokenValue: getCookie('c_csrf') || '',
      tokenName: 'X-XSRF-TOKEN' || '',
    };
    return (
      <Upload
        fileKeyName="file"
        headers={{
          [csrfToken.tokenName]: csrfToken.tokenValue,
        }}
        // data={{ name: 'xxxxxxx.jpeg' }}
        beforeUpload={beforeUpload}
        name={props.name}
        accept={props.accept}
        maxSize={props.maxSize}
        limit={1}
        disabled={props.disabled}
        required={props.required}
        action="/profile/upload_profile"
        formatter={formatter}
        value={uploadValues}
        onRemove={onRemove}
        onSuccess={onSuccess}
        actionRender={actionRender}
        onError={onError}
        listType="image"
      >
        <Button>
          <Icon type="upload" />
          {commonLocale['page.common.btn.upload']}
        </Button>
      </Upload>
    );
  },
  mapProps({}, (props, field) => {
    return {
      ...props,
      disabled: field.readPretty || props.disabled,
    };
  }),
);

// 新的接入的上传，替换 /profile/upload_profile
const UploadDocument = connect(
  props => {
    const isPre = location.hostname.includes('staging');
    const UPLOAD_HOST = {
      SG: isPre ? 'filebroker-staging.lazada.sg' : 'filebroker.lazada.sg',
      TH: isPre ? 'filebroker-staging.lazada.co.th' : 'filebroker.lazada.co.th',
      VN: isPre ? 'filebroker-staging.lazada.vn' : 'filebroker.lazada.vn',
      MY: isPre ? 'filebroker-staging.lazada.com.my' : 'filebroker.lazada.com.my',
      PH: isPre ? 'filebroker-staging.lazada.com.ph' : 'filebroker.lazada.com.ph',
      ID: isPre ? 'filebroker-staging.lazada.co.id' : 'filebroker.lazada.co.id',
      PK: isPre ? 'filebroker.daraz.pk' : 'filebroker.daraz.pk', // 预发的域名被下掉了，暂时先用线上域名
      BD: isPre ? 'filebroker.daraz.com.bd' : 'filebroker.daraz.com.bd',
      NP: isPre ? 'filebroker.daraz.com.np' : 'filebroker.daraz.com.np',
      LK: isPre ? 'filebroker.daraz.lk' : 'filebroker.daraz.lk',
      MM: isPre ? 'filebroker.shop.com.mm' : 'filebroker.shop.com.mm',
    }[country];

    const value = props.value?.uploadId ? props.value : null;
    const uploadValues = value
      ? [
          {
            name: value?.name,
            url: value?.downloadUrl || value?.downloadURL,
            downloadURL: value?.downloadUrl || value?.downloadURL,
            imgURL: value?.downloadUrl || value?.downloadURL,
            uploadId: value?.uploadId,
            mime: value?.mime,
          },
        ]
      : undefined;

    const onRemove = useMemoizedFn(() => {
      props.onChange(null);
      return true;
    });

    const formatter = useMemoizedFn((res, file) => {
      return {
        success: parseInt(res.code) === 0,
        ...res,
        mime: file.type,
      };
    });

    const showImg = url => {
      Dialog.show({
        title: 'img preview',
        content: <img src={url} style={{ width: 400, height: 400 }} />,
        footer: false,
      });
    };

    const actionRender = file => {
      return (
        <span style={{ marginRight: 20 }}>
          <Button
            text
            onClick={e => {
              e.preventDefault();
              showImg(value?.downloadUrl || value?.downloadURL);
            }}
            size="large"
          >
            <Icon type="eye" style={{ marginRight: 8, cursor: 'pointer' }} />
          </Button>

          <Button text component="a" href={value?.downloadUrl || value?.downloadURL} target="_blank">
            <Icon type="download" style={{ cursor: 'pointer' }} />
          </Button>
        </span>
      );
    };

    const fileNameRender = file => {
      return file?.name;
    };

    return (
      <Upload
        action={`//${UPLOAD_HOST}/upload/secret`}
        data={{ bizCode: `${country}_SELLER_PROFILE_UPLOAD` }}
        fileKeyName="file"
        // headers={{
        //   [csrfToken.tokenName]: csrfToken.tokenValue,
        // }}
        name={props.name}
        accept={props.accept}
        limit={props.maxSize}
        disabled={props.disabled}
        formatter={formatter}
        value={uploadValues}
        onRemove={onRemove}
        actionRender={actionRender}
        fileNameRender={fileNameRender}
        listType="image"
        request={options => {
          const {
            action,
            file,
            data: { bizCode },
            onProgress,
            onSuccess,
            onError,
          } = options;
          // 上传到 filebroker
          upload({
            file,
            url: `${action}`,
            bizCode,
            onData: progress => {
              const { total, percent: uploadPercent, loaded } = progress;
              onProgress({ total, percent: uploadPercent, loaded });
            },
          })
            .then(({ data: origin }) => {
              const { filename, code } = origin;
              const { name, type } = file;
              // filebroker 接口成功
              if (code === 0) {
                // 调用商家基础接口保存
                axios({
                  url: 'mtop.global.merchant.subaccount.sellerprofile.fileMetas.save',
                  data: { originalName: name, uniqueName: filename, mime: type },
                })
                  .then(({ data }) => {
                    const res = data?.data ?? {};
                    props.onChange &&
                      props.onChange({
                        name: res.imgName,
                        url: res.downloadUrl,
                        downloadURL: res.downloadUrl,
                        imgURL: res.downloadUrl,
                        uploadId: res.fileUrl,
                        mime: res.mime,
                      });
                    onSuccess('Upload Success');
                    Message.success('Upload Success');
                  })
                  .catch(err => {
                    onError('Upload Failed');
                    Message.error(err.error || 'Upload Failed');
                  });
              } else {
                onError('Upload Failed');
                Message.error('Upload Failed');
              }
            })
            .catch(() => {
              onError('Upload Failed');
              Message.error('Upload Failed');
            });
        }}
      >
        <Button>
          <Icon type="upload" />
          {commonLocale['page.common.btn.upload']}
        </Button>
      </Upload>
    );
  },
  mapProps({}, (props, field) => {
    return {
      ...props,
      disabled: field.readPretty || props.disabled,
    };
  }),
);

const DatePickerCustom = props => {
  return (
    <DatePicker
      {...props}
      value={moment(+props.value).format('YYYY-MM-DD')}
      onChange={value => props.onChange && props.onChange(+moment(value))}
    />
  );
};

const RangeDate = props => {
  let valArr = props.value && Array.isArray(props.value) ? props.value : ['', ''];
  let start = valArr[0] && valArr[0].length > 0 ? valArr[0] : undefined;
  let end = valArr[1] && valArr[1].length > 0 ? valArr[1] : undefined;
  const currentDate = dayjs();

  if (!props.form.editable && start === undefined && end === undefined) {
    return <div>-</div>;
  }
  return (
    <RangePicker
      {...props}
      style={{ width: 300 }}
      value={[start, end]}
      onChange={value => {
        props.onChange && props.onChange([value[0] ?? '', value[1] ?? '']);
      }}
      disabledDate={date => {
        return date.valueOf() <= currentDate.valueOf();
      }}
    />
  );
};

export const TEXT_UI_TYPES = ['LoginContactText', 'ContactText', 'ShopNameText', 'PasswordText', 'WhatsAppContactText'];

// the value is not string/number, need to compare by JSON.stringify
export const OBJECT_UI_TYPE = ['Address', 'Upload', 'BankSelector'];

export const UI_TYPE_MAP = {
  Input,
  RadioGroup: Radio.Group,
  Select,
  ContactText,
  LoginContactText,
  WhatsAppContactText,
  ShopNameText,
  PasswordText,
  Address: AddressFormily,
  Upload: UploadDocument,
  BankSelector: BankSelectorFormily,
  DatePicker: DatePickerCustom,
  ContactTextList: ContactTextList,
  RangeDate: RangeDate,
};
