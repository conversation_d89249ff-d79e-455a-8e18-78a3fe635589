import { <PERSON>oon, Button, Card, Icon, Loading, Message } from '@alifd/next';
import { SubmitAffix } from '@alife/asc-components';
import { withTrackEffects } from '@alife/form-track-plugin';
import * as i18n from '@alife/lazada-i18n';
import { ASCTools } from '@alife/workstation-utils';
import { Field as IField, IFieldProps, createForm, onFieldMount, onFieldValueChange } from '@formily/core';
import { Form, FormItem, FormLayout, Reset, Submit } from '@formily/next';
import { Field, FormConsumer } from '@formily/react';
import { useCreation, useMemoizedFn, useRequest } from 'ahooks';
import React, { useState } from 'react';
import commonLocale from '../../constants/common-locale';
import { useOtpVerify } from '../../hooks/otp-verify';
import { useUpdateHistory } from '../../hooks/update-history';
import GlobalNotice, { IGlobalNoticeData } from './global-notice';
import { OBJECT_UI_TYPE, TEXT_UI_TYPES, UI_TYPE_MAP } from './ui-types';

import './index.scss';

const reactions = {
  // Are you SST registered?
  873: {
    874: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
    875: (field, form) => {
      return state => {
        state.required = form.query('873').value() === '1';
        state.visible = form.query('873').value() === '1';
      };
    },
  },

  // Are you SST registered?
  880: {
    881: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
    882: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
  },

  // Are you Tourism(TTx) registered?
  877: {
    878: (field, form) => {
      return state => {
        state.required = form.query('877').value() === '1';
        state.visible = form.query('877').value() === '1';
      };
    },
    879: (field, form) => {
      return state => {
        state.required = form.query('877').value() === '1';
        state.visible = form.query('877').value() === '1';
      };
    },
  },

  // Are you Tourism(TTx) registered?
  883: {
    884: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
    885: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
  },

  // Do you have a TIN number?
  886: {
    887: (field, form) => {
      return state => {
        state.required = form.query('886').value() === '1';
        state.visible = form.query('886').value() === '1';
      };
    },
    626: (field, form) => {
      return state => {
        state.required = form.query('886').value() === '1';
        state.visible = form.query('886').value() === '1';
      };
    },
  },

  // Do you have a TIN number?
  888: {
    889: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
    625: (field, form) => {
      return state => {
        state.required = field.value === '1';
        state.visible = field.value === '1';
      };
    },
  },
};

const reactionsKeys = Object.keys(reactions);

const currentBU = ASCTools.getBU();
const noop = () => {};
const getFormComponentByUiType = uiType => {
  return UI_TYPE_MAP[uiType];
};

interface IGroup {
  title: React.ReactNode;
  fieldIds: string[];
}

interface IProps {
  pannelKey: string;
  services: {
    render: () => Promise<any>;
    precheck: (data: any) => Promise<any>;
    submit: (data: any, fields?: IFieldItem[]) => Promise<any>;
  };
  // MRender提供额外业务逻辑入口，通过该入口可以干涉MRender，从而实现一些业务逻辑
  externalAction?: (form: any) => void;
  needTips?: boolean;
  needTermsAndConditions?: React.ReactNode;
  grouping?: IGroup[];
}

export interface IFieldItem {
  name: string;
  needOtp: boolean;
  uiType: string;
}

interface IMamualReturnData {
  pageError: string;
  fieldList: IFieldItem[];
  globalNotice: IGlobalNoticeData;
  globalDisabled: boolean;
}

interface ProfileRuleItem {
  ruleDetail: string;
  ruleDetailDesc: string;
  whiteListType: string;
  whiteListValues: string[];
}

const VAT_CASCADE_FIELD = {
  Individual: {
    VATField: '478',
    linkageField: ['477', '859', '860'],
    invoicingField: ['861'],
  },
  Corporate: {
    VATField: '38',
    linkageField: ['37', '606', '607'],
    invoicingField: ['608'],
  },
};
const country = ASCTools.getCountry();

export default (props: IProps) => {
  // this createFrom must be wrote inside function,
  // because this component will be used on many tab in same page
  const pannelSpm = props.pannelKey;
  const [form, setTrackerError] = useCreation(() => {
    const getFieldId = (fprops: IFieldProps) => {
      const componentProps = fprops.component?.[1];
      if (componentProps && componentProps.fieldName) {
        return componentProps.fieldName;
      }
      fprops.name.toString();
    };

    const { autoFormTrack, setError } = withTrackEffects({
      formKey: pannelSpm,
      getFieldId,
      getFieldLabel: getFieldId,
    });

    return [
      createForm({
        editable: false,
        effects() {
          autoFormTrack();
          onFieldMount('98', (field: IField, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Individual') {
              form.setFieldState('99', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
              form.setFieldState('100', state => {
                state.visible = field.value === '0';
              });
            }
          });
          onFieldMount('100', (field: IField, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Individual') {
              form.setFieldState('101', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
            }
          });
          onFieldMount('102', (field: IField, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Corporate') {
              form.setFieldState('103', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
              form.setFieldState('104', state => {
                state.visible = field.value === '0';
              });
            }
          });
          onFieldMount('104', (field: IField, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Corporate') {
              form.setFieldState('105', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
            }
          });
          onFieldValueChange('98', (field, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Individual') {
              form.setFieldState('99', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
              form.setFieldState('100', state => {
                state.visible = field.value === '0';
              });
            }
          });
          onFieldValueChange('100', (field, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Individual') {
              form.setFieldState('101', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
            }
          });
          onFieldValueChange('102', (field, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Corporate') {
              form.setFieldState('103', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
              form.setFieldState('104', state => {
                state.visible = field.value === '0';
              });
            }
          });
          onFieldValueChange('104', (field, form) => {
            // PH 个人卖家
            if (country === 'PH' && form.query('0').value() === 'Corporate') {
              form.setFieldState('105', state => {
                state.required = field.value === '1';
                state.visible = field.value === '1';
              });
            }
          });

          reactionsKeys.forEach(key => {
            const subReaction = reactions[key];
            Object.keys(subReaction).forEach(subKey => {
              onFieldValueChange(key, (field, form) => {
                form.setFieldState(subKey, subReaction[subKey](field, form));
              });
              onFieldMount(key, (field: IField, form) => {
                form.setFieldState(subKey, subReaction[subKey](field, form));
              });
            });
          });
        },
      }),

      setError,
    ];
  }, [pannelSpm]);

  const { grouping } = props;
  const groupedFieldIds = grouping ? grouping.flatMap(g => g.fieldIds) : [];
  const [shouldShowModifyBar, setShouldShowModifyBar] = useState(false);
  const [showDialog, setshowDialog] = useState(false);
  const resolveField = (data: any = {}) => data?.data?.fields || [];
  const externalAction = props.externalAction || noop;
  // debugger
  const { data: renderData = {}, loading, run } = useRequest(() => {
    return props.services
      ?.render()
      .then(({ data: result }) => {
        const globalDisabled = result?.data?.globalDisabled;
        const fl = resolveField(result).map(item => {
          if (!globalDisabled) {
            return item;
          }
          item.disabled = true;
          return item;
        });
        const fm = fl.reduce((res, item) => {
          res[item.name] = item.value;
          if (!shouldShowModifyBar && !TEXT_UI_TYPES.includes(item.uiType) && !item.disabled) {
            setShouldShowModifyBar(true);
          }
          return res;
        }, {});
        // it's very important to set initial values
        // so that we can compare the initial values with final values when submit
        // to check if we need to show OTP verification
        form.setInitialValues(fm);

        // console.log(' render 时候的数据:');
        // console.log(fm);

        // we also need to set values here
        // so that we can re-render the values after user submitted of cancel
        form.setValues(fm);
        externalAction(form);
        // todo need to remove
        // form.setState(state => {
        //   state.editable = true;
        // });
        return {
          fieldList: fl,
          globalNotice: result?.data?.globalNotice,
          globalDisabled,
          pageError: null,
        };
      })
      .catch(e => {
        return {
          fieldList: [],
          globalNotice: null,
          globalDisabled: false,
          pageError: e.data?.data?.error || e.message,
        };
      });
  });

  const { fieldList = [], pageError, globalNotice, globalDisabled = false } = renderData as IMamualReturnData;

  const [HistoryDialog, { onShowHistory }] = useUpdateHistory({});

  const setFormEditing = bool => {
    form.setState(state => {
      state.editable = bool;
    });
    if (bool === false) {
      run();
    }
  };

  const doSubmitService = useMemoizedFn((options?: any) => {
    const { otpCode } = options || {};
    return props.services?.submit({ ...form.values, otpCode }, fieldList).then(() => {
      Message.success(commonLocale['page.common.tips.success']);
      setFormEditing(false);
    });
  });

  const doSubmit = useMemoizedFn((options?: any) => {
    return doSubmitService(options).catch(e => {
      const message = e.data?.data?.error || e.message;
      Message.error(message);
      setTrackerError(message);
      console.error(e);
    });
  });

  const [OtpDialog, { onShowOtp }] = useOtpVerify({});
  const onSubmit = useMemoizedFn(
    (): Promise<any> => {
      // opt valid
      let needOtpValid = false;
      for (const item of fieldList) {
        if (item.needOtp) {
          const fstate = form.getFieldState(item.name);

          if (OBJECT_UI_TYPE.includes(item.uiType)) {
            if (JSON.stringify(fstate.initialValue) !== JSON.stringify(fstate.value)) {
              needOtpValid = true;
              break;
            }
          } else if (fstate.initialValue !== fstate.value) {
            needOtpValid = true;
            break;
          }
        }
      }

      if (needOtpValid) {
        // if need opt valid, to the precheck
        // to avoid wrong input but opt valid times again and again

        const formValues = { ...form.values };

        return props.services
          ?.precheck(formValues)
          .then(() => {
            return onShowOtp(
              { doSubmitService },
              {
                title: i18n.formatMessage({
                  id: 'page.account.setting.pleaseVerifyYourAccount',
                  defaultMessage: 'Please verify your account',
                }),
              },
            ).catch(() => {
              form.submitting = false;
            });
          })
          .catch(e => {
            Message.error(e.message);
            return null;
          });
      }

      // console.log(' 提交 时候的数据:');
      // console.log(JSON.parse(JSON.stringify(form.values)));
      // return
      return doSubmit();
    },
  );

  const historyText = i18n.formatMessage({
    id: 'page.account.setting.modificationHistory',
    defaultMessage: 'Modification History',
  });

  const CurrentField = (inputData: any) => {
    const { item = {} } = inputData || {};
    const sellerProfileRuleList: ProfileRuleItem[] = item?.sellerProfileRuleList ?? [];

    const title = (
      <span>
        <span className="mr5">{item.label}</span>
        {item.history || item.tips ? (
          <Balloon
            align="r"
            triggerType="click"
            trigger={<Icon type="prompt" size="small" />}
            closable={false}
            data-spm="tip_wrap"
          >
            <div dangerouslySetInnerHTML={{ __html: item.tips }} />
            {item.history ? (
              <Button
                data-spm="d_modification_history"
                onClick={() => onShowHistory(item, { title: `${item.label} ${historyText}` }).catch(noop)}
                text
              >
                {historyText}
              </Button>
            ) : null}
          </Balloon>
        ) : null}
      </span>
    );

    const fieldProps: any = {
      name: item.name,
      title: title,
      required: item.required,
      decorator: [FormItem],
      visible:
        country === 'TH' &&
        (VAT_CASCADE_FIELD[form.values['0']]?.linkageField.includes(item?.name) ||
          VAT_CASCADE_FIELD[form.values['0']]?.invoicingField.includes(item?.name))
          ? false
          : true,
      description: item.warning ? (
        <Message style={{ marginBottom: 16 }} type="warning">
          {item.warning}
          &nbsp;&nbsp;&nbsp;
          <a href={item.qcFileUrl}>{item.qcOriginalName}</a>
        </Message>
      ) : null,
      validator: value => {
        // daraz的手机号数据库有脏数据，导致提交时哪怕不改手机号，也因为格式错误无法提交，因此如果初始数据和提交数据相同时，不进行正则校验
        if (currentBU === 'DARAZ' && item.name === '10') {
          let fieldObj = form.getFieldState(item.name);

          if (fieldObj.initialValue === fieldObj.value) {
            return '';
          }
        }

        const { sellerId } = window?.dadaConfig || {};
        // 如果sellerProfileRuleList为空，说明没有配置正则，停止校验
        if (sellerProfileRuleList && sellerProfileRuleList?.length === 0) return '';
        // 遍历正则列表
        for (let index = 0; index < sellerProfileRuleList.length; index++) {
          const { ruleDetail, ruleDetailDesc, whiteListType, whiteListValues = [] } =
            sellerProfileRuleList[index] || {};

          // 先检查白名单，whiteListType和whiteListValues一定会同时有值或者无值
          if (whiteListType && whiteListValues.length > 0) {
            if (whiteListType === 'sellerId' && whiteListValues.includes(sellerId)) {
              // 继续下一轮循环，不走正则校验
              continue;
            }
            if (whiteListType === 'value' && whiteListValues.includes(value)) {
              // 继续下一轮循环，不走正则校验
              continue;
            }
          }

          const reg = new RegExp(ruleDetail);
          // 没有设置白名单，或者不在白名单列表，校验正则
          // 如果校验不通过，提示错误信息
          if (!reg.test(value)) {
            return ruleDetailDesc || 'error';
          }
          return '';
        }
      },
    };

    const tips = item?.tips?.length > 59 ? item?.tips.slice(0, 59) + '...' : item?.tips;
    // label field will be settle inside Input/Select/Address Component, we don't need it
    const component = getFormComponentByUiType(item.uiType);
    if (component) {
      fieldProps.component = [
        component,
        {
          ...item,
          label: '',
          fieldLabel: item.label,
          reloadPage: run,
          placeholder: sellerProfileRuleList?.[0]?.ruleDetailDesc
            ? sellerProfileRuleList?.[0]?.ruleDetailDesc
            : tips
            ? tips
            : '',
          form: form,
        },
      ];

      return (
        <Field
          {...fieldProps}
          reactions={field => {
            const name = item?.name;
            // 卖家类型
            const sellerType = form.values['0'];
            // 当前卖家类型需要匹配的级联字段
            const currantVatField = VAT_CASCADE_FIELD[sellerType] || {};
            // 当前卖家类型需要匹配的字段
            const currentLinkageField = currantVatField?.linkageField || [];
            // 当前卖家地址需要匹配的卖家银行支行字段
            const currentInvoicingField = currantVatField?.invoicingField || [];

            // 是否选择的 YES
            const isIndividualVat = form.values[currantVatField.VATField] === '1';
            // 是否 VAT 的级联校验字段
            const isVatLinkage = currentLinkageField.includes(name);
            // 是否是 VAT 和 Address 的级联校验字段
            const isInvoicingLinkage = currentInvoicingField.includes(name);

            if (country === 'TH' && (isVatLinkage || isInvoicingLinkage)) {
              field.visible = isIndividualVat;
            }
            if (country === 'TH' && isVatLinkage) {
              field.required = isIndividualVat;
            }
            if (country === 'TH' && isInvoicingLinkage) {
              field.required = form.values[currentLinkageField[currentLinkageField.length - 1]] === 'Branch';
            }
          }}
        />
      );
    }
    return null;
  };
  return (
    <Loading data-spm={pannelSpm} visible={loading} inline={false}>
      {pageError ? (
        <Card>
          <div className="mrender-page-error-text">
            <p>{pageError}</p>
            <p>
              <Button onClick={run}>
                {i18n.formatMessage({ id: 'page.account.setting.clickToReload', defaultMessage: 'Click to Reload' })}
              </Button>
            </p>
          </div>
        </Card>
      ) : (
        <>
          <GlobalNotice
            {...globalNotice}
            globalDisabled={globalDisabled}
            ReRender={(isReRender = false) => {
              if (isReRender) {
                run();
                setshowDialog(false);
              }
            }}
            showDialog={showDialog}
            setshowDialog={setshowDialog}
          />
          <Form form={form} labelCol={6} wrapperCol={10}>
            {grouping ? (
              <>
                {grouping.map((group, index) => {
                  const groupFields = fieldList.filter(field => group.fieldIds.includes(field.name));
                  if (groupFields.length === 0) {
                    return null;
                  }
                  return (
                    <Card
                      title={group.title}
                      key={typeof group.title === 'string' ? group.title : index}
                      style={{ marginBottom: 20 }}
                    >
                      <FormLayout layout="vertical" wrapperWidth={600} colon={false}>
                        {groupFields.map(item => (
                          <CurrentField item={item} key={item.name} />
                        ))}
                      </FormLayout>
                    </Card>
                  );
                })}
                {(() => {
                  const ungroupedFields = fieldList.filter(field => !groupedFieldIds.includes(field.name));
                  if (ungroupedFields.length > 0) {
                    return (
                      <Card title={commonLocale['page.common.other.information.title']}>
                        <FormLayout layout="vertical" wrapperWidth={600} colon={false}>
                          {ungroupedFields.map(item => (
                            <CurrentField item={item} key={item.name} />
                          ))}
                        </FormLayout>
                      </Card>
                    );
                  }
                  return null;
                })()}
              </>
            ) : (
              <Card>
                <FormLayout layout="vertical" wrapperWidth={600} colon={false}>
                  {fieldList.map(item => (
                    <CurrentField item={item} key={item.name} />
                  ))}
                </FormLayout>
              </Card>
            )}
            <FormConsumer>
              {currentFrom => {
                return (
                  <SubmitAffix style={{ marginTop: 20, display: shouldShowModifyBar ? 'block' : 'none' }}>
                    {globalNotice?.type === 'warning' ? (
                      <Button
                        data-spm="d_modify"
                        className="aplus-auto-exp"
                        type="primary"
                        onClick={() => {
                          setshowDialog(true);
                        }}
                      >
                        {commonLocale['page.account.setting.checkTheProgress']}
                      </Button>
                    ) : null}
                    {currentFrom.editable ? (
                      <>
                        {props?.needTips && (
                          <div
                            style={{ textAlign: 'left', width: '100%' }}
                            dangerouslySetInnerHTML={{ __html: commonLocale['page.todo.iddocAgreement'] }}
                          ></div>
                        )}
                        {props.needTermsAndConditions && (
                          <div style={{ marginRight: 'auto', alignSelf: 'center' }}>
                            {props.needTermsAndConditions}
                          </div>
                        )}
                        <Reset
                          forceClear
                          data-spm="d_cancel"
                          className="aplus-auto-exp"
                          onClick={() => setFormEditing(false)}
                        >
                          {commonLocale['page.common.btn.cancel']}
                        </Reset>
                        <Submit
                          data-spm="d_submit"
                          className="aplus-auto-exp"
                          loading={currentFrom.submitting}
                          onSubmit={onSubmit}
                        >
                          {commonLocale['page.common.btn.submit']}
                        </Submit>
                      </>
                    ) : (
                      <Button
                        data-spm="d_modify"
                        className="aplus-auto-exp"
                        onClick={() => setFormEditing(true)}
                        type="primary"
                      >
                        {commonLocale['page.common.btn.modify']}
                      </Button>
                    )}
                  </SubmitAffix>
                );
              }}
            </FormConsumer>
            <HistoryDialog />
            <OtpDialog />
          </Form>
        </>
      )}
    </Loading>
  );
};
