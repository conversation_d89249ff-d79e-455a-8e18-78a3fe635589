import React, { useEffect, useState } from 'react';
import { Message, Dialog, Table, Pagination, Box } from '@alifd/next';
import { useMemoizedFn, useRequest } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import { timeFormat } from '../../utils/timeFormat';
import commonLocale from '../../constants/common-locale';

export interface IGlobalNoticeData {
  content: string;
  id: string;
  type: 'success' | 'warning' | 'error' | 'notice' | 'help' | 'loading';
  ReRender: (isReRender) => void;
  setshowDialog: (e) => void;
  showDialog: boolean;
  globalDisabled: boolean;
}

export default (props: IGlobalNoticeData) => {
  if (!props.content) {
    return null;
  }

  const [visible, setVisible] = useState<boolean>(false);
  const [currentPage, setcurrentPage] = useState<number>(1);
  const [currentPageSize, setcurrentPageSize] = useState<number>(10);

  const closeable = ['success', 'error'].includes(props.type);
  const readNotice = useMemoizedFn(() => {
    return axios
      .post('mtop.global.merchant.sellermodifylog.read', {
        id: props.id,
      })
      .catch(e => {
        console.error(e);
        // ignore error
      });
  });
  const onClose = useMemoizedFn(() => {
    if (['error'].includes(props.type)) {
      //   debugger;
      readNotice();
    }
  });

  useEffect(() => {
    run();
  }, [currentPage, currentPageSize]);

  useEffect(() => {
    if (props.showDialog) {
      run();
    }
  }, [props.showDialog]);

  useEffect(() => {
    // set success message as read automatically
    if (['success'].includes(props.type)) {
      //   debugger;
      readNotice();
    }
  }, [props.id]);

  //获取提交审核记录
  const { data: data, loading, run } = useRequest(
    () => {
      return axios
        .post('mtop.global.merchant.subaccount.sellerprofile.bpms.list', {
          groupId: 2,
          current: currentPage,
          pageSize: currentPageSize,
        })
        .then(res => {
          return res.data?.data;
        });
    },
    {
      manual: true,
      onError: err => {
        Message.error({ content: err });
      },
    },
  );

  //去除审核记录
  const { run: runReject, loading: loadingReject } = useRequest(
    () => {
      return axios.post('mtop.global.merchant.subaccount.sellerprofile.qc.reject', {
        groupId: 2,
      });
    },
    {
      manual: true,
      onSuccess: result => {
        Message.success(commonLocale['page.common.tips.success']);
        props.ReRender(true);
      },
      onError: err => {
        Message.error({ content: err });
      },
    },
  );

  const updateTimeRender = (value, index, record) => {
    const updateTime = timeFormat(record.updateTime);
    return <span>{updateTime}</span>;
  };

  const handlePageSizeChange = size => {
    setcurrentPageSize(size);
  };

  const onChange = current => {
    setcurrentPage(current);
  };

  const DialogonClose = () => {
    setVisible(false);
    props.setshowDialog(false);
  };

  const DialogCancel = () => {
    runReject().then(() => {
      setVisible(false);
    });
    props.setshowDialog(false);
  };

  const DialogOk = () => {
    setVisible(false);
    props.setshowDialog(false);
  };

  return (
    <Message style={{ marginBottom: 16 }} type={props.type} closeable={closeable} onClose={onClose}>
      <span
        dangerouslySetInnerHTML={{
          __html: props?.content,
        }}
      ></span>
      <a
        href="javascript:;"
        onClick={() => {
          setVisible(true);
          run();
        }}
      >
        &nbsp;&nbsp;&nbsp;
        {props?.type === 'warning' && props?.globalDisabled === false
          ? commonLocale['page.account.setting.checkNow']
          : null}
      </a>
      <Dialog
        title={commonLocale['page.account.setting.checkTheProgress']}
        onClose={DialogonClose}
        onCancel={DialogCancel}
        onOk={DialogOk}
        style={{ padding: '16 24 24', fontSize: '16', color: '#4f5669' }}
        okProps={{ children: commonLocale['page.account.setting.iGoIt'] }}
        cancelProps={{ children: commonLocale['page.account.setting.recall'], type: 'secondary' }}
        visible={visible || props.showDialog}
      >
        <Message type={props.type} style={{ marginBottom: 16 }}>
          <div dangerouslySetInnerHTML={{ __html: props?.content }} />
        </Message>
        <Box style={{ overflow: 'auto', maxHeight: 180 }}>
          <Table loading={loading || loadingReject} dataSource={data?.dataSource}>
            <Table.Column
              title={commonLocale['page.account.setting.progress']}
              htmlTitle="Unique Id"
              dataIndex="progress"
              style={{ width: 250 }}
            />
            <Table.Column
              title={commonLocale['page.account.setting.updateTime']}
              htmlTitle="Unique Id"
              dataIndex="updateTime"
              cell={updateTimeRender}
              style={{ width: 250 }}
            />
            <Table.Column
              title={commonLocale['page.account.setting.remark']}
              htmlTitle="Unique Id"
              dataIndex="remark"
              style={{ width: 250 }}
            />
          </Table>
          <Pagination
            total={data?.pageInfo?.total || 0}
            pageSize={currentPageSize}
            pageSizePosition="start"
            onPageSizeChange={handlePageSizeChange}
            onChange={onChange}
            pageSizeSelector="dropdown"
          />
        </Box>
      </Dialog>
    </Message>
  );
};
