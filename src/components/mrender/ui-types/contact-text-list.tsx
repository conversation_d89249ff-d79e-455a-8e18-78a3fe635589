import * as i18n from '@alife/lazada-i18n';
import { Button, Icon } from '@alifd/next';
import React from 'react';
import commonLocale from '../../../constants/common-locale';
import { useUpdateOtherSellerContact } from '../../../hooks/update-contact';
import { axios } from '@alife/workstation-utils';
import { useRequest } from 'ahooks';
const noop = () => {};

export const ContactTextList = props => {
  const [ContactDialog, { onContactEdit }] = useUpdateOtherSellerContact({
    onOk: () => {
      props.reloadPage?.();
    },
  });

  const { data: optInfo = {} } = useRequest(
    () =>
      axios
        .get('mtop.global.merchant.subaccount.otp.userinfo')
        .then(({ data: res }) => {
          return res.data;
        })
        .catch(e => {}),
    {},
  );

  const prefix = props.prefix ? `${props.prefix} ` : '';
  let contactValue = [];
  try {
    contactValue = JSON.parse(props.value);
  } catch (error) {
    console.log(error);
  }
  return (
    <div>
      {contactValue && contactValue.length ? (
        <>
          <div>{prefix}</div>
          {contactValue.map((item, index) => {
            const key = Object.keys(item)[0];
            return (
              <span className="inline-block hover-color-#1a71ff p-x-2px cursor-pointer">
                <span className="inline-block p-l-2px m-r-1" key={index}>
                  {item[key]}
                </span>
                <Button
                  text
                  // warning
                  disabled={!optInfo.isMaster}
                  // style={{color: "#e64a4a"}}
                  onClick={() =>
                    onContactEdit(
                      { optType: 'delete', optValue: item[key], ...props },
                      {
                        title: `${props.fieldLabel} ${i18n.formatMessage({
                          id: 'page.common.btn.delete',
                          defaultMessage: 'Delete',
                        })}`,
                      },
                    )
                  }
                >
                  <Icon type="delete" style={{ marginRight: 8, cursor: 'pointer' }} />
                </Button>
              </span>
            );
          })}
        </>
      ) : (
        <span className="not-set-text">{commonLocale['page.common.tips.notSet']}</span>
      )}
      {!props.disabled ? (
        <Button
          disabled={contactValue.length >= 10}
          onClick={() =>
            onContactEdit(
              { optType: 'add', ...props },
              {
                title: `${props.fieldLabel} ${i18n.formatMessage({
                  id: 'page.account.setting.contact.add',
                  defaultMessage: 'Add',
                })}`,
              },
            ).catch(noop)
          }
          data-spm="d_add_seller_contact"
          className="aplus-auto-exp"
          text
        >
          {i18n.formatMessage({
            id: 'page.account.setting.contact.add',
            defaultMessage: 'Add',
          })}
        </Button>
      ) : null}
      <ContactDialog />
    </div>
  );
};
