import React, { useMemo } from 'react';
import { Card, Message } from '@alifd/next';
import CollapsePanel from '../collapse-panel';
import Step from '../step';
import { useRequest } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import dayjs from 'dayjs';
import * as i18n from '@alife/lazada-i18n';

import './index.scss';

interface GetSellerProfileResponse {
  qcInfo: {
    globalNoticeSubmitTime: number;
    globalNoticeUpdateTime: number;
    qcComment: string;
    qcStatus: string;
  };
}

export enum QcUpdateStatus {
  reject = 'reject',
  verifing = 'verifing',
  verified = 'verified',
}

const statusTextMap = {
  reject: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.reject', defaultMessage: 'Rejected' }),
    color: '#E72B00',
    bg: '#FFEBE7',
  },
  verifing: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.verifing', defaultMessage: 'Verifing' }),
    color: '#F9AC2A',
    bg: '#FEF7EA',
  },
  verified: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.verified', defaultMessage: 'Verified' }),
    color: '#41A716',
    bg: '#E3F8DA',
  },
};

const statusMap = {
  pending: QcUpdateStatus.verifing,
  reject: QcUpdateStatus.reject,
  cancel: QcUpdateStatus.verified,
  success: QcUpdateStatus.verified,
};

const stepItems = [
  {
    title: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.submit', defaultMessage: 'Submitted new document' }),
    // description: '2024/01/01 13:12:12',
  },
  {
    title: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.scanning', defaultMessage: 'Security scanning' }),
    // description: '2024/01/04 13:12:12',
  },
  {
    title: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.screening', defaultMessage: 'Document screening' }),
    description: i18n.formatMessage({
      id: 'asc.setting.antd.accountNodes.screening.passCostDays',
      defaultMessage: '3-5 Days',
    }),
  },
  {
    title: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.verified', defaultMessage: 'Verified and live' }),
    description: i18n.formatMessage({
      id: 'asc.setting.antd.accountNodes.verified.passCostDays',
      defaultMessage: '1-5 Days',
    }),
  },
];

// const mockQcInfo = {
//   qcInfo: {
//     globalNoticeSubmitTime: *************,
//     globalNoticeUpdateTime: *************,
//     qcComment: 'It may take 5-7 working days to verify your seller info, please wait patiently.',
//     qcStatus: 'pending',
//   },
// };

const BusinessProgress = () => {
  const { data } = useRequest(
    () => {
      return axios.get('mtop.global.merchant.subaccount.profile.render.lazada', {
        params: { groupId: 2 },
      });
    },
    { cacheKey: 'business-progress' },
  );
  const qcInfo = data?.data?.data?.qcInfo || {};
  // const qcInfo = mockQcInfo.qcInfo;

  const transformQcInfo = (qcInfo: GetSellerProfileResponse['qcInfo'] | undefined) => {
    if (!qcInfo) return {};

    return {
      status: statusMap[qcInfo.qcStatus as keyof typeof statusMap],
      updatedAt: dayjs(qcInfo.globalNoticeUpdateTime).format('YYYY/MM/DD HH:mm:ss'),
      submitAt: dayjs(qcInfo.globalNoticeSubmitTime).format('YYYY/MM/DD HH:mm:ss'),
      comment: qcInfo.qcComment,
    };
  };

  const transformStepItems = (qcInfo: GetSellerProfileResponse['qcInfo'] | undefined) => {
    if (!qcInfo) return [];
  };

  const { status, updatedAt, submitAt, comment } = useMemo(() => transformQcInfo(qcInfo), [qcInfo]);

  return (
    <CollapsePanel
      defaultExpanded={true}
      title={
        <div className="collapse-panel-title">
          <span>Review Progress</span>
          {status && (
            <span
              className="collapse-panel-title-status"
              style={{
                color: statusTextMap[status].color,
                backgroundColor: statusTextMap[status].bg,
              }}
            >
              {statusTextMap[status].text}
            </span>
          )}
          <span className="collapse-panel-title-updated">
            {i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.updatedIn', defaultMessage: 'Updated on' })}{' '}
            {updatedAt}
          </span>
        </div>
      }
    >
      <div className="collapse-content">
        {status && [QcUpdateStatus.reject, QcUpdateStatus.verifing].includes(status) && comment && (
          <Message className="collapse-panel-message" type={status === QcUpdateStatus.reject ? 'error' : 'notice'}>
            <span>{comment}</span>
          </Message>
        )}
        <Card className="collapse-panel-card">
          <Step items={stepItems} current={2} errorStep={3} />
        </Card>
      </div>
    </CollapsePanel>
  );
};

export default BusinessProgress;
