import React from 'react';
import { Icon, Message } from '@alifd/next';
import * as i18n from '@alife/lazada-i18n';
import { useRequest } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import './item-status.scss';

export enum EStatus {
  reject = 'reject',
  verifing = 'verifing',
  verified = 'verified',
}

export function fetchMerchantAccountInfo({ groupId }: { groupId: string }) {
  return axios
    .get('mtop.global.merchant.subaccount.profile.render.lazada', {
      url: 'mtop.global.merchant.subaccount.profile.render.lazada',
      method: 'post',
      params: {
        groupId,
      },
    })
    .then(res => {
      const statusMap = {
        pending: EStatus.verifing,
        reject: EStatus.reject,
        cancel: EStatus.verified,
        success: EStatus.verified,
      };

      return {
        status: res?.data?.data?.qcInfo.qcStatus && statusMap[res?.data?.data?.qcInfo.qcStatus],
        updateAt: res.data.data?.qcInfo?.globalNoticeUpdateTime
          ? dayjs(res.data.data?.qcInfo?.globalNoticeUpdateTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
        submitAt: res.data.data?.qcInfo?.globalNoticeSubmitTime
          ? dayjs(res.data.data?.qcInfo?.globalNoticeSubmitTime).format('YYYY-MM-DD HH:mm:ss')
          : '',
        notice: res.data.data?.qcInfo?.qcComment || res.data.data?.globalNotice?.content,
      };
    });
}

const DetailText = i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.detail', defaultMessage: 'Detail' });

const statusMap = {
  reject: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.reject', defaultMessage: 'Rejected' }),
    color: '#E72B00',
    bg: '#FFEBE7',
  },
  verifing: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.verifing', defaultMessage: 'Verifing' }),
    color: '#F9AC2A',
    bg: '#FEF7EA',
  },
  verified: {
    text: i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.verified', defaultMessage: 'Verified' }),
    color: '#41A716',
    bg: '#E3F8DA',
  },
};

export enum EBizType {
  business = '2',
  bank = '5',
  storeProfile = 'storeProfile',
  sellerProfile = '1',
}

interface IProps {
  label: string;
  icon: string;
}

const nodes = [
  {
    label: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.submit', defaultMessage: 'Submitted new document' }),
    key: 'submit',
  },
  {
    label: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.scanning', defaultMessage: 'Security scanning' }),
    key: 'scanning',
  },
  {
    label: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.screening', defaultMessage: 'Document screening' }),
    passCostDays: i18n.formatMessage({
      id: 'asc.setting.antd.accountNodes.screening.passCostDays',
      defaultMessage: '3-5 Days',
    }),
    key: 'screening',
  },
  {
    label: i18n.formatMessage({ id: 'asc.setting.antd.accountNodes.verified', defaultMessage: 'Verified and live' }),
    passCostDays: i18n.formatMessage({
      id: 'asc.setting.antd.accountNodes.verified.passCostDays',
      defaultMessage: '1-5 Days',
    }),
    key: 'verified',
  },
];

const statusIconMap = {
  [EStatus.reject]: 'https://img.alicdn.com/imgextra/i2/O1CN01S6LQxr1xflzwcOnjW_!!*************-55-tps-36-36.svg',
  [EStatus.verified]: 'https://gw.alicdn.com/imgextra/i2/O1CN01JVR52P1GtwmGHLmEx_!!*************-55-tps-36-36.svg',
};

export default function ItemStatus({ label, icon }: IProps) {
  const [openDetail, setOpenDetail] = useState(false);

  const { data } = useRequest(fetchMerchantAccountInfo, {
    defaultParams: [{ groupId: '2' }],
  });

  const serviceResponseStatus = data?.status;

  const status = serviceResponseStatus || EStatus.verified;

  const updateAt = data?.updateAt || '';

  const submitAt = data?.submitAt || '';

  const notice = data?.notice || '';

  useEffect(() => {
    setOpenDetail(!(status === EStatus.verified));
  }, [status]);

  const submitUrl = '/apps/setting/business'

  return (
    <div className={classNames('container service-hub-account')}>
      <div className="header">
        <div className="left-section">
          <img src={icon} alt="account status icon" />
          <span className="label">{label}</span>
          {serviceResponseStatus && (
            <span
              className="status-badge"
              style={{ color: statusMap[status].color, backgroundColor: statusMap[status].bg }}
            >
              {statusMap[status].text}
            </span>
          )}
          {updateAt && (
            <span className="update-time">
              {i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.updatedIn', defaultMessage: 'Updated on' })}{' '}
              {updateAt}
            </span>
          )}
        </div>
        <div className="right-section">
          {status === EStatus.verified && (
            <a
              href={submitUrl}
              data-spm={`d_detail_2`}
              className="aplus-auto-exp"
              target="_blank"
              rel="noreferrer"
            >
              {DetailText}
            </a>
          )}
          <Icon
            type="arrow-down"
            className={classNames(
              'ml-4px aplus-auto-exp',
              { 'rotate-180': openDetail },
              { 'rotate-270': !openDetail && status === EStatus.verified },
            )}
            data-spm={`d_expand_2`}
            onClick={() => {
              setOpenDetail(!openDetail);
            }}
            onKeyUp={() => {}}
          />
        </div>
      </div>
      {status && [EStatus.reject, EStatus.verifing].includes(status) && notice && openDetail && (
        <Message type={status === EStatus.reject ? 'error' : 'notice'} className="notice-message" closeable>
          {notice}&nbsp;
          <a
            href={submitUrl}
            data-spm="d_detail_resubmit"
            data-more={submitUrl}
            className="aplus-auto-exp"
            target="_blank"
            rel="noreferrer"
          >
            {status === EStatus.reject
              ? i18n.formatMessage({ id: 'asc.setting.antd.accountstatus.submitNew', defaultMessage: 'Resubmit' })
              : DetailText}
          </a>
        </Message>
      )}
      <div className="detail-content" style={openDetail ? { height: 'auto', marginTop: 16 } : { height: '0' }}>
        {nodes.map((node, index) => (
          <div key={node.key} className="item-node">
            <div className={classNames('item-status', [0, 1].includes(index) ? EStatus.verified : status)}>
              {[0, 1].includes(index) || !status ? (
                <img
                  src={'https://img.alicdn.com/imgextra/i2/O1CN01JVR52P1GtwmGHLmEx_!!*************-55-tps-36-36.svg'}
                  alt="success icon"
                />
              ) : ([EStatus.reject, EStatus.verified].includes(status) && [2].includes(index)) ||
                ([EStatus.verified].includes(status) && [3].includes(index)) ? (
                <img src={statusIconMap[status]} alt="status icon" />
              ) : (
                <div className="number-icon">{index + 1}</div>
              )}
            </div>
            <div className="node-label">{node.label}</div>
            <div className="node-time">
              {[0, 1].includes(index)
                ? submitAt
                : status === EStatus.verifing
                ? node.passCostDays
                : [3].includes(index) && status === EStatus.reject
                ? node.passCostDays
                : updateAt}
            </div>
            {status === EStatus.reject && [2].includes(index) && (
              <a
                href={submitUrl}
                data-spm="d_detail_submit"
                data-more={submitUrl}
                target="_blank"
                rel="noreferrer"
                className="resubmit-link aplus-auto-exp"
              >
                {DetailText}
              </a>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
