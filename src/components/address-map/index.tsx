import * as React from 'react';
import { Component } from 'react';
import * as i18n from '@alife/lazada-i18n';
import { ASCTools } from '@alife/workstation-utils';
import AMapLoader from '@amap/amap-jsapi-loader';
import Locale from '../../constants/common-locale';
import { Box, Icon } from '@alifd/next';
import './index.scss';

window['_AMapSecurityConfig'] = {
  securityJsCode: '9b2b2dbb4a79f35702cb52b30dabd207',
};

interface IProps {
  center?: Array<number>[2];
  vectorMapForeign?: string;
  zoom?: number;
  setMap?: (v) => void;
  mapClickInfo?: (v) => void;
  searchLocation?: () => void;
}

const countryPos = {
  ID: [106.847504, -6.207407],
  SG: [103.847696, 1.336408],
  MY: [101.693536, 3.148649],
  PH: [120.98614, 14.564857],
  TH: [100.491331, 13.752435],
  VN: [106.665329, 10.755339],
};

class MapComponent extends Component<IProps> {
  map: any = null;
  marker: any = null;

  componentDidMount() {
    const { center, vectorMapForeign, zoom, setMap } = this.props;
    setMap && setMap(this);

    const initPos = center || countryPos[ASCTools.getCountry() || 'SG'];

    AMapLoader.load({
      key: 'c47eba62eab11abb6ff9e65d70665b81', // 21475ebead1c9b728a4f10f64a67b7bf
      version: '1.4.15',
      plugins: ['AMap.Geocoder', 'AMap.PlaceSearch', 'AMap.Geolocation', 'AMap.ToolBar'],
    })
      .then(AMap => {
        this.map = new AMap.Map('container', {
          // viewMode:"3D",
          resizeEnable: true,

          zoom: zoom || 16,
          center: initPos,
          vectorMapForeign: vectorMapForeign || 'style_lazada',
          overseaDataType: 'mapbox',
        });

        this.bindEvent();
        // Default marker
        ASCTools.getCountry() !== 'SG' &&
          this.createMarker(
            i18n.formatMessage({
              id: 'page.warehouse.address.edit.map.drag',
              defaultMessage: 'Click On Map To Pin',
            }),
            '',
            initPos,
            false,
          );
      })
      .catch(e => {
        console.log(e);
      });
  }

  bindEvent() {
    this.map.on('click', e => this.showInfoClick(e, this.props));
  }

  showInfoClick(e, props) {
    const { mapClickInfo } = props;
    mapClickInfo && mapClickInfo([e?.lnglat?.getLng(), e?.lnglat?.getLat()]);
  }

  setCenter(pos) {
    Array.isArray(pos) && this.map?.setCenter(pos);
  }

  // 定位
  getLocation = () => {
    return new Promise((resolve, reject) => {
      const AMap = window['AMap'];
      if (!AMap) reject('AMap not define!');
      const location = new AMap.Geolocation({
        enableHighAccuracy: true, //是否使用高精度定位，默认:true
        timeout: 10000, //超过10秒后停止定位，默认：无穷大
        maximumAge: 0, //定位结果缓存0毫秒，默认：0
        // noIpLocate: 2, // PC禁止IP定位
        GeoLocationFirst: true,
        convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
        showButton: false, //显示定位按钮，默认：true
        showMarker: true, //定位成功后在定位到的位置显示点标记，默认：true
        showCircle: true, //定位成功后用圆圈表示定位精度范围，默认：true
        panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
        zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
      });
      location?.getCurrentPosition((status, result) => {
        if (status === 'complete') {
          const pos = [result?.position?.lng || 0, result?.position?.lat || 0];
          resolve(pos);
        } else {
          reject(status);
        }
      });
    });
  };
  /**
   * Search location by address  使用菜鸟搜索 已废弃
   * @param cityCode 城市编码
   * @param address 详细地址
   */
  searchLongLat(cityCode, address) {
    return new Promise((resolve, reject) => {
      const AMap = window['AMap'];
      if (!AMap) reject('AMap not define!');
      // 地理编码与逆地理编码 getLocation getAddress
      const geocoder = new AMap.Geocoder({
        city: cityCode || '010', //城市设为北京，默认：“全国”
      });
      // error INVALID_USER_SCODE
      geocoder.getLocation(address || '天安门', function(status, result) {
        if (status !== 'complete') reject(result);
        const geoCode = (result?.geocodes || [])[0];
        const pos = [geoCode?.location?.lng, geoCode?.location?.lat];
        resolve(pos);
      });
    });
  }

  /**
   * Get address from Long Lat  使用菜鸟搜索 已废弃
   * @param pos 经纬度  Long Lat
   */
  searchAddress = (pos: Array<number>) => {
    return new Promise((resolve, reject) => {
      const AMap = window['AMap'];
      if (!AMap) reject('AMap not define!');
      // 地理编码与逆地理编码 getLocation getAddress
      const geocoder = new AMap.Geocoder({});
      geocoder.getAddress(pos, (status, result) => {
        if (status !== 'complete') reject(result);
        const address = result?.regeocode?.formattedAddress;
        const addressComponent = result?.regeocode?.addressComponent; // city  cityCode district province street township streetNumber
        resolve({ address, addressComponent });
      });
    });
  };

  zoomIn() {
    const level = this.map?.getZoom();
    if (level >= 18) return;
    this.map?.setZoom(level + 1);
  }

  zoomOut() {
    const level = this.map?.getZoom();
    if (level <= 3) return;
    this.map?.setZoom(level - 1);
  }

  createMarker(title = '', content = '', pos, showButton = true) {
    const AMap = window['AMap'];
    if (!AMap) return;
    if (!pos) {
      const center = this.map?.getCenter();
      pos = [center?.lng, center?.lat];
    }
    this.marker && this.removeMarker();
    var markerContent =
      '<div class="custom-content-marker">' +
      '<div class="marker-content">' +
      '<div class="marker-title">' +
      title +
      '</div>' +
      content +
      (!!showButton
        ? '<div class="confirm-button"><a id="warehouse-map-click-confirm" class="aplus-auto-exp map-button" data-spm="d_map_confirm">' +
          Locale['page.common.btn.confirm'] +
          '</a></div>'
        : '') +
      '</div>' +
      '<div class="line"></div>' +
      '</div>';
    var position = new AMap.LngLat(pos[0], pos[1]);
    this.marker = new AMap.Marker({
      position: position,
      // 将 html 传给 content
      content: markerContent,
      // 以 icon 的 [center bottom] 为原点
      offset: new AMap.Pixel(0, 0),
      anchor: 'bottom-center',
    });
    this.map?.add(this.marker);
  }

  removeMarker() {
    this.map?.remove(this.marker);
    this.marker = null;
  }

  render() {
    const { searchLocation } = this.props;
    // 1.初始化创建地图容器,div标签作为地图容器，同时为该div指定id属性；
    return (
      <div id="container" className="asc-seller-amap-wrap">
        <Box direction="column" className="controls-bar">
          <a
            className="location aplus-auto-exp"
            data-spm="d_warehouse_edit_location"
            onClick={() => searchLocation && searchLocation()}
          >
            <img src="//img.alicdn.com/imgextra/i4/O1CN01Hw0dKo1SD8qDkpxEy_!!6000000002212-2-tps-147-165.png" />
          </a>
          <Icon className="aplus-auto-exp" data-spm="d_warehouse_edit_add" type="add" onClick={() => this.zoomIn()} />
          <Icon
            className="aplus-auto-exp"
            data-spm="d_warehouse_edit_minusa"
            type="minus"
            onClick={() => this.zoomOut()}
          />
        </Box>
      </div>
    );
  }
}
//导出地图组建类
export default MapComponent;
