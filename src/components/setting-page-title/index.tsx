import React from 'react';
import commonLocale from '../../constants/common-locale';
import { PageTitle } from '@alife/asc-components';
import { Button } from '@alifd/next';

const SettingPageTitle = ({ title = '', ...rest }) => {
  const settingLabel = commonLocale['page.common.nav.setting'];
  const settingLink = '/apps/setting/index';
  const breadcrumb: any[] = [{ link: '/', label: commonLocale['page.common.nav.home'] }];
  const pageTitle = title || settingLabel;
  if (title) {
    breadcrumb.push({
      link: settingLink,
      label: settingLabel,
    });
    breadcrumb.push({
      label: title,
    });
  } else {
    breadcrumb.push({
      label: settingLabel,
    });
  }
  return <PageTitle breadcrumb={breadcrumb} title={pageTitle} {...rest} />;
};

export default SettingPageTitle;
