import * as i18n from '@alife/lazada-i18n';
import React from 'react';
import { ShapeAttrs } from '@antv/g-base/lib/types';
import { wrapBizChart } from './bizcharts';
import { useCreation } from 'ahooks';
// import * as locale from "./locale";
interface IInstrumentPanelProps {
  data?: number;
}

const scale = {
  value: {
    min: 0,
    max: 1,
    ticks: [0.2, 0.5, 0.8],
    formatter: v => {
      return `${(v * 100).toFixed(0)}%`;
    },
  },
};

export const InstrumentPanel = wrapBizChart((props: IInstrumentPanelProps & { App: any }) => {
  const { App } = props;

  const { Annotation, Axis, Chart, Coordinate, G2, Point } = App;
  useCreation(() => {
    const { registerShape } = G2;

    // 自定义Shape 部分
    registerShape('point', 'gauge-indicator2', {
      draw(cfg, container) {
        const group = container.addGroup();

        const center = this.parsePoint({ x: 0, y: 0 }); // 获取极坐标系下画布中心点

        // 绘制指针
        group.addShape('line', {
          attrs: {
            x1: center.x,
            y1: center.y,
            x2: cfg.x,
            y2: cfg.y,
            stroke: '#4F5669',
            lineWidth: 3,
            lineCap: 'round',
          },
        });

        group.addShape('circle', {
          attrs: {
            x: center.x,
            y: center.y,
            r: 5,
            stroke: '#4F5669',
            lineWidth: 3,
            fill: '#fff',
          },
        });

        return group;
      },
    });
  }, []);

  // const { data: donutData } = props;

  const isEmpty = props.data === undefined;

  const data = [{ value: +(props.data ?? 0) }];

  const content = data => {
    const contentMap = {
      poor: {
        content: i18n.formatMessage({
          id: 'page.product.assortment.poor',
          defaultMessage: 'Need Improve',
        }),
        color: '#565658',
      },
      medium: {
        content: i18n.formatMessage({
          id: 'page.product.assortment.medium',
          defaultMessage: 'Medium',
        }),
        color: '#FAAD14',
      },
      strong: {
        content: i18n.formatMessage({
          id: 'page.product.assortment.strong',
          defaultMessage: 'Strong',
        }),
        color: '#30BF78',
      },
    };
    if (data < 0.3 || !data) {
      return contentMap.poor;
    } else if (data > 0.3 && data < 0.6) {
      return contentMap.medium;
    } else {
      return contentMap.strong;
    }
  };
  return (
    <>
      <Chart data={data} padding={[0, 10, 20, 10]} scale={scale} width={320} height={160}>
        <Coordinate type="polar" radius={0.8} startAngle={(-12 / 10) * Math.PI} endAngle={(2 / 10) * Math.PI} />

        <Axis name="1" />
        <Axis
          name="value"
          line={null}
          tickLine={{
            length: -5,
          }}
          label={{
            offset: -8,
            style: (label, v, arr) => {
              const result: ShapeAttrs = {
                fontSize: 12,
                textAlign: 'center',
                textBaseline: 'middle',
              };

              if (v < 1) {
                result.textAlign = 'start';
              } else if (v > 1) {
                result.textAlign = 'end';
              } else {
                result.textBaseline = 'top';
              }

              return result;
            },
          }}
          grid={null}
        />

        <Annotation.Text position={[0, 1]} style={{ fontSize: 10, fill: '#565658', textAlign: 'end' }} offsetX={-24} />

        <Annotation.Text
          position={[1, 1]}
          style={{ fontSize: 10, fill: '#565658', textAlign: 'start' }}
          offsetX={24}
          // content={'Strong'}
        />

        <Annotation.Text
          position={[0, 0]}
          style={{ fontSize: 15, fill: '#000', textAlign: 'center' }}
          offsetY={35}
          content={(value: any[]) => {
            if (isEmpty) {
              return '--';
            }
            const v = value[0].value;
            return `${(v * 100).toFixed(0)}%`;
          }}
        />
        <Annotation.Text
          position={[0, 0]}
          style={{ fill: content(props.data).color, fontSize: 14, textAlign: 'center' }}
          offsetY={62}
          content={content(props.data).content}
        />

        <Point position="value*1" color="#1890FF" shape="gauge-indicator2" />
        <Annotation.Arc
          start={[0, 1.1]}
          end={[1, 1.1]}
          style={{
            stroke: 'l(0) 0:#fb3f01 0.5:#FAAD14 1:#30BF78',
            lineWidth: 15,
            lineDash: null,
          }}
        />
      </Chart>
    </>
  );
});
