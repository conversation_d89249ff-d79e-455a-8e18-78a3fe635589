import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Select, Box } from '@alifd/next';
import { useMemoizedFn } from 'ahooks';
import { AddressSelector } from '@alife/asc-components';

const regions = [
  {
    label: 'Afghanistan',
    value: 'AF',
  },

  {
    label: 'Albania',
    value: 'AL',
  },

  {
    label: 'Algeria',
    value: 'DZ',
  },

  {
    label: 'Andorra',
    value: 'AD',
  },

  {
    label: 'Angola',
    value: 'AO',
  },

  {
    label: 'Antigua and Barbuda',
    value: 'AG',
  },

  {
    label: 'Argentina',
    value: 'AR',
  },

  {
    label: 'Armenia',
    value: 'AM',
  },

  {
    label: 'Australia',
    value: 'AU',
  },

  {
    label: 'Austria',
    value: 'AT',
  },

  {
    label: 'Bahrain',
    value: 'BH',
  },

  {
    label: 'Bangladesh',
    value: 'BD',
  },

  {
    label: 'Brazil',
    value: 'BR',
  },

  {
    label: 'Brunei',
    value: 'BN',
  },

  {
    label: 'Cambodia',
    value: 'KH',
  },

  {
    label: 'Canada',
    value: 'CA',
  },

  {
    label: 'Chile',
    value: 'CL',
  },

  {
    label: 'China',
    value: 'CN',
  },

  {
    label: 'Congo - Kinshasa',
    value: 'CD',
  },

  {
    label: 'France',
    value: 'FR',
  },

  {
    label: 'Germany',
    value: 'DE',
  },

  {
    label: 'Hong Kong',
    value: 'HK',
  },

  {
    label: 'Hungary',
    value: 'HU',
  },

  {
    label: 'India',
    value: 'IN',
  },

  {
    label: 'Indonesia',
    value: 'ID',
  },

  {
    label: 'Ireland',
    value: 'IE',
  },

  {
    label: 'Israel',
    value: 'IL',
  },

  {
    label: 'Italy',
    value: 'IT',
  },

  {
    label: 'Japan',
    value: 'JP',
  },

  {
    label: 'Lithuania',
    value: 'LT',
  },

  {
    label: 'Macau SAR China',
    value: 'MO',
  },

  {
    label: 'Malaysia',
    value: 'MY',
  },

  {
    label: 'Netherlands',
    value: 'NL',
  },

  {
    label: 'New Zealand',
    value: 'NZ',
  },

  {
    label: 'Norway',
    value: 'NO',
  },

  {
    label: 'Philippines',
    value: 'PH',
  },

  {
    label: 'Singapore',
    value: 'SG',
  },

  {
    label: 'South Korea',
    value: 'KR',
  },

  {
    label: 'Spain',
    value: 'ES',
  },

  {
    label: 'Sweden',
    value: 'SE',
  },

  {
    label: 'Taiwan',
    value: 'TW',
  },

  {
    label: 'Turkey',
    value: 'TR',
  },

  {
    label: 'Turkmenistan',
    value: 'TM',
  },

  {
    label: 'United Arab Emirates',
    value: 'AE',
  },

  {
    label: 'United Kingdom',
    value: 'GB',
  },

  {
    label: 'United States',
    value: 'US',
  },

  {
    label: 'Vietnam',
    value: 'VN',
  },

  {
    label: 'Thailand',
    value: 'TH',
  },
];

interface AddressValue {
  venture?: string;
  locationLevel1?: string;
  locationLevel2?: string;
  locationLevel3?: string;
}

interface IProps {
  onChange?: Function;
  isCrossBoard?: boolean;
  value?: AddressValue;
}

const isSea = (venture?: string) => {
  if (!venture) {
    return false;
  }
  return ['SG', 'MY', 'TH', 'VN', 'ID', 'PH'].includes(venture);
};

const calculateAddressValue = (value?: AddressValue) => {
  if (!value) {
    return '';
  }
  for (let i = 3; i > 0; i -= 1) {
    const current = value[`locationLevel${i}`];
    if (current) {
      return current;
    }
  }
};

/**
 * Only show Region Selector for CrossBoard Account
 * When the Region is one of SEA venture, show the Address Tree Selector
 */
const CrossBoardRegion = (props: IProps) => {
  const onRegionChange = useMemoizedFn((v: string) => {
    const newValue: AddressValue = {
      venture: v,
      locationLevel1: '',
      locationLevel2: '',
      locationLevel3: '',
    };

    if (props.onChange) {
      props.onChange(newValue);
    }
  });

  const onAddressChange = useMemoizedFn((v, d, extra) => {
    // console.log(extra);
    const newValue: AddressValue = { ...props.value };
    for (let i = 1; i < 4; i += 1) {
      const current = extra?.selectedPath[i - 1];
      if (current) {
        newValue[`locationLevel${i}`] = current.value;
      } else {
        newValue[`locationLevel${i}`] = '';
      }
    }
    if (props.onChange) {
      props.onChange(newValue);
    }
  });

  // console.log('---- render -----');
  // console.log(props.value);

  return (
    <Box direction="row">
      {props.isCrossBoard ? (
        <Select
          showSearch
          value={props.value?.venture}
          onChange={onRegionChange}
          placeholder="Country/Region"
          dataSource={regions}
          className="mr5"
        />
      ) : null}
      {isSea(props.value?.venture) ? (
        <AddressSelector
          value={calculateAddressValue(props.value)}
          iso={props.value?.venture}
          onChange={onAddressChange}
        />
      ) : null}
    </Box>
  );
};

export default CrossBoardRegion;
