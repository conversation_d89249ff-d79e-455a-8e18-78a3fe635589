import * as i18n from '@alife/lazada-i18n';
import React, { useState, useCallback } from 'react';
import { useRequest, useMemoizedFn } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import { CascaderSelect } from '@alifd/next';
import { CopyText } from '@alife/asc-components';

import './index.scss';

// interface SelectorItem {
//   label: string;
//   value: string | number;
// }

interface IBankServiceParams {
  bankName?: string;
}

const getBankErrorMessage = i18n.formatMessage({
  id: 'page.account.setting.getBankError',
  defaultMessage: 'Get Bank Error',
});

/**
 * @description ********改动，valueRender没什么用，还有很复杂的bug，改成组件自动识别value，然后在初始化渲染时候
 * 根据props.value里的参数，对selectorValue进行赋值即可。
 */
const getBanksService = (params?: IBankServiceParams): Promise<any> =>
  axios.get('mtop.global.merchant.subaccount.bank.branches.lazada', { params }).then(({ data: res }) => res.data);

const BankSelector = props => {
  const [dataSource, setDataSource] = useState([]);

  // console.log(props);
  const loadChildren = useMemoizedFn(selected => {
    // console.log(selected);
    return getBanksService({ bankName: selected.value }).then(banks => {
      if (!banks?.length) {
        throw new Error(getBankErrorMessage);
      }
      const newDataSource = [...dataSource];
      for (const item of newDataSource) {
        if (item.value === selected.value) {
          // there on 2 level of bank
          item.children = banks.map(bankItem => {
            bankItem.isLeaf = true;
            return bankItem;
          });
          break;
        }
      }

      setDataSource(newDataSource);
    });
  });

  const { error, run } = useRequest(
    () =>
      getBanksService()
        .then(banks => {
          if (!banks?.length) {
            throw new Error(getBankErrorMessage);
          }
          setDataSource(banks);

          if (props.value?.branch) {
            loadChildren({ value: props.value?.branch }).catch(e => {
              // ignore error
              console.error(e);
            });
          }
          return banks;
        })
        .catch(e => {
          console.error(e);
        }),
    {},
  );

  const onChange = (_value, _data, extra) => {
    // console.log('onchange: ', extra.selectedPath);

    let value;
    if (extra?.selectedPath?.length > 1) {
      value = {
        bank: extra.selectedPath[0].value,
        branch: extra.selectedPath[1].value,
        swift: extra.selectedPath[1].swift,
        bankCode: extra.selectedPath[1].bankCode,
        bankId: extra.selectedPath[1]?.bankMdmId,
      };
    } else if (extra?.selectedPath?.length > 0) {
      value = {
        bank: extra.selectedPath[0].value,
        branch: '',
        swift: extra.selectedPath[0].swift,
        bankCode: extra.selectedPath[0].bankCode,
        bankId: extra.selectedPath[0]?.bankMdmId,
      };
    }

    if (value) {
      props.onChange(value);
    }
  };

  // const selectorValue: string[] = [];

  let selectorValue;
  if (props.value?.bank) {
    // selectorValue.push(props.value.bank);
    if (props.value.branch) {
      // selectorValue.push(props.value.branch);
      selectorValue = `${props.value.bank} / ${props.value.branch}`;
    } else {
      selectorValue = props.value.bank;
    }
  }

  // console.log('selectorValue', selectorValue);
  const valueRender = useMemoizedFn(item => {
    // if it's the first time, we can only get the value from server side
    console.log(item);
    let label = [];
    let subList;

    // generate default label by value or label
    let defaultLabel = item.label;
    if (!defaultLabel) {
      if (!props.value?.bank) {
        return '';
      }
      defaultLabel = props.value?.bank;
      if (props.value?.branch) {
        defaultLabel = `${defaultLabel} / ${props.value?.branch}`;
      }
    }
    // dataSource not ready
    if (!dataSource.length) {
      return defaultLabel;
    }

    for (const bank of dataSource) {
      if (bank.value === props.value?.bank) {
        label.push(bank.label);
        subList = bank.children;
        break;
      }
    }

    // only one level
    if (!props.value?.branch) {
      return label[0];
    }

    // children not ready
    if (!subList) {
      return defaultLabel;
    }

    for (const bank of subList) {
      if (bank.value === props.value?.branch) {
        label.push(bank.label);
        break;
      }
    }

    return label.join(' / ');
  });

  return (
    <div>
      {error ? (
        <span className="bank-retry-button" onClick={run}>
          {i18n.formatMessage({
            id: 'page.account.setting.loadBanksErrorClickTo',
            defaultMessage: 'Load Banks Error, Click to retry!',
          })}
        </span>
      ) : (
        <CascaderSelect
          readOnly={props.readOnly}
          disabled={props.disabled}
          // valueRender={valueRender}
          value={selectorValue}
          onChange={onChange}
          dataSource={dataSource}
          loadData={loadChildren}
        />
      )}
      <div>
        {props.value?.bankCode ? (
          <CopyText
            data-spm="d_bank_code_copy"
            className="bank-extra-info aplus-auto-exp"
            value={props.value?.bankCode}
          >
            <span className="bank-extra-label">
              {i18n.formatMessage({ id: 'page.account.setting.bankCode', defaultMessage: 'Bank Code' })}:{' '}
            </span>
            {props.value?.bankCode}
          </CopyText>
        ) : null}
        {props.value?.swift ? (
          <CopyText data-spm="d_swift_copy" className="bank-extra-info aplus-auto-exp" value={props.value?.swift}>
            <span className="bank-extra-label">
              {i18n.formatMessage({ id: 'page.account.setting.swift', defaultMessage: 'Swift' })}:{' '}
            </span>
            {props.value?.swift}
          </CopyText>
        ) : null}
      </div>
    </div>
  );
};

export default BankSelector;
