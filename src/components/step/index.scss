.steps-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  font-family: sans-serif;
  width: 100%;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;

  .step-icon-container {
    margin-bottom: 8px;
    z-index: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .step-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #999;
    border: 2px solid #ccc;
    background-color: #fff;
  }
  
  .check-icon {
    width: 24px;
    height: 24px;
    color: #fff;
  }

  .step-content {
    .step-title {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .step-description {
      font-size: 12px;
      color: #999;
    }
  }

  &.completed {
    .step-icon {
      background-color: #16a747;
      border-color: #16a747;
      color: #fff;

      .next-icon {
        color: #fff;
        font-size: 20px;
      }
    }
    .step-title {
      color: #333;
    }
  }
  
  &.error {
    .step-icon {
      background-color: #e72b00;
      border-color: #e72b00;
      color: #fff;

      .next-icon {
        color: #fff;
        font-size: 20px;
      }
    }
    .step-title {
      color: #e72b00;
      font-weight: bold;
    }
  }

  &.in_progress {
    .step-icon {
      border-color: #16a747;
      color: #16a747;
    }
    .step-title {
      color: #333;
      font-weight: bold;
    }
  }
}

.css-error-icon {
  width: 100%;
  height: 100%;
  position: relative;
}

.css-error-icon::before,
.css-error-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: var(--icon-thickness);
  background-color: #fff;
  border-radius: 2px;
}

.css-error-icon::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.css-error-icon::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.step-connector {
    flex: 1;
    height: 4px;
    border-radius: 4px;
    background-color: #ccc;
    margin: 0 -20px;
    position: relative;
    top: 15px;

  &.completed {
    background-color: #28a745;
  }
} 