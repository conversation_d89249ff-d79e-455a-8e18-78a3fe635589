import React from 'react';
import { Icon } from '@alifd/next';
import { Switch, Case, Default } from '@alife/workstation-utils';
import './index.scss';

interface StepItem {
  title: string;
  description: string;
  key?: string;
  passCostDays?: string;
  status?: 'completed' | 'in_progress' | 'pending' | 'error';
}

interface StepProps {
  items: StepItem[];
}

const ErrorIcon = ({ thickness = '3px' }) => (
  <div className="css-error-icon" style={{ '--icon-thickness': thickness } as React.CSSProperties} />
);

const Step: React.FC<StepProps> = ({ items }) => {
  return (
    <div className="steps-container">
      {items.map((item, index) => {
        const status = item.status || 'pending';
        const isLastItem = index === items.length - 1;

        return (
          <React.Fragment key={item.key || index}>
            <div className={`step-item ${status}`}>
              <div className="step-icon-container">
                <div className="step-icon">
                  <Switch>
                    <Case when={status === 'completed'}>
                      <Icon type="select" />
                    </Case>
                    <Case when={status === 'error'}>
                      <ErrorIcon />
                    </Case>
                    <Default>
                      <span>{index + 1}</span>
                    </Default>
                  </Switch>
                </div>
              </div>
              <div className="step-content">
                <div className="step-title">{item.title}</div>
                <div className="step-description">{item.description}</div>
              </div>
            </div>
            {!isLastItem && <div className={`step-connector ${status === 'completed' ? 'completed' : ''}`} />}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default Step;
