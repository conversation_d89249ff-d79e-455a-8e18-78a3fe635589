export const timeFormat = (data, type = 'yyyy/mm/dd hh:ii:ss') => {
  let format = type ? type : 'yyyy-mm-dd';
  let date = data ? new Date(data) : new Date();
  let o = {
    'm+': date.getMonth() + 1, //月份
    'd+': date.getDate(), //日
    'h+': date.getHours(), //小时
    'i+': date.getMinutes(), //分
    's+': date.getSeconds(), //秒
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substring(('' + o[k]).length));
    }
  }
  return format;
};
