import { Message } from '@alifd/next';

const wait = (time = 0) => new Promise(resolve => setTimeout(resolve, time));
/**
 * 通过Formily 适配Actions
 */
export function createFormActions() {
  const dadaActions = {
    toast(data) {
      const duration = data.duration || 1000;
      return new Promise(resolve => {
        Message.show({
          type: data.type || 'success',
          content: data.content || data.value || 'Success', // 新结构返回的是 content， 老结构是 value
          duration: duration,
          afterClose: () => {
            resolve(true);
          },
          onClose: () => resolve(true),
        });
      });
    },
    openUrl(data) {
      const target = data.target || '_blank';
      return wait(data.duration || 500).then(() => {
        const href = data.href || location.href;
        if (target === '_blank') {
          window.open(href);
        } else {
          if (location.href === href) {
            location.reload();
          } else {
            location.href = href;
          }
        }
        return Promise.resolve();
      });
    },
    redirect(data) {
      if (data.content) {
        Message.success({
          title: data.content,
        });
      }
      return wait(data.duration || 500).then(() => {
        const href = data.href || location.href;
        location.href = href;
        return Promise.resolve();
      });
    },
    goBack(data) {
      if (history.length > 1) {
        history.back();
      } else {
        location.href = data.href || '/';
      }
    },
  };

  const runSingle = dadaActionData => {
    const { eventType, ...restProps } = dadaActionData;
    return dadaActions[eventType] && dadaActions[eventType](restProps);
  };

  return {
    async runDadaAction(actions) {
      if (Array.isArray(actions.data)) {
        for (const action of actions.data) {
          await runSingle(action);
        }
      } else {
        await runSingle(actions.data);
      }
    },
  };
}
