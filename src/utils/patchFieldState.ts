import pick from 'lodash/pick';
import omit from 'lodash/omit';
import { isPlainObj } from '@formily/shared';
import { Field, IFieldStateSetter } from '@formily/core';

const stateKeys = [
  'name',
  'visited',
  'invalid',
  'visible',
  'display',
  'loading',
  'value',
  'required',
  'dataSource',
  'disabled',
  'title',
  'label',
];

export function patchFieldState(setFieldState: IFieldStateSetter, item: Record<string, any>) {
  setFieldState(item.name, (field: Field) => {
    const [formItemProps, componentProps] = [pick(item, stateKeys), omit(item, stateKeys)];
    // console.log(componentProps);
    Object.entries(componentProps).forEach(([key, value]) => {
      if (isPlainObj(value) && isPlainObj(field.componentProps[key])) {
        field.setComponentProps({
          [key]: value,
        });
      } else {
        if (key === 'options' && Array.isArray(value)) {
          field.setDataSource(value);
        }
        field.setComponentProps({
          [key]: value,
        });
      }
    });

    if (field.unmounted === true && formItemProps.visible === true) {
      field.unmounted = false;
    }

    Object.entries(formItemProps).forEach(([key, value]) => {
      if (key === 'visible') {
        field.hidden = !value;
      } else if (key === 'label') {
        field.title = value;
      } else {
        field.setState({
          [key]: value,
        });
      }
    });
  });
}

export function overwriteFieldState(setFieldState, item: Record<string, any>) {
  setFieldState(item.name, state => {
    const [formItemProps, componentProps] = [pick(item, stateKeys), omit(item, stateKeys)];

    Object.assign(state, formItemProps, { props: componentProps });
  });
}
