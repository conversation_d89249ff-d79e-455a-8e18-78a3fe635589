/**
 * Invoke when click Confirm
 * @param callback
 */
export function confirmClick(callback) {
  setTimeout(() => {
    const el = document.getElementById('warehouse-map-click-confirm');
    if (!!el)
      el.onclick = function() {
        !!callback && callback();
      };
  }, 50);
}

export const defaultForm = [
  {
    disabled: false,
    extData: {},
    label: 'Warehouse name',
    labelPosition: 'left',
    name: 'name',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'First and Last name',
    labelPosition: 'left',
    name: 'contactName',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'Phone number',
    labelPosition: 'left',
    name: 'phone',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'Email',
    labelPosition: 'left',
    name: 'email',
    required: false,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'Address',
    labelPosition: 'left',
    name: 'address1',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'countryRegion',
    labelPosition: 'left',
    name: 'countryRegion',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'District',
    labelPosition: 'left',
    name: 'district',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
  {
    disabled: false,
    extData: {},
    label: 'Postal Code',
    labelPosition: 'left',
    name: 'postcode',
    required: true,
    subLabel: '*',
    uiType: 'Input',
    value: '',
    visible: true,
  },
];

export const SortElements = elements => {
  let postCode = [];
  elements.map((el, index) => {
    if (el?.name === 'postcode') {
      postCode = elements.splice(index, 1);
    }
  });
  elements?.map((el, index) => {
    //TO DO address1 in first elements
    const isPostCode = index - 1 >= 0 && elements[index - 1]?.name === 'postcode';
    if (el?.name === 'address1' && !isPostCode && !!postCode[0]) postCode = elements.splice(index, 0, postCode[0]);
  });
  return elements;
};
