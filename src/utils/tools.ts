const subfixMap = {
  sg: 'sg',
  my: 'com.my',
  th: 'co.th',
  vn: 'vn',
  id: 'co.id',
  ph: 'com.ph',
};

export const getCountry = () => {
  const hostname = location.hostname;
  const res = hostname.match(/^sellercenter(-staging)?\.lazada\.(.+)/);
  if (res) {
    return {
      [subfixMap.sg]: 'sg',
      [subfixMap.my]: 'my',
      [subfixMap.th]: 'th',
      [subfixMap.vn]: 'vn',
      [subfixMap.id]: 'id',
      [subfixMap.ph]: 'ph',
    }[res[2]];
  } else {
    const cnRes = hostname.match(/^sellercenter-(\w+)(-staging)?\.lazada-seller\.cn$/);
    if (cnRes) {
      return cnRes[1];
    }
  }
  return 'sg';
};

export const debounce = (fn, ms) => {
  let timeId: any = null;
  return function() {
    const args = arguments;
    const context = this;
    timeId && clearTimeout(timeId);
    timeId = setTimeout(() => {
      fn.apply(context, args);
      timeId = null;
    }, ms);
  };
};

export const setCache = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify({ _warehouse: data }));
  } catch {}
};

export const getCache = key => {
  try {
    const data = localStorage.getItem(key) || '';
    if (!data) return;
    return JSON.parse(data)?._warehouse;
  } catch {}
};
