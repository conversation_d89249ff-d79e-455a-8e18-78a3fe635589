const fields = ['name', 'value', 'data', 'options', 'dataSource', 'extData', 'visible'];

export default (data, isKv = false): any => {
  const obj = {};

  const traverse = elements => {
    if (!elements?.length) {
      return;
    }

    for (const ele of elements) {
      if (ele.name) {
        obj[ele.name] = {};
        for (const field of fields) {
          if (typeof ele[field] !== 'undefined') {
            obj[ele.name][field] = ele[field];
          }
        }
      }
      traverse(ele.elements);
    }
  };

  traverse(data);

  if (isKv) {
    return Object.keys(obj).reduce((res, key) => {
      res[key] = obj[key].value;
      return res;
    }, {});
  }

  return obj;
};
