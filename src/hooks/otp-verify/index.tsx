import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction } from '@alife/asc-components';
import { Button } from '@alifd/next';
import VerificationCode from './verification-code';

import './index.scss';

const OptContent = props => {
  // props.ok();
  const [verifyValue, setVerifyValue] = useState({ code: '', type: '', phone: '', email: '' });
  const [errorMsg, setErrorMsg] = useState('');

  const onChange = v => {
    setVerifyValue(v);
  };

  const onClick = () => {
    VerificationCode.validator(verifyValue as any).then((errorByValidator: string) => {
      if (errorByValidator) {
        // Message.error(errorByValidator);
        setErrorMsg(errorByValidator);
        return;
      }

      props.record
        .doSubmitService({ otpCode: verifyValue.code })
        .then(() => {
          props.ok();
        })
        .catch(e => {
          console.error(e);
          const message = e.data?.data?.error || e.message;
          setErrorMsg(message);
        });
    });
  };
  const onCancel = () => {
    props.close();
  };

  return (
    <div>
      {errorMsg ? (
        <div style={{ color: '#f00', marginBottom: 10 }}>
          {i18n.formatMessage({ id: 'page.account.setting.error', defaultMessage: 'Error' })}: {errorMsg}
        </div>
      ) : null}
      <VerificationCode onChange={onChange} />
      <div
        className="address-dialog-footer"
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          margin: '25px 0 6px 0',
        }}
      >
        <Button style={{ marginRight: 10 }} onClick={onCancel}>
          {i18n.formatMessage({ id: 'page.common.btn.cancel', defaultMessage: 'Cancel' })}
        </Button>
        <Button type="primary" onClick={onClick}>
          {i18n.formatMessage({ id: 'page.account.setting.verifyAndSubmit', defaultMessage: 'Verify and Submit' })}
        </Button>
      </div>
    </div>
  );
};

export const useOtpVerify = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onShowOtp: showFunction }] => {
  config.dialogProps = { ...config.dialogProps, closeMode: ['close', 'esc'] };
  const [OptDialog, { view }] = useFormDialog({ content: OptContent, ...config, hasMessage: false });
  return [OptDialog, { onShowOtp: view }];
};
