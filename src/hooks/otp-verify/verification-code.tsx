import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Input, Button, Message } from '@alifd/next';
import { useRequest, useMemoizedFn } from 'ahooks';
import { useCountDown } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import commonLocale from '../../constants/common-locale';

const bizCode = 'sellerProfileSellerInfoUpdate';

export enum OTP_TYPES {
  PHONE = 'phone',
  EMAIL = 'email',
}

interface IOtpSendParams {
  email?: string;
  phone?: string;
  bizCode?: string;
  nationCode?: string;
}

interface IOtpInfo extends IOtpSendParams {
  type: OTP_TYPES;
  code?: string;
}

const VerificationCode = props => {
  // const [countdown, setTargetDate] = useCountDown();
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      // alert('End of the time');
    },
  });
  const [code, setCode] = useState('');
  const [optType, setOtpType] = useState(OTP_TYPES.PHONE);

  const onChange = options => {
    const res: IOtpInfo = { code, type: optType };
    props.onChange({ ...res, ...options });
  };

  const updateOptType = newOptType => {
    setOtpType(newOptType);
    onChange({ type: newOptType });
  };

  const onCodeChange = e => {
    setCode(e);
    onChange({ code: e });
  };

  const { data: optInfo = {} } = useRequest(
    () =>
      axios
        .get('mtop.global.merchant.subaccount.otp.userinfo')
        .then(({ data: res }) => {
          if (!res.data.phone) {
            setOtpType(OTP_TYPES.EMAIL);
          }
          return res.data;
        })
        .catch(e => {}),
    {},
  );

  const sendCode = useMemoizedFn(() => {
    const data: IOtpSendParams = { bizCode };
    const umidtoken = AWSC?.configFY();

    if (optType === OTP_TYPES.PHONE) {
      data.phone = optInfo.phone;
      data.nationCode = optInfo.nationCode?.toString().replace(/\+/, '');
    } else {
      data.email = optInfo.email;
    }

    return axios
      .get('mtop.global.merchant.subaccount.otp.code.send', {
        headers: {
          'x-ua': umidtoken?.getUA(),
          'x-umidtoken': umidtoken?.umidToken,
          'x-osversion': umidtoken?.uabModule?.getVersion(),
          'x-entrance': 'pc',
        },
        data,
      })
      .then(() => {
        setTargetDate(Date.now() + 60000);
      })
      .catch(e => {
        console.error(e);
        const message = e.data?.data?.error || e.message;
        Message.error(message);
      });
  });

  return (
    <div data-spm="verify_dialog">
      {optType !== OTP_TYPES.PHONE ? (
        <div className="mb10">
          <div className="mb5 grey-text">
            <span>
              {i18n.formatMessage({
                id: 'page.account.setting.yourCurrentLoginEmail',
                defaultMessage: 'Your current login email',
              })}
            </span>
          </div>
          <div>{optInfo.email}</div>
        </div>
      ) : (
        <div className="mb10">
          <div className="mb5 grey-text">
            <span>
              {i18n.formatMessage({
                id: 'page.account.setting.yourCurrentLoginPhone',
                defaultMessage: 'Your current login phone',
              })}
            </span>
          </div>
          <div>
            <span>{optInfo.nationCode}</span>
            <span>{` ${optInfo.phone}`}</span>
          </div>
        </div>
      )}

      <div className="mb10">
        <Input value={code} onChange={onCodeChange} className="mr10" />
        <Button onClick={sendCode} disabled={countdown !== 0} className="aplus-auto-exp" data-spm="d_verify_opt_send">
          {countdown === 0 ? (
            <span>{commonLocale['page.common.btn.send']}</span>
          ) : (
            <span>{commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}</span>
          )}
        </Button>
      </div>
      {optInfo.phone && optInfo.email ? (
        <div>
          {optType === OTP_TYPES.PHONE ? (
            <Button
              onClick={() => {
                updateOptType(OTP_TYPES.EMAIL);
              }}
              className="aplus-auto-exp"
              data-spm="d_switch_verify"
              text
            >
              <img
                style={{ height: 24 }}
                src="https://img.alicdn.com/tfs/TB1qVa3UQL0gK0jSZFAXXcA9pXa-60-60.png"
                alt=""
              />

              <span>
                {i18n.formatMessage({
                  id: 'page.account.setting.verifyViaLoginEmailAddress',
                  defaultMessage: 'Verify via login email address',
                })}
              </span>
            </Button>
          ) : (
            <Button
              onClick={() => {
                updateOptType(OTP_TYPES.PHONE);
              }}
              text
            >
              <img
                style={{ height: 24 }}
                src="https://img.alicdn.com/tfs/TB1t8qPUNz1gK0jSZSgXXavwpXa-60-60.png"
                alt=""
              />

              <span>
                {i18n.formatMessage({
                  id: 'page.account.setting.verifyViaLoginPhoneAddress',
                  defaultMessage: 'Verify via login phone address',
                })}
              </span>
            </Button>
          )}
        </div>
      ) : null}
    </div>
  );
};

VerificationCode.validator = (opt: IOtpInfo): Promise<string> => {
  return new Promise(resolve => {
    if (!opt?.code) {
      return resolve(commonLocale['page.common.tips.required']);
    }
    resolve('');
  });
};

export default VerificationCode;
