import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction } from '@alife/asc-components';
import { useFusionTable } from 'ahooks';
import moment from 'moment';
import { axios } from '@alife/workstation-utils';
import { Table, Pagination } from '@alifd/next';

const DialogContent = ({ record }) => {
  const [error, setError] = useState('');

  const { paginationProps, tableProps } = useFusionTable(options => {
    return axios
      .get('mtop.global.merchant.sellermodifylog.list', {
        params: {
          ...options,
          subType: record.name,
          type: 'profile_field',
        },
      })
      .then(({ data: res }) => {
        return {
          list: res?.data?.dataSource || [],
          total: res?.data?.pageInfo?.total || 0,
        };
      })
      .catch(e => {
        setError(e.data?.data?.error || e.message);
      });
  }, {});

  return (
    <div style={{ width: 730 }}>
      {error ? <div style={{ marginBottom: 10, color: '#f00' }}>{error}</div> : null}
      <Table {...tableProps} fixedHeader={true} maxBodyHeight={500}>
        <Table.Column
          width={180}
          title={i18n.formatMessage({ id: 'page.account.setting.userName', defaultMessage: 'User Name' })}
          dataIndex="operatorName"
        />
        <Table.Column
          width={150}
          title={i18n.formatMessage({ id: 'page.account.setting.userEmail', defaultMessage: 'User Email' })}
          dataIndex="operatorEmail"
        />
        <Table.Column
          width={160}
          title={i18n.formatMessage({ id: 'page.account.setting.updateTime', defaultMessage: 'Update Time' })}
          dataIndex="createTime"
          cell={val => moment(new Date(val)).format('YYYY-MM-DD HH:mm:ss')}
        />
        <Table.Column
          title={i18n.formatMessage({ id: 'page.account.setting.newValue', defaultMessage: 'New Value' })}
          dataIndex="newValue"
          cell={value => <div dangerouslySetInnerHTML={{ __html: value }} />}
        />
      </Table>
      <Pagination {...paginationProps} />
    </div>
  );
};

export const useUpdateHistory = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onShowHistory: showFunction }] => {
  const [HistoryDialog, { view }] = useFormDialog({ content: DialogContent, ...config });
  return [HistoryDialog, { onShowHistory: view }];
};
