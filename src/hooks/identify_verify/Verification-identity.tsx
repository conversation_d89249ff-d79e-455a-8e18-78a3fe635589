import React, { useState, useEffect } from 'react';
import { When } from '@alife/workstation-utils';
import { Button, Box } from '@alifd/next';
import Smscode from './smscode';
import commonLocale from '../../constants/common-locale';

const TYPE = {
  topIcon: 'https://lzd-img-global.slatic.net/g/tps/tfs/TB1v0JvAmBYBeNjy0FeXXbnmFXa-300-300.png',
  mobileVerification: 'https://img.alicdn.com/imgextra/i1/O1CN01P2810q1UiP41Bpv1j_!!6000000002551-2-tps-32-32.png',
  emailVerification: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!6000000007445-2-tps-38-28.png',
  confirmThroughEmail: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!6000000007445-2-tps-38-28.png',
  confirmThroughSMS: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!6000000007445-2-tps-38-28.png',
  confirmThroughDevice: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!6000000007445-2-tps-38-28.png',
};

interface VerificationIdentityProps {
  getlist: () => Promise<any>;
  onSendSmsCode: (setErrorMsg) => void;
  onSendEmailCode: (setErrorMsg) => void;
  onVerifySmsCode: (current, setErrorMsg) => void;
  onVerifyEmailCode: (current, setErrorMsg) => void;
}

enum STEP {
  'selectVerifyType',
  'emailVerification',
  'mobileVerification',
}

export default function VerificationIdentity({
  getlist,
  onSendSmsCode,
  onSendEmailCode,
  onVerifySmsCode,
  onVerifyEmailCode,
}: VerificationIdentityProps) {
  const [step, setstep] = useState<number>(0);
  const [account, setAccount] = useState<string>();
  const [nationalCode, setNationalCode] = useState<string>();
  const [userVerificationList, setUserVerificationList] = useState<Array<object>>();
  const [verifyType, setVerifyType] = useState();

  useEffect(() => {
    getlist().then(res => {
      console.log('list', res);
      setUserVerificationList(res.userVerificationList);
    });
  }, []);

  return (
    <Box style={{ width: 460, minHeight: 360 }} align="center">
      <When condition={step === STEP['selectVerifyType']}>
        <Box direction="column" spacing={20} align="center">
          <img src={TYPE['topIcon']} style={{ width: 100 }} />
          <div>
            <div style={{ textAlign: 'center' }}>To protect your account security, we need to verify your identity</div>
            <div style={{ textAlign: 'center' }}>Please choose a way to verify:</div>
          </div>
          <Box direction="column" spacing={20}>
            {userVerificationList?.map(item => {
              const { type, href, account, icon, accountPrefix } = item as any;
              if (type === 'emailVerification' || type === 'mobileVerification') {
                return (
                  <div style={{ border: '1px solid #e5e5e5', borderRadius: 24, padding: '6px 10px' }}>
                    <Button
                      key={type}
                      className="aplus-auto-exp aplus-auto-clk"
                      data-spm={`d_${type}`}
                      text
                      component="a"
                      href={href}
                      onClick={() => {
                        //请求接口
                        setstep(STEP[type as string]);
                        setAccount(account);
                        setVerifyType(type);
                        setNationalCode(accountPrefix);
                      }}
                    >
                      <img style={{ height: 25 }} src={icon ? icon : TYPE[type]} /> &nbsp;&nbsp;
                      <span>{account}</span>
                    </Button>
                  </div>
                );
              } else {
                return (
                  <div style={{ border: '1px solid #e5e5e5', borderRadius: 24, padding: '5px 10px' }}>
                    <Button
                      key={type}
                      className="aplus-auto-exp aplus-auto-clk"
                      data-spm={`d_${type}`}
                      text
                      onClick={() => {
                        window.open(href);
                      }}
                      component="a"
                      href={href}
                    >
                      <img style={{ width: 30 }} src={icon ? icon : TYPE[type]} /> &nbsp;&nbsp;
                      <span>{account}</span>
                    </Button>
                  </div>
                );
              }
            })}
          </Box>
        </Box>
      </When>
      <When condition={step === STEP['emailVerification'] || step === STEP['mobileVerification']}>
        <div style={{ width: 420 }}>
          <h1> {commonLocale['page.LZDlogin.enterCode']} </h1>
          <Smscode
            account={account}
            onNext={async (current, setErrorMsg) => {
              if (verifyType === 'mobileVerification') {
                return onVerifySmsCode(current, setErrorMsg);
              }
              if (verifyType === 'emailVerification') {
                return onVerifyEmailCode(current, setErrorMsg);
              }
            }}
            onBack={() => {
              setstep(STEP['selectVerifyType']);
            }}
            onResend={async setErrorMsg => {
              if (verifyType === 'mobileVerification') {
                return onSendSmsCode(setErrorMsg);
              }
              if (verifyType === 'emailVerification') {
                return onSendEmailCode(setErrorMsg);
              }
            }}
            locale={{
              enterCodeTips: commonLocale['page.account.setting.Enter6digit'],
              enterCode: commonLocale['page.LZDlogin.enterCode'], //#
              resend: (commonLocale['page.common.btn.resendAfter'] as unknown) as string, //#
              receiveTips: commonLocale['page.account.setting.notReceiveTheCode'],
              back: commonLocale['page.common.btn.previous'],
              next: commonLocale['page.common.btn.next'],
              send: commonLocale['page.common.btn.send'],
            }}
            nationalCode={nationalCode}
          />
        </div>
      </When>
    </Box>
  );
}
