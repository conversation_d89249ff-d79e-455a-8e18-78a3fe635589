import React, { useState } from 'react';
import { Input, Button, Message, Box, Loading } from '@alifd/next';
import { useRequest, useMemoizedFn, useCreation, useCountDown } from 'ahooks';
import debounce from 'lodash/debounce';
import { ASCTools, axios, getCookie, setCookie } from '@alife/workstation-utils';
import commonLocale from '../../constants/common-locale';
import {
  CONST_SWITCH_BUTTON,
  CONST_PHONE_PREFIX,
  CONST_SWITCH_INFO,
  EnumOtpType,
  CONST_OTP_TYPE_SHOW,
  EnumOtpChannel,
} from '../../pages/account/constants';
import { SwitchButton, SwitchInfo } from './switch-otp';
import { createVerificationIdentityDialog } from '../verification-identity';

interface IOtpSendParams {
  email?: string;
  phone?: string;
  fingerPrint?: string;
  verifyCode?: string;
  nationCode?: string;
  bizCode: string;
}

interface IOtpInfo extends IOtpSendParams {
  otpType: EnumOtpType;
  nationCode?: string;
  masterNationCode?: string;
  countryNationCode?: string;
}

type IProps = {
  bizCode: string;
  onChange?: (option: IOtpInfo) => void;
  fingerPrint?: string;
  updateType: EnumOtpType;
};

const country = ASCTools.getCountry();
// CB卖家可以选择修改的渠道
const cbSeller = [EnumOtpChannel.SMS, EnumOtpChannel.EMAIL];
const VerificationCode = (props: IProps) => {
  const [targetDate, setTargetDate] = useState<number>();
  const [countdown] = useCountDown({ targetDate });
  const [verifyCode, setVerifyCode] = useState('');
  const [otpType, setOtpType] = useState(EnumOtpType.PHONE);
  const { data: optInfo = {}, loading: otpInfoLoading } = useRequest(
    () =>
      axios
        .get('mtop.global.seller.member.getUserInfo')
        .then(({ data: res }) => {
          if (!res.phone) {
            setOtpType(EnumOtpType.EMAIL);
          }
          return res;
        })
        .catch(() => {}),
    {},
  );

  const { data: currentOptInfo = {}, loading: currentLoading } = useRequest(
    () =>
      axios
        .get('mtop.global.merchant.subaccount.otp.userinfo')
        .then(({ data: res }) => {
          return res?.data;
        })
        .catch(() => {}),
    {},
  );

  const { data: otpChannelList = [], loading: otpChannelLoading } = useRequest<EnumOtpChannel[], []>(
    () =>
      axios
        .post('mtop.global.seller.chats.app.list')
        .then(({ data: res }) => {
          let result: EnumOtpChannel[] = [];
          if (optInfo?.email) {
            result.push(EnumOtpChannel.EMAIL);
          }
          if (optInfo?.phone) {
            result = [...result, ...(res?.module || [])];
          }
          return result;
        })
        .catch(err => {
          return [];
        }),
    {
      ready: !otpInfoLoading,
    },
  );

  const onChange = useMemoizedFn(options => {
    const res: IOtpInfo = {
      verifyCode,
      otpType,
      phone: optInfo.phone,
      email: optInfo.email,
      countryNationCode: '+' + (optInfo.phonePrefixCode || CONST_PHONE_PREFIX[country]),
      bizCode: props.bizCode,
      fingerPrint: props.fingerPrint,
    };

    props.onChange?.({ ...res, ...options });
  });

  const updateOptType = useMemoizedFn(newOptType => {
    setOtpType(newOptType);
    onChange({ otpType: newOptType });
  });

  const onCodeChange = useMemoizedFn(v => {
    setVerifyCode(v);
    onChange({ verifyCode: v });
  });

  // 可切换验证类型
  const switchBtnList = useCreation(() => {
    const result: any[] = [];
    // TODO 当前代码暂时不支持语音验证码，后续根据产品需求补充枚举
    for (const val of otpChannelList) {
      if (!CONST_SWITCH_BUTTON[val]) continue;
      // 判断当前卖家手机区号是否和和当前国家一致，不一致不给使用特定国家校验方式
      if (currentOptInfo.countryNationCode === `+${CONST_PHONE_PREFIX[country]}`) {
        CONST_SWITCH_BUTTON[val].otpType !== otpType && result.push(CONST_SWITCH_BUTTON[val]);
      } else {
        CONST_SWITCH_BUTTON[val].otpType !== otpType && cbSeller.includes(val) && result.push(CONST_SWITCH_BUTTON[val]);
      }
    }
    return result;
  }, [otpType, otpChannelLoading, currentLoading]);

  const sendCode = debounce(() => {
    const type = otpType === EnumOtpType.PHONE ? 'phoneNumber' : 'email'; //判断发送发送smsCode还是发送emailCode
    const token = getCookie('token');
    const data = {
      token: token,
      type: 'COMMON_VERIFICATION',
      platform: 'pc',
      [type]: optInfo[otpType],
      countryCallingCode: optInfo?.phonePrefixCode || optInfo?.countryCallingCode,
      deliveryType: CONST_SWITCH_INFO[otpType].deliveryType,
    };
    const url = CONST_SWITCH_INFO[otpType].requestUrl;

    return axios
      .get(url, { data })
      .then(({ data }) => {
        if (data.success) {
          setTargetDate(Date.now() + 60000);
        } else {
          Message.error(data?.errorCode?.displayMessage || 'send fail');
        }
      })
      .catch(err => {
        const { data: resp } = err;
        console.log(err);
        Message.error(resp?.data?.errorCode?.displayMessage || 'send fail');
      });
  });

  return (
    <Loading visible={otpInfoLoading || otpChannelLoading || currentLoading} style={{ width: '100%' }}>
      <div className="aplus-module-auto-exp" data-spm="code">
        <SwitchInfo
          {...CONST_SWITCH_INFO[otpType]}
          prefix={optInfo.phonePrefixCode}
          address={optInfo[CONST_OTP_TYPE_SHOW[otpType]]}
          otpType={otpType}
        />

        <div>
          <div>{CONST_SWITCH_INFO[otpType].titleLocale}</div>
          <Input
            value={verifyCode}
            placeholder={commonLocale['page.LZDlogin.enterCode']}
            onChange={onCodeChange}
            className="mr10"
          />
          <Button
            onClick={sendCode}
            disabled={countdown !== 0}
            className="aplus-auto-exp aplus-auto-clk"
            data-spm={`d_send_code_${props.updateType}`}
          >
            {countdown === 0
              ? commonLocale['page.common.btn.send']
              : commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}
          </Button>
        </div>
        <Box direction="column" align="flex-start" spacing={5} style={{ marginTop: 10 }}>
          {switchBtnList.map(item => {
            return (
              <SwitchButton
                {...item}
                updateType={props.updateType}
                onClick={() => {
                  updateOptType(item.otpType);
                  setVerifyCode('');
                }}
              />
            );
          })}
        </Box>
      </div>
    </Loading>
  );
};

VerificationCode.validator = (opt: IOtpInfo): Promise<string> => {
  return new Promise(resolve => {
    if (!opt) {
      return resolve(commonLocale['page.common.tips.required']);
    }
    const currentToken = getCookie('token');
    const data = {
      type: 'COMMON_VERIFICATION',
      platform: 'pc',
      code: opt?.verifyCode,
      token: currentToken,
      [opt.otpType === 'email' ? 'email' : 'phoneNumber']: opt[CONST_OTP_TYPE_SHOW[opt.otpType]],
      countryCallingCode: opt.countryNationCode,
    };

    if (!data.code) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    return axios
      .post(CONST_SWITCH_INFO[opt.otpType].checkUrl, { ...data })
      .then((res: any): void => {
        const { token, tokenType, success } = res.data;
        if (success) {
          if (tokenType === 'ACCOUNT_SECOND_VERIFICATION') {
            createVerificationIdentityDialog({ token, tokenType, resolve });
          } else {
            //设置新的token
            setCookie('token', '');
            setCookie('token', token);
            resolve(res.message);
          }
        } else {
          resolve(res?.data?.errorCode?.displayMessage || 'unknown error');
        }
      })
      .catch(err => {
        const { data: resp } = err;
        resolve(resp?.data?.errorCode?.displayMessage || 'unknown error');
      });
  });
};

export default VerificationCode;
