import React from 'react';
import { Dialog } from '@alifd/next';
import { axios, setCookie } from '@alife/workstation-utils';
import VerificationIdentity from './Verification-identity';

//获取可验证类型
const getlist = ({ token, tokenType }) => {
  return axios
    .post('mtop.global.seller.identification.getidentificationlist', {
      platform: 'pc',
      token,
      tokenType,
    })
    .then(({ data }) => data);
};

//二次验证发送code
const sendSmsCode = ({ token }) => {
  return axios
    .post('mtop.global.seller.verificationcode.sendsms', {
      type: 'ACCOUNT_SECOND_VERIFICATION',
      platform: 'pc',
      token,
    })
    .then(({ data }) => data);
};

const sendEmailCode = ({ token }) => {
  return axios
    .post('mtop.global.seller.verificationCode.sendEmail', {
      type: 'ACCOUNT_SECOND_VERIFICATION',
      platform: 'pc',
      token,
    })
    .then(({ data }) => data);
};

//二次验证code
const VerifySmsCode = ({ token, code }) => {
  return axios
    .post('mtop.global.seller.identification.verifyidentificationotpcode', {
      type: 'ACCOUNT_SECOND_VERIFICATION',
      platform: 'pc',
      code,
      token,
    })
    .then(({ data }) => data);
};

const VerifyEmailCode = ({ token, code }) => {
  return axios
    .post('mtop.global.seller.identification.verifyIdentificationEmailCode', {
      type: 'ACCOUNT_SECOND_VERIFICATION',
      platform: 'pc',
      code,
      token,
    })
    .then(({ data }) => data);
};

export const VerificationIdentityHandle = (props: any) => {
  const { resolve, token, tokenType } = props;
  const dialog = {};
  dialog['show'] = Dialog.show({
    hasMask: false,
    footer: false,
    content: (
      <VerificationIdentity
        getlist={() => {
          return getlist({
            token,
            tokenType,
          });
        }}
        //发送sms
        onSendSmsCode={setErrorMsg => {
          return sendSmsCode({
            token,
          })
            .then(({ data }) => {
              if (data?.success === false) {
                return Promise.reject();
              }
            })
            .catch(err => {
              const { data: resp } = err;
              setErrorMsg(resp?.data?.errorCode?.displayMessage || err || 'unknown error');
            });
        }}
        //发送email
        onSendEmailCode={setErrorMsg => {
          return sendEmailCode({
            token,
          })
            .then(({ data }) => {
              if (data?.success === false) {
                return Promise.reject();
              }
            })
            .catch(err => {
              const { data: resp } = err;
              setErrorMsg(resp?.data?.errorCode?.displayMessage || 'unknown error');
            });
        }}
        //验证 sms
        onVerifySmsCode={(code, setErrorMsg) => {
          return VerifySmsCode({
            token,
            code,
          })
            .then(res => {
              const { token, tokenType, success } = res;
              if (success) {
                if (tokenType === 'ACCOUNT_SECOND_VERIFICATION') {
                  dialog['show'].hide();
                  VerificationIdentityHandle({ resolve, token, tokenType });
                } else {
                  setCookie('token', '');
                  setCookie('token', res.token);
                  resolve(''); //二次验证成功
                  dialog['show'].hide();
                }
              } else {
                setErrorMsg(res.message);
              }
            })
            .catch(err => {
              const { data: resp } = err;
              setErrorMsg(resp?.data?.errorCode?.displayMessage || 'unknown error');
            });
        }}
        //验证email
        onVerifyEmailCode={(code, setErrorMsg) => {
          return VerifyEmailCode({
            token,
            code,
          })
            .then(res => {
              const { token, tokenType, success } = res;
              if (success) {
                if (tokenType === 'ACCOUNT_SECOND_VERIFICATION') {
                  dialog['show'].hide();
                  VerificationIdentityHandle({ resolve, token, tokenType });
                } else {
                  setCookie('token', '');
                  setCookie('token', res.token);
                  resolve(''); //二次验证成功
                  dialog['show'].hide();
                }
              } else {
                setErrorMsg(res.message);
              }
            })
            .catch(err => {
              const { data: resp } = err;
              setErrorMsg(resp?.data?.errorCode?.displayMessage || 'unknown error');
            });
        }}
      />
    ),
    onClose: () => {
      resolve('Please verify it again'); //关闭验证窗口，提示
    },
  });
};
