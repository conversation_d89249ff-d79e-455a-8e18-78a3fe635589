import React, { useRef, useState, useEffect } from 'react';
import { Form, Button, Input, Box } from '@alifd/next';
import { useCountDown } from 'ahooks';
import './index.scss';

const NumberRegex = /^[0-9]*$/;
interface IProps {
  onBack: () => void;
  onNext: (...args: any[]) => Promise<any>;
  onResend: (...args: any[]) => Promise<any>;
  onChange?: () => void;
  errorMsg?: string;
  account?: string | number;
  nationalCode?: string;
  // actionType?: SMS_ACTION_TYPE_PARAMS;
  locale?: {
    enterCodeTips?: string;
    enterCode?: string;
    resend?: any;
    receiveTips?: string;
    back?: string;
    next?: string;
    send?: string;
  };
}

const COUNT_DOWN_TIME = 60000;
const MAX_FOCUS_IDX = 5;
const COUNT_NUM = 6;

export const SMSCode = (props: IProps) => {
  const { onBack, onResend, onNext, onChange, account, nationalCode, locale } = props;
  let numberInput = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState(false);
  const [current, setCurrent] = useState<string>('');
  // const [countdown, setTargetDate] = useCountDown();
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      // alert('End of the time');
    },
  });
  const [curIdx, setCurIdx] = useState<number>(0);
  const [errorMsg, setErrorMsg] = useState('');

  const fakeInputs = new Array(COUNT_NUM).fill(0);
  function autoFocus() {
    numberInput && numberInput?.current?.focus();
  }

  function changeInput(value) {
    setErrorMsg('');
    if (!NumberRegex.test(value)) return;
    const countIdx = value.length - 1;
    onChange && onChange();
    if (!value) {
      setCurIdx(0);
    } else if (value && countIdx !== curIdx) {
      const tempArr = value.split('');
      const appendNum = tempArr.pop();
      tempArr.splice(curIdx, 1, appendNum);
      value = tempArr.join('');
      const step = countIdx > curIdx ? 1 : -1;
      setCurIdx(curIdx + step);
    } else {
      setCurIdx(Math.min(curIdx + 1, MAX_FOCUS_IDX));
    }

    setCurrent(value);
  }
  function onClick(item) {
    const cur = Number(item.target.id);
    autoFocus();
    setCurIdx(cur);
  }

  useEffect(() => {
    //进来就发送验证
    setTargetDate(Date.now() + COUNT_DOWN_TIME);
    onResend();

    autoFocus();
    setCurIdx(0);
    document.addEventListener('click', autoFocus);
    return () => document.removeEventListener('click', autoFocus);
  }, []);

  return (
    <div className="sms-code-container">
      <div className="sms-code-container-tips">
        {locale?.enterCodeTips}&nbsp;
        <span className="sms-code-container-tips-number">
          {account && NumberRegex.test(account as string) ? `+ ${nationalCode} ${account}` : account}
        </span>
      </div>
      <div className="row-form">
        <Form>
          <div className="login-form">
            <Input
              data-spm="sms_code_input"
              className="hidden-ipt"
              ref={numberInput as any}
              hasBorder={false}
              onChange={changeInput}
              value={current}
              trim={true}
              cutString={true}
              maxLength={6}
              type="hidden"
            ></Input>
            <div className="number-wrp">
              {fakeInputs.map((item, index) => {
                return (
                  <div
                    data-spm="d_sign_up_resend"
                    onClick={onClick}
                    id={`${index}`}
                    className={`number-wrp-ipt index${index} ${curIdx === index ? 'highlight' : ''} ${
                      errorMsg ? 'error-light' : ''
                    }`}
                  >
                    {current[index] ? current[index] : ''}
                  </div>
                );
              })}
            </div>
            {errorMsg ? <div className="error-message">{errorMsg}</div> : null}
            <div className="sms-code-container-tips">
              <span className="sms-code-container-tips-receive">{locale?.receiveTips}</span>&nbsp;
              <Button
                text
                disabled={countdown > 0}
                onClick={() => {
                  setTargetDate(Date.now() + COUNT_DOWN_TIME);
                  onResend && onResend(setErrorMsg);
                }}
              >
                {countdown === 0 ? locale?.send : locale?.resend({ time: Math.round(countdown / 1000) })}
              </Button>
            </div>
            <Box spacing={20}>
              <Button
                type="primary"
                className="aplus-auto-exp aplus-auto-clk"
                data-spm="d_sms_code_submit"
                style={{
                  height: 48,
                  width: 420,
                  borderRadius: 12,
                  fontSize: 16,
                }}
                loading={loading}
                onClick={async () => {
                  setLoading(true);
                  await onNext(current, setErrorMsg);
                  setLoading(false);
                }}
              >
                {locale?.next}
              </Button>
              <Button
                type="normal"
                style={{
                  height: 48,
                  width: 420,
                  borderRadius: 12,
                  fontSize: 16,
                  color: '#858b9c',
                }}
                className="btn aplus-auto-exp aplus-auto-clk plain"
                data-spm="sms_code_back"
                onClick={onBack}
              >
                {locale?.back}
              </Button>
            </Box>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default SMSCode;
