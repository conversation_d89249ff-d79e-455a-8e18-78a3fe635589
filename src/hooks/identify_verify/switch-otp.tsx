import { Button } from '@alifd/next';
import { ButtonProps } from '@alifd/next/types/button';
import React from 'react';
import { EnumOtpType, SwitchChannelInfo, EnumOtpChannel } from '../../pages/account/constants';

interface SwitchButtonProps extends ButtonProps {
  switchLocale: string;
  otpType: EnumOtpChannel;
  updateType: EnumOtpType;
}

interface SwitchInfoProps extends SwitchChannelInfo {
  prefix: string;
  address: string;
  otpType: EnumOtpType;
}

const icons = {
  [EnumOtpType.PHONE]: 'https://img.alicdn.com/imgextra/i1/O1CN01O1MDZI1x5aU5XesBs_!!*************-55-tps-12-12.svg',
  [EnumOtpType.EMAIL]: 'https://img.alicdn.com/imgextra/i1/O1CN01fJXoEw1S29QFsATuu_!!*************-55-tps-12-12.svg',
  [EnumOtpType.WHATSAPP]: 'https://img.alicdn.com/imgextra/i3/O1CN01wwLaMt1I68iSP6CAH_!!*************-55-tps-12-12.svg',
  [EnumOtpType.ZALO]: 'https://img.alicdn.com/imgextra/i3/O1CN01T15Zty22mFS9gy08O_!!*************-55-tps-12-12.svg',
  [EnumOtpType.VIBER]: 'https://img.alicdn.com/imgextra/i1/O1CN01mhC9LC1ilWVvlnnVU_!!*************-55-tps-12-12.svg',
};

export const SwitchButton: React.FC<SwitchButtonProps> = ({ switchLocale, otpType, updateType, ...arg }) => {
  return (
    <Button {...arg} text>
      <img className="opt-switcher-icon" src={icons[otpType]} alt="" style={{ marginRight: 5, height: 16 }} />
      <span>{switchLocale}</span>
    </Button>
  );
};

export const SwitchInfo: React.FC<SwitchInfoProps> = ({ currentLocale, prefix, address, otpType }) => {
  return (
    <div>
      <div>{currentLocale}</div>
      <div>{otpType !== EnumOtpType.EMAIL ? `+${prefix} ${address}` : address}</div>
    </div>
  );
};
