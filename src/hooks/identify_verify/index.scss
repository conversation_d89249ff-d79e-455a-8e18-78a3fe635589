//sms-code样式
.sms-code-container {
  width: 420px;
  .login-form {
    .next-number-picker-normal {
      width: 44px;
      height: 46px;
      margin: 0 5px;
    }

    .hidden-ipt {
      position: absolute;
      z-index: -3;
      opacity: 0;
    }

    .number-wrp {
      display: flex;
      justify-content: space-between;
      font-family: Roboto-Regular;
      font-size: 20px;
      color: #111111;
      margin-bottom: 12px;
      width: 350px;
      &-ipt {
        width: 48px;
        height: 48px;
        line-height: 48px;
        // margin-right: 12px;
        border-radius: 12px;
        text-align: center;
        border: 1px solid #c6cad2;
      }
      .highlight {
        border-color: #2c64ff;
      }
      .error-light {
        color: #e61414;
        border: 1px solid #e61414;
      }
    }
    .error-message {
      color: #e61414;
      font-size: 14px;
      margin-bottom: 24px;
    }
    .input-account {
      &.phone {
        display: flex;
        align-items: center;

        .ipt,
        input {
          position: relative;
          width: 420px;
          height: 48px;
          border-radius: 12px;
        }
      }
      &.sms {
        .next-number-picker {
          width: 88px;
          height: 92px;
        }
      }
      .line {
        height: 34px;
        width: 1px;
        background: #cecece;
        position: absolute;
        left: 121px;
      }
      .cur-country {
        position: absolute;
        z-index: 3;
        top: 1px;
        left: calc(100% - 88px);
        display: flex;
        justify-content: center;
        align-items: center;
        // width: 78px;
        height: 45px;
        line-height: 45px;
        background: #fff;
        font-size: 16px;
        color: #868686;
        cursor: pointer;

        .country-icon {
          margin-top: 5px;
          img {
            height: 22px;
            width: 22px;
            border-radius: 11px;
            border: 1px solid #e2e2e2;
          }
        }
        .country-text {
          margin-left: 4px;
        }
      }
      .ipt,
      input {
        width: 420px;
        height: 48px;
        background: #fcfcfd;
        border: 1px solid #ecf0f3;
        border-radius: 12px;
        font-size: 14px;
        color: #454749;
        transition: unset;
      }
      input {
        padding-left: 20px;
        border: 0 none;
      }
    }
    .btn {
      width: 420px;
      height: 48px;
      background: #1a71ff;
      border-radius: 12px;
      margin-left: 0;
      opacity: 0.96;
      font-size: 16px;
      color: #ffffff;
      margin-bottom: 16px;
      &:hover {
        background-color: #618bff;
      }
    }
    .plain {
      background: #fff;
      color: #858b9c;
      border: 1px solid #c6cad2;
      border-radius: 12px;
      &:hover {
        background-color: #f2f3f7;
      }
    }
    &.sms {
      margin-bottom: 30px;
    }
    .next-box {
      margin-top: 60px;
    }
  }

  &-titlewrp {
    position: relative;
    width: 100%;
    height: 35px;
    line-height: 35px;
    justify-content: space-between;
    margin-bottom: 40px;
    .title {
      display: inline-block;
      font-size: 30px;
      color: #111111;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
  &-tips {
    position: relative;
    color: #595f6d 100%;
    font-size: 16px;
    margin-bottom: 24px;
    .countdown-wrp {
      color: #3f70f3;
      cursor: pointer;
      margin-left: 5px;
    }
    &-number {
      font-size: 16px;
      font-weight: bold;
      color: #111111;
    }

    &-receive {
      color: #595f6d;
      font-size: 14px;
    }
  }
}
