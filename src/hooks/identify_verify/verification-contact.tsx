import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Input, Button, Message } from '@alifd/next';
import { useMemoizedFn, useRequest, useCountDown } from 'ahooks';
import { axios, getCookie } from '@alife/workstation-utils';
import debounce from 'lodash/debounce';
import commonLocale from '../../constants/common-locale';
import { CONST_SWITCH_INFO } from '../../pages/account/constants';

export enum OTP_TYPES {
  PHONE = 'phone',
  EMAIL = 'email',
}
interface IOtpSendParams {
  email?: string;
  phone?: string;
  verifyCode: string;
  otpType: string;
  bizCode: string;
  token: string;
}
interface IOtpInfo extends IOtpSendParams {
  otpType: OTP_TYPES;
  nationCode?: string;
  masterNationCode?: string;
  countryNationCode?: string;
  phonePrefixCode?: string;
}

enum CHANGE_TYPE {
  password = 'CHANGE_PASSWORD',
  phone = 'CHANGE_PHONE',
  email = 'CHANGE_EMAIL_WITH_CODE',
}

const VerificationContact = props => {
  // const [countdown, setTargetDate] = useCountDown();
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      // alert('End of the time');
    },
  });
  const [verifyCode, setVerifyCode] = useState('');
  const [inputValue, setInputValue] = useState('');
  const otpType = props.isUpdatePhone ? 'phone' : 'email';

  const { data: optInfo = {} } = useRequest(
    () =>
      axios
        .get('mtop.global.merchant.subaccount.otp.userinfo')
        .then(({ data: res }) => {
          return res;
        })
        .catch(() => {}),
    {},
  );

  const getPhoneEmailValue = useMemoizedFn((currentValue?) => ({
    [otpType]: currentValue || inputValue,
  }));
  const countryNationCode =
    optInfo?.data?.countryNationCode ?? `+${props.countryNationCode?.toString().replace(/\+/, '')}`;
  const onChange = useMemoizedFn(options => {
    const res = { verifyCode, otpType, bizCode: props.bizCode };
    props.onChange({ ...res, ...getPhoneEmailValue(), ...options, countryNationCode: countryNationCode });
  });

  const onCodeChange = useMemoizedFn(v => {
    setVerifyCode(v);
    onChange({ verifyCode: v });
  });

  const onFingerPrintChange = useMemoizedFn(v => {
    setInputValue(v);
    onChange(getPhoneEmailValue(v));
  });

  const sendCode = debounce(() => {
    if (!inputValue) {
      Message.error(
        props.isUpdatePhone
          ? i18n.formatMessage({
              id: 'page.account.setting.phoneNumberIsRequired',
              defaultMessage: 'Phone Number is required',
            })
          : i18n.formatMessage({ id: 'page.account.setting.emailIsRequired', defaultMessage: 'Email is required' }),
      );
      return;
    }

    const token = getCookie('token');
    const data = {
      // type: 'COMMON_VERIFICATION',
      type: CHANGE_TYPE[otpType],
      platform: 'pc',
      token: token,
      [otpType === 'phone' ? 'phoneNumber' : 'email']: inputValue,
      countryCallingCode: countryNationCode,
      deliveryType: CONST_SWITCH_INFO[otpType].deliveryType,
    };

    const urls = {
      phone: 'mtop.global.seller.verificationcode.sendsms',
      email: 'mtop.global.seller.verificationCode.sendEmail',
    };

    return axios
      .get(urls[otpType], { data })
      .then(({ data }) => {
        if (data.success) {
          setTargetDate(Date.now() + 60000);
        } else {
          Message.error(data?.errorCode?.displayMessage || 'send fail');
        }
      })
      .catch(err => {
        const { data: resp } = err;
        Message.error(resp?.data?.errorCode?.displayMessage || 'send fail');
      });
  });

  const phonePlaceholder =
    parseInt(countryNationCode) !== 66
      ? i18n.formatMessage({
          id: 'page.LZDregister.sms.phone.placeholder',
          defaultMessage: 'Phone number',
        })
      : '08XXXXXXXX';
  const emailPlaceholder = i18n.formatMessage({
    id: 's-wb-subaccount@email',
    defaultMessage: 'Email',
    app: 's-wb-subaccount',
  });
  const inputPlaceHolder = props.isUpdatePhone ? phonePlaceholder : emailPlaceholder;

  return (
    <div className="aplus-module-auto-exp" data-spm="contact">
      <div>
        <div>
          {props.isUpdatePhone
            ? i18n.formatMessage({ id: 'page.account.setting.newPhoneNumber', defaultMessage: 'New Phone Number' })
            : i18n.formatMessage({ id: 'page.account.setting.newEmail', defaultMessage: 'New Email' })}
        </div>
        <div style={{ width: 265 }}>
          <Input
            addonTextBefore={props.isUpdatePhone ? countryNationCode : null}
            value={inputValue}
            onChange={onFingerPrintChange}
            placeholder={inputPlaceHolder}
          />
        </div>
      </div>
      <div>
        <div>{commonLocale['page.account.setting.verificationCode']}</div>
        <Input
          value={verifyCode}
          placeholder={commonLocale['page.LZDlogin.enterCode']}
          onChange={onCodeChange}
          className="mr10"
        />
        <Button
          onClick={sendCode}
          disabled={countdown !== 0}
          className="aplus-auto-exp aplus-auto-clk"
          data-spm={`d_send_code_contact_${props.updateType}`}
        >
          {countdown === 0
            ? commonLocale['page.common.btn.send']
            : commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}
        </Button>
      </div>
    </div>
  );
};

VerificationContact.validator = (opt: IOtpInfo, verifySuccess) => {
  return new Promise(resolve => {
    if (!opt) {
      return resolve(commonLocale['page.common.tips.required']);
    }
    const token = getCookie('token');
    const data: any = {
      fingerPrint: opt[opt.otpType],
      type: CHANGE_TYPE[opt.otpType],
      platform: 'pc',
      code: opt.verifyCode,
      token: token,
      [opt.otpType === 'phone' ? 'phoneNumber' : 'email']: opt[opt.otpType],
      countryCallingCode: opt?.phonePrefixCode || opt?.countryNationCode,
    };

    if (!data.fingerPrint) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    if (!data.code) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    const urls = {
      phone: 'mtop.global.seller.identification.verifyIdentificationOTPCode',
      email: 'mtop.global.seller.identification.verifyIdentificationEmailCode',
    };

    return axios
      .post(urls[opt.otpType], data)
      .then((res: any) => {
        if (res?.data?.success) {
          resolve('');
          verifySuccess(); //修改成功，关闭弹窗
        } else {
          Message.error(res?.data?.errorCode?.displayMessage || 'unknown error');
        }
      })
      .catch(err => {
        const { data: resp } = err;
        resolve(resp?.data?.errorCode?.displayMessage || 'unknown error');
      });
  });
};

export default VerificationContact;
