import * as i18n from '@alife/lazada-i18n';
import React, { useEffect, useState } from 'react';
import { useFormDialog, IDialogProps, showFunction, IDialogConfig } from '@alife/asc-components';
import { axios, Switch, Case, getCookie, delCookie } from '@alife/workstation-utils';
import { Box, Button, Message, Typography, Loading } from '@alifd/next';
import { useSetState, useRequest } from 'ahooks';
import commonLocale from '../../constants/common-locale';
import './index.scss';
interface IBind {
  bind: boolean;
  lack?: boolean;
  expire?: boolean;
}
interface IProfile {
  link: string;
  name: string;
  email: string;
  friendsCount?: number | string;
  profilePicture?: string;
}

const StatusText = {
  lack: i18n.formatMessage({
    id: 'page.setting.bindAccount.lack',
    defaultMessage: 'Bind Again',
  }),
  expire: i18n.formatMessage({
    id: 'page.setting.bindAccount.expire',
    defaultMessage: 'Bind Facebook',
  }),
  bind: i18n.formatMessage({
    id: 'page.setting.bindAccount.bind',
    defaultMessage: 'Bind Facebook',
  }),
  unBind: i18n.formatMessage({
    id: 'page.setting.bindAccount.unbind',
    defaultMessage: 'Unbind',
  }),
};
export const checkBind = () => {
  return axios
    .get('mtop.lazada.merchant.social.media.facebook.status.get')
    .then(res => res.data.data)
    .catch();
};

export const getFacebookProfile = () => {
  return axios
    .get('mtop.lazada.merchant.social.media.facebook.profile.get')
    .then(res => res.data.data)
    .catch();
};

const FacebookContent = props => {
  const { record } = props;
  const { bindStatus } = record;
  const [bindData, setBindData] = useSetState<IBind>({
    ...bindStatus,
  });
  const [profile, setProfile] = useSetState<IProfile>({
    link: '',
    name: '',
    email: '',
    friendsCount: '',
    profilePicture: '',
  });
  const [loading, setLoading] = useState(false);
  const { loading: fbLoading } = useRequest(
    async () => {
      const data = await getFacebookProfile();
      if (data) {
        setProfile(data);
        setLoading(false);
      }
      return data;
    },
    {
      ready: true,
    },
  );
  const bindFacebook = (token: string) => {
    return axios
      .post('mtop.lazada.merchant.social.media.facebook.bind', {
        oauthCode: token,
        oauthType: 'facebook',
        redirectUri: location.href,
        // traffic: 'seller',
      })
      .then(res => res.data.data)
      .catch(e => Message.error(e));
  };
  const deBindFacebook = (): Promise<any> => {
    return axios
      .post('mtop.lazada.merchant.social.media.facebook.unbind', {
        oauthOpenType: 'facebook',
      })
      .then(res => {
        const { data } = res as any;
        const { error = '', errorCode = '' } = data;
        if (error) {
          Message.error({
            content: errorCode,
            offset: [0, 100],
          });
          return false;
        }
        Message.success({
          content: 'Unbind Success',
          offset: [0, 100],
        });
        setLoading(true);
        Promise.all([checkBind(), getFacebookProfile()]).then(([bindStatus, profile]) => {
          setLoading(false);
          setProfile(profile);
          setBindData(bindStatus);
        });
      })
      .catch(e => {
        Message.error({
          content: e,
          offset: [0, 100],
        });
        return false;
      });
  };
  const logoutFacebook = () => {
    document.cookie = 'domain=facebook.com;access_token=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;';
    document.cookie = 'domain=facebook.com;xs=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;';
    if (window.FB) {
      return window.FB.logout(function(response) {
        // window.alert('logout facebook' + JSON.stringify(response));
      });
    }
    return false;
  };
  const facebookAuth = async () => {
    const extraData = {
      scope: 'email, user_posts, user_photos, user_videos, user_likes, user_friends, user_link, public_profile',
      auth_type: 'reauthorize',
    };
    await logoutFacebook();
    if (window.FB && window.FB.login) {
      window.FB.login(async response => {
        if (response.authResponse) {
          const token = response.authResponse.accessToken;
          const data = await bindFacebook(token);
          if (data) {
            setLoading(true);
            Promise.all([checkBind(), getFacebookProfile()]).then(([bindStatus, profile]) => {
              setLoading(false);
              setProfile(profile);
              setBindData(bindStatus);
            });
          } else {
            Message.error({
              content: 'Third party bind failed！',
              offset: [0, 100],
            });
          }
        }
      }, extraData);
    } else {
      Message.error({
        content: 'Third party init failed',
        offset: [0, 100],
      });
    }
  };
  return (
    <Loading visible={fbLoading || loading}>
      <Switch>
        <Case when={bindData?.bind && !bindData?.expire}>
          <Box className="facebook-binding">
            <Typography.H4>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.title',
                defaultMessage: 'Social Account Binding',
              })}
            </Typography.H4>
            <p style={{ color: '#4F5669' }}>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.succDesc',
                defaultMessage: 'You have successfully bound your Facebook account',
              })}
            </p>
            {bindData?.lack && (
              <p style={{ color: '#3A3A3A' }}>
                {i18n.formatMessage({
                  id: 'page.account.setting.bindAccount.lackDesc',
                  defaultMessage:
                    ' But we are lacking of the following information: Your friend list, post, videos, photos, likes and links',
                })}
              </p>
            )}
            <Box justify="center" align="center" className="fb-profile-msg">
              <Typography.H5>
                {i18n.formatMessage({
                  id: 'page.account.setting.bindAccount.profile',
                  defaultMessage: 'Facebook Account',
                })}
              </Typography.H5>
              <img
                className="fb-profile-msg-avatar"
                src={
                  profile?.profilePicture ||
                  'https://img.alicdn.com/imgextra/i4/O1CN01GynTF91hDnf6mtPpT_!!*************-2-tps-80-80.png'
                }
                alt=""
              />
              <a href={profile?.link}>{profile?.name}</a>
              <p>{profile?.email}</p>
            </Box>
            <div className="bind-button">
              {bindData?.lack && (
                <Button onClick={facebookAuth} type="primary">
                  {bindData?.lack ? StatusText.lack : StatusText.bind}
                </Button>
              )}
              <Button style={{ marginLeft: 20 }} onClick={deBindFacebook} type="primary">
                {StatusText.unBind}
              </Button>
            </div>
          </Box>
        </Case>
        <Case when={bindData?.bind && bindData?.expire}>
          <Box className="facebook-binding">
            <Typography.H4>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.title',
                defaultMessage: 'Social Account Binding',
              })}
            </Typography.H4>
            <p style={{ color: '#4F5669' }}>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.expireDesc',
                defaultMessage: 'Your Facebook account has expired, please re-bind',
              })}
            </p>
            <Box justify="center" align="center" className="fb-profile-msg">
              <Typography.H5>
                {i18n.formatMessage({
                  id: 'page.account.setting.bindAccount.profile',
                  defaultMessage: 'Facebook Account',
                })}
              </Typography.H5>
              <img
                className="fb-profile-msg-avatar"
                src={
                  profile?.profilePicture ||
                  'https://img.alicdn.com/imgextra/i4/O1CN01GynTF91hDnf6mtPpT_!!*************-2-tps-80-80.png'
                }
                alt=""
              />
              <a href={profile?.link}>{profile?.name}</a>
              <p>{profile?.email}</p>
              <Box justify="center" align="center">
                <Typography.H5>
                  {' '}
                  {i18n.formatMessage({
                    id: 'page.account.setting.bindAccount.bindInformation',
                    defaultMessage: 'We may need to access the following information:',
                  })}
                </Typography.H5>
                <p style={{ color: '#3A3A3A' }}>
                  {i18n.formatMessage({
                    id: 'page.account.setting.bindAccount.authority',
                    defaultMessage: 'Your friend list, post, videos, photos, likes and links',
                  })}
                </p>
              </Box>
            </Box>
            <div className="bind-button">
              <Button onClick={facebookAuth} type="primary">
                {StatusText.expire}
              </Button>
            </div>
          </Box>
        </Case>
        <Case when={!bindData?.bind}>
          <Box className="facebook-binding">
            <Typography.H4>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.title',
                defaultMessage: 'Social Account Binding',
              })}
            </Typography.H4>
            <p>
              {i18n.formatMessage({
                id: 'page.account.setting.bindAccount.bindDesc',
                defaultMessage: 'Bind your Facebook account to enjoy the benefits of Facebook login',
              })}
            </p>
            <Box justify="center" align="center">
              <img
                className="fb-icon"
                src="https://img.alicdn.com/imgextra/i1/O1CN012tPVsm1iOcdZzG5Pw_!!*************-55-tps-48-48.svg"
                alt=""
              />
              <Typography.H5>
                {' '}
                {i18n.formatMessage({
                  id: 'page.account.setting.bindAccount.bindInformation',
                  defaultMessage: 'We may need to access the following information:',
                })}
              </Typography.H5>
              <p style={{ color: '#3A3A3A' }}>
                {i18n.formatMessage({
                  id: 'page.account.setting.bindAccount.authority',
                  defaultMessage: 'Your friend list, post, videos, photos, likes and links',
                })}
              </p>
            </Box>
            <div className="bind-button">
              <Button onClick={facebookAuth} type="primary">
                {StatusText.bind}
              </Button>
            </div>
          </Box>
        </Case>
      </Switch>
    </Loading>
  );
};

export const useFacebookBindContent = (props): [React.FC<IDialogProps>, { onFacebookBind: showFunction }] => {
  const [FacebookDialog, { create }] = useFormDialog({
    content: FacebookContent,
    dialogProps: {
      title: '',
    },
    footer: false,
    locale: {
      successMessage: commonLocale['page.common.tips.success'],
      failMessage: commonLocale['page.common.tips.failed'],
    },
    onCancel: props.onCancel,
  });
  return [FacebookDialog, { onFacebookBind: create }];
};
