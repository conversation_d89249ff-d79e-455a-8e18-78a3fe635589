import { <PERSON>, <PERSON><PERSON>, Dialog, Message } from '@alifd/next';
import { Case, Switch, When } from '@alife/workstation-utils';
import { useRequest, useSetState } from 'ahooks';
import React, { useState } from 'react';
import commonLocale from '../../constants/common-locale';
import SmsCode from './sms-code';
import { getIdentificationList } from './service';
import { isEmpty } from 'lodash';

const CONST_ICONS = {
  topIcon: 'https://lzd-img-global.slatic.net/g/tps/tfs/TB1v0JvAmBYBeNjy0FeXXbnmFXa-300-300.png',
  mobileVerification: 'https://img.alicdn.com/imgextra/i1/O1CN01P2810q1UiP41Bpv1j_!!6000000002551-2-tps-32-32.png',
  emailVerification: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!*************-2-tps-38-28.png',
  confirmThroughEmail: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!*************-2-tps-38-28.png',
  confirmThroughSMS: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!*************-2-tps-38-28.png',
  confirmThroughDevice: 'https://img.alicdn.com/imgextra/i4/O1CN01JNZJKD24rrPPdTinE_!!*************-2-tps-38-28.png',
};

export enum EnumStep {
  'selectVerifyType',
  'emailVerification',
  'mobileVerification',
}

interface DialogProps {
  token: string;
  resolve: (value: string | PromiseLike<string>) => void;
  tokenType: string;
  dialog?: any;
}

export const VerificationIdentity: React.FC<DialogProps> = ({ token, resolve, tokenType, dialog }) => {
  const [step, setStep] = useState<number>(0);
  const [account, setAccount] = useState<string>();
  const [nationalCode, setNationalCode] = useState<string>();
  const [currentType, setCurrentType] = useSetState({ verifyType: '', otpType: '' });
  const { verifyType, otpType } = currentType;

  // 获取核身列表
  const { data } = useRequest(() => getIdentificationList({ token, tokenType }), {
    onError: (err: any) => {
      Message.error(err?.data?.errorCode?.displayMessage || 'unknown error');
    },
  });
  const userVerificationList = data?.userVerificationList ?? [];

  const VerificationButton = ({ type, href, accountPrefix, icon, item, account }) => {
    return (
      <div style={{ border: '1px solid #e5e5e5', borderRadius: 24, padding: '6px 20px' }}>
        <Button
          key={type}
          className="aplus-auto-exp aplus-auto-clk"
          data-spm={`d_${type}`}
          text
          component="a"
          href={href}
          onClick={() => {
            //请求接口
            setStep(EnumStep[type as string]);
            setAccount(account);
            setCurrentType({ verifyType: type, otpType: item?.type });
            setNationalCode(accountPrefix);
          }}
        >
          <img style={{ height: 20, fontSize: 20 }} src={icon ? icon : CONST_ICONS[type]} /> &nbsp;&nbsp;
          <span>{item?.text || account}</span>
        </Button>
      </div>
    );
  };

  return (
    <Box style={{ width: 460, minHeight: 360 }} align="center">
      <When condition={step === EnumStep['selectVerifyType']}>
        <Box direction="column" spacing={20} align="center">
          <img src={CONST_ICONS['topIcon']} style={{ width: 100 }} />
          <div>
            <div style={{ textAlign: 'center' }}>To protect your account security, we need to verify your identity</div>
            <div style={{ textAlign: 'center' }}>Please choose a way to verify:</div>
          </div>
          <Box direction="column" spacing={20}>
            {userVerificationList?.map(item => {
              const { type, href, account, icon, accountPrefix, deliveryType = [] } = item;
              return (
                <Switch>
                  <Case when={type === 'mobileVerification'}>
                    <Box direction="column" spacing={10}>
                      {!isEmpty(deliveryType)
                        ? deliveryType.map(x => (
                            <VerificationButton
                              type={type}
                              accountPrefix={accountPrefix}
                              item={x}
                              href={href}
                              icon={icon}
                              account={account}
                            />
                          ))
                        : null}
                    </Box>
                  </Case>
                  <Case when={type === 'emailVerification'}>
                    <VerificationButton
                      type={type}
                      href="javascript:;"
                      icon={
                        'https://img.alicdn.com/imgextra/i1/O1CN01fJXoEw1S29QFsATuu_!!*************-55-tps-12-12.svg'
                      }
                      item={{ text: 'Email', type: 'email' }}
                      accountPrefix=""
                      account={account}
                    />
                  </Case>
                  <Case when={type !== 'emailVerification' && type !== 'mobileVerification'}>
                    <div style={{ border: '1px solid #e5e5e5', borderRadius: 24, padding: '5px 20px' }}>
                      <Button
                        key={type}
                        className="aplus-auto-exp aplus-auto-clk"
                        data-spm={`d_${type}`}
                        text
                        onClick={() => {
                          window.open(href);
                        }}
                        component="a"
                        href={href}
                      >
                        <img style={{ width: 30 }} src={icon ? icon : CONST_ICONS[type]} /> &nbsp;&nbsp;
                        <span>{account}</span>
                      </Button>
                    </div>
                  </Case>
                </Switch>
              );
            })}
          </Box>
        </Box>
      </When>
      <When condition={step === EnumStep['emailVerification'] || step === EnumStep['mobileVerification']}>
        <div style={{ width: 420 }}>
          <h1> {commonLocale['page.LZDlogin.enterCode']} </h1>
          <SmsCode
            account={account}
            verifyType={verifyType}
            otpType={otpType}
            token={token}
            resolve={resolve}
            close={!isEmpty(dialog) && dialog['show'].hide}
            onBack={() => {
              setStep(EnumStep['selectVerifyType']);
            }}
            nationalCode={nationalCode}
          />
        </div>
      </When>
    </Box>
  );
};

/** 函数调用方式 */
export const createVerificationIdentityDialog = ({ token, tokenType, resolve }: DialogProps) => {
  const dialog = {};
  dialog['show'] = Dialog.show({
    hasMask: false,
    footer: false,
    content: <VerificationIdentity token={token} tokenType={tokenType} resolve={resolve} dialog={dialog} />,
    onClose: () => {
      resolve('Please verify it again');
    },
  });
};
