import { axios } from '@alife/workstation-utils';

export interface IdentificationMap {
  account: string;
  accountPrefix: string;
  name: string;
  type: string;
  deliveryType: {
    icon: string;
    text: string;
    type: string;
    supportOneClickVerify: boolean;
  }[];
  icon: string;
  isFold: boolean;
  tokenType: string;
  href: string;
}
export const getIdentificationList = (params: {
  token: string;
  tokenType: string;
}): Promise<{ userVerificationList: IdentificationMap[] }> => {
  return axios({
    url: 'mtop.global.seller.identification.getidentificationlist',
    params: { platform: 'pc', ...params },
  }).then(({ data }) => data);
};

export const sendSmsCode = (params: { token: string; deliveryType: string }) => {
  return axios({
    url: 'mtop.global.seller.verificationcode.sendsms',
    params: { platform: 'pc', type: 'ACCOUNT_SECOND_VERIFICATION', ...params },
  }).then(({ data }) => data);
};

export const sendEmailCode = (params: { token: string }) => {
  return axios({
    url: 'mtop.global.seller.verificationCode.sendEmail',
    params: { platform: 'pc', type: 'ACCOUNT_SECOND_VERIFICATION', ...params },
  }).then(({ data }) => data);
};

export const checkSmsCode = (params: { token: string; code: string }) => {
  return axios({
    url: 'mtop.global.seller.identification.verifyidentificationotpcode',
    params: { platform: 'pc', type: 'ACCOUNT_SECOND_VERIFICATION', ...params },
  }).then(({ data }) => data);
};

export const checkEmailCode = (params: { token: string; code: string }) => {
  return axios({
    url: 'mtop.global.seller.identification.verifyIdentificationEmailCode',
    params: { platform: 'pc', type: 'ACCOUNT_SECOND_VERIFICATION', ...params },
  }).then(({ data }) => data);
};
