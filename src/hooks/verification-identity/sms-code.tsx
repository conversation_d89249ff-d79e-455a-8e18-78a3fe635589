import React, { useRef, useState, useEffect } from 'react';
import { Form, Button, Input, Box } from '@alifd/next';
import commonLocale from '../../constants/common-locale';
import { useRequest, useCountDown } from 'ahooks';
import { checkEmailCode, checkSmsCode, sendEmailCode, sendSmsCode } from './service';
import { setCookie } from '@alife/workstation-utils';
import { createVerificationIdentityDialog } from '.';
import './index.scss';

interface IProps {
  verifyType: string;
  account?: string | number;
  otpType: string;
  nationalCode?: string;
  token: string;
  onBack: () => void;
  close: () => void;
  resolve: (value: string | PromiseLike<string>) => void;
}

const COUNT_DOWN_TIME = 60000;
const MAX_FOCUS_IDX = 5;
const COUNT_NUM = 6;
const NUMBER_REG = /^[0-9]*$/;

export const SmsCode: React.FC<IProps> = props => {
  const { account, nationalCode, verifyType, token, onBack, close, resolve, otpType } = props;
  const [current, setCurrent] = useState<string>('');
  const [targetDate, setTargetDate] = useState<number>();
  const [countdown] = useCountDown({ targetDate });
  const [curIdx, setCurIdx] = useState<number>(0);
  const [errorMsg, setErrorMsg] = useState('');
  let numberInput = useRef<HTMLInputElement>(null);
  const fakeInputs = new Array(COUNT_NUM).fill(0);

  const { run: sendSms } = useRequest(sendSmsCode, {
    manual: true,
    onError: (err: any) => {
      const { data: resp } = err;
      setErrorMsg(resp?.data?.errorCode?.displayMessage || err || 'unknown error');
    },
  });

  const { run: sendEmail } = useRequest(sendEmailCode, {
    manual: true,
    onError: (err: any) => {
      const { data: resp } = err;
      setErrorMsg(resp?.data?.errorCode?.displayMessage || err || 'unknown error');
    },
  });

  const { run: checkSms, loading: checkSmsLoading } = useRequest(checkSmsCode, {
    manual: true,
    onError: (err: any) => {
      const { data: resp } = err;
      setErrorMsg(resp?.data?.errorCode?.displayMessage || err || 'unknown error');
    },
    onSuccess: (res: any) => {
      const { token, tokenType, success } = res;
      if (success) {
        if (tokenType === 'ACCOUNT_SECOND_VERIFICATION') {
          close();
          createVerificationIdentityDialog({ resolve, token, tokenType });
        } else {
          setCookie('token', '');
          setCookie('token', res.token);
          resolve(''); //二次验证成功
          close();
        }
      } else {
        setErrorMsg(res.message);
      }
    },
  });

  const { run: checkEmail, loading: checkEmailLoading } = useRequest(checkEmailCode, {
    manual: true,
    onError: (err: any) => {
      const { data: resp } = err;
      setErrorMsg(resp?.data?.errorCode?.displayMessage || err || 'unknown error');
    },
    onSuccess: (res: any) => {
      const { token, tokenType, success } = res;
      if (success) {
        if (tokenType === 'ACCOUNT_SECOND_VERIFICATION') {
          close();
          createVerificationIdentityDialog({ resolve, token, tokenType });
        } else {
          setCookie('token', '');
          setCookie('token', res.token);
          resolve(''); //二次验证成功
          close();
        }
      } else {
        setErrorMsg(res.message);
      }
    },
  });

  useEffect(() => {
    //进来就发送验证
    setTargetDate(Date.now() + COUNT_DOWN_TIME);
    sendCode();

    autoFocus();
    setCurIdx(0);
    document.addEventListener('click', autoFocus);
    return () => document.removeEventListener('click', autoFocus);
  }, []);

  const autoFocus = () => {
    numberInput && numberInput?.current?.focus();
  };

  // 发送验证码
  const sendCode = () => {
    if (verifyType === 'mobileVerification') {
      sendSms({ token, deliveryType: otpType });
    }
    if (verifyType === 'emailVerification') {
      sendEmail({ token });
    }
  };

  // 校验验证码
  const checkCode = () => {
    if (verifyType === 'mobileVerification') {
      checkSms({ token, code: current });
    }
    if (verifyType === 'emailVerification') {
      checkEmail({ token, code: current });
    }
  };

  const onChangeInput = value => {
    setErrorMsg('');
    if (!NUMBER_REG.test(value)) return;
    const countIdx = value.length - 1;
    if (!value) {
      setCurIdx(0);
    } else if (value && countIdx !== curIdx) {
      const tempArr = value.split('');
      const appendNum = tempArr.pop();
      tempArr.splice(curIdx, 1, appendNum);
      value = tempArr.join('');
      const step = countIdx > curIdx ? 1 : -1;
      setCurIdx(curIdx + step);
    } else {
      setCurIdx(Math.min(curIdx + 1, MAX_FOCUS_IDX));
    }

    setCurrent(value);
  };

  const onClick = item => {
    const cur = Number(item.target.id);
    autoFocus();
    setCurIdx(cur);
  };

  return (
    <div className="sms-code-container">
      <div className="sms-code-container-tips">
        {commonLocale['page.account.setting.Enter6digit']}&nbsp;
        <span className="sms-code-container-tips-number">
          {account && NUMBER_REG.test(account as string) ? `+ ${nationalCode} ${account}` : account}
        </span>
      </div>
      <div className="row-form">
        <Form>
          <div className="login-form">
            <Input
              data-spm="sms_code_input"
              className="hidden-ipt"
              ref={numberInput as any}
              hasBorder={false}
              onChange={onChangeInput}
              value={current}
              trim={true}
              cutString={true}
              maxLength={6}
              type="hidden"
            ></Input>
            <div className="number-wrp">
              {fakeInputs.map((item, index) => {
                return (
                  <div
                    data-spm="d_sign_up_resend"
                    onClick={onClick}
                    id={`${index}`}
                    className={`number-wrp-ipt index${index} ${curIdx === index ? 'highlight' : ''} ${
                      errorMsg ? 'error-light' : ''
                    }`}
                  >
                    {current[index] ? current[index] : ''}
                  </div>
                );
              })}
            </div>
            {errorMsg ? <div className="error-message">{errorMsg}</div> : null}
            <div className="sms-code-container-tips">
              <span className="sms-code-container-tips-receive">
                {commonLocale['page.account.setting.notReceiveTheCode']}
              </span>
              &nbsp;
              <Button
                text
                disabled={countdown > 0}
                onClick={() => {
                  setTargetDate(Date.now() + COUNT_DOWN_TIME);
                  sendCode();
                }}
              >
                {countdown === 0
                  ? commonLocale['page.common.btn.send']
                  : commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}
              </Button>
            </div>
            <Box spacing={20}>
              <Button
                type="primary"
                className="aplus-auto-exp aplus-auto-clk check-btn"
                data-spm="d_sms_code_submit"
                loading={checkSmsLoading || checkEmailLoading}
                onClick={() => {
                  checkCode();
                }}
              >
                {commonLocale['page.common.btn.next']}
              </Button>
              <Button
                type="normal"
                className="btn aplus-auto-exp aplus-auto-clk plain back-btn"
                data-spm="sms_code_back"
                onClick={onBack}
              >
                {commonLocale['page.common.btn.previous']}
              </Button>
            </Box>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default SmsCode;
