import { Button, Input, Loading, Message } from '@alifd/next';
import * as i18n from '@alife/lazada-i18n';
import { axios } from '@alife/workstation-utils';
import { useCountDown, useMemoizedFn, useRequest } from 'ahooks';
import React, { useState } from 'react';
import commonLocale from '../../constants/common-locale';

export enum OTP_TYPES {
  PHONE = 'phone',
  EMAIL = 'email',
}

interface IOtpSendParams {
  email?: string;
  phone?: string;
  fingerPrint?: string;
  verifyCode?: string;
  nationCode?: string;
  bizCode: string;
}

interface IOtpInfo extends IOtpSendParams {
  otpType: OTP_TYPES;
  nationCode?: string;
  masterNationCode?: string;
  countryNationCode?: string;
}

type IProps = {
  bizCode: string;
  onChange?: (option: IOtpInfo) => void;
  fingerPrint?: string;
};
const VerificationCode = (props: IProps) => {
  // const [countdown, setTargetDate] = useCountDown();
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      // alert('End of the time');
    },
  });
  const [verifyCode, setVerifyCode] = useState('');
  const [otpType, setOtpType] = useState(OTP_TYPES.PHONE);

  const { data: optInfo = {}, loading } = useRequest(
    () =>
      axios
        .get('mtop.global.merchant.subaccount.otp.userinfo')
        .then(({ data: res }) => {
          if (!res.data.phone) {
            setOtpType(OTP_TYPES.EMAIL);
          }
          return res.data;
        })
        .catch(() => {}),
    {},
  );

  const onChange = useMemoizedFn(options => {
    const res: IOtpInfo = {
      verifyCode,
      otpType,
      phone: optInfo.phone,
      email: optInfo.email,
      nationCode: optInfo.nationCode,
      masterNationCode: optInfo.masterNationCode,
      countryNationCode: optInfo.countryNationCode,
      bizCode: props.bizCode,
      fingerPrint: props.fingerPrint,
    };

    props.onChange?.({ ...res, ...options });
  });

  const updateOptType = useMemoizedFn(newOptType => {
    setOtpType(newOptType);
    onChange({ otpType: newOptType });
  });

  const onCodeChange = useMemoizedFn(v => {
    // console.log('----');
    setVerifyCode(v);
    onChange({ verifyCode: v });
  });

  const sendCode = useMemoizedFn(() => {
    const umidtoken = AWSC?.configFY();
    const data: IOtpSendParams = {
      bizCode: props.bizCode,
    };

    if (otpType === OTP_TYPES.PHONE) {
      data.phone = optInfo.phone;
      data.fingerPrint = props.fingerPrint ?? optInfo.phone;
      data.nationCode = optInfo.nationCode?.toString().replace(/\+/, '');
    } else {
      data.email = optInfo.email;
      data.fingerPrint = props.fingerPrint ?? optInfo.email;
    }

    return axios
      .get('mtop.global.merchant.subaccount.otp.code.send', {
        data,
        headers: {
          'x-ua': umidtoken?.getUA(),
          'x-umidtoken': umidtoken?.umidToken,
          'x-osversion': umidtoken?.uabModule?.getVersion(),
          'x-entrance': 'pc',
        },
      })
      .then(res => {
        if (res?.data?.data) {
          setTargetDate(Date.now() + 60000);
        } else {
          Message.error(
            i18n.formatMessage({
              id: 'page.common.systemBusy',
              defaultMessage: 'System busy, please refresh and try again',
            }),
          );
        }
      })
      .catch(e => {
        const message = e.data?.data?.error || e.message;
        Message.error(message);
      });
  });

  return (
    <div>
      {otpType === OTP_TYPES.PHONE ? (
        <div>
          <div>
            {i18n.formatMessage({
              id: 'page.account.setting.yourCurrentLoginPhone',
              defaultMessage: 'Your current login phone',
            })}
          </div>
          <div>
            {optInfo.nationCode} {optInfo.phone}
          </div>
        </div>
      ) : (
        <div>
          <div>
            {i18n.formatMessage({
              id: 'page.account.setting.yourCurrentLoginEmail',
              defaultMessage: 'Your current login email',
            })}
          </div>
          <div>{optInfo.email}</div>
        </div>
      )}

      <Loading visible={loading}>
        <div>
          <div>{commonLocale['page.account.setting.verificationCode']}</div>
          <Input
            value={verifyCode}
            placeholder={commonLocale['page.LZDlogin.enterCode']}
            onChange={onCodeChange}
            className="mr10"
          />
          <Button onClick={sendCode} disabled={countdown !== 0} className="aplus-auto-exp" data-spm="d_code_opt_send">
            {countdown === 0
              ? commonLocale['page.common.btn.send']
              : commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}
          </Button>
        </div>
      </Loading>
      {optInfo.phone && optInfo.email ? (
        <div>
          {otpType === OTP_TYPES.PHONE ? (
            <Button
              onClick={() => {
                updateOptType(OTP_TYPES.EMAIL);
              }}
              className="aplus-auto-exp"
              data-spm="d_switch_verify"
              text
            >
              <img
                className="opt-switcher-icon"
                src="https://img.alicdn.com/tfs/TB1qVa3UQL0gK0jSZFAXXcA9pXa-60-60.png"
                alt=""
              />

              <span>
                {i18n.formatMessage({
                  id: 'page.account.setting.verifyViaLoginEmailAddress',
                  defaultMessage: 'Verify via login email address',
                })}
              </span>
            </Button>
          ) : (
            <Button
              onClick={() => {
                updateOptType(OTP_TYPES.PHONE);
              }}
              text
            >
              <img
                className="opt-switcher-icon"
                src="https://img.alicdn.com/tfs/TB1t8qPUNz1gK0jSZSgXXavwpXa-60-60.png"
                alt=""
              />

              <span>
                {i18n.formatMessage({
                  id: 'page.account.setting.verifyViaLoginPhoneAddress',
                  defaultMessage: 'Verify via login phone address',
                })}
              </span>
            </Button>
          )}
        </div>
      ) : null}
    </div>
  );
};

VerificationCode.validator = (opt: IOtpInfo): Promise<string> => {
  return new Promise(resolve => {
    if (!opt) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    const data = {
      bizCode: opt.bizCode,
      [opt.otpType]: opt[opt.otpType],
      fingerPrint: opt?.fingerPrint ?? opt[opt.otpType],
      verifyCode: opt.verifyCode,
    };

    if (!data.fingerPrint) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    if (!data.verifyCode) {
      return resolve(commonLocale['page.common.tips.required']);
    }
    return axios
      .post('mtop.global.merchant.subaccount.otp.code.verify', data)
      .then((res: any): void => {
        if (res.success) {
          resolve('');
        } else {
          resolve(res.message);
        }
      })
      .catch(e => {
        console.error(e);
        resolve(e.data?.data?.error || e.message);
      });
  });
};

export default VerificationCode;
