import * as i18n from '@alife/lazada-i18n';
import React from 'react';

const ContactReview = props => {
  const prefixText = props.prefix ? `${props.prefix} ` : '';
  debugger;
  return (
    <div>
      <div>
        <span className="field-label mr10">
          {i18n.formatMessage({ id: 'page.account.setting.oldContact', defaultMessage: 'Old Contact' })}:
        </span>
        {props.oldContact ? `${prefixText}${props.oldContact}` : '--'}
      </div>
      <div>
        <span className="field-label mr10">
          {i18n.formatMessage({ id: 'page.account.setting.newContact', defaultMessage: 'New Contact' })}:
        </span>
        {props.isUpdatePhone ? `${props.countryNationCode} ` : null}
        {props.newContact}
      </div>
    </div>
  );
};

export default ContactReview;
