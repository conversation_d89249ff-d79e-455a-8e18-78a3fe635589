import { Button, Message } from '@alifd/next';
import { IDialogConfig, IDialogProps, showFunction, useFormDialog } from '@alife/asc-components';
import { withTrackEffects } from '@alife/form-track-plugin';
import * as i18n from '@alife/lazada-i18n';
import { axios, getCookie, request, setCookie } from '@alife/workstation-utils';
import { createForm } from '@formily/core';
import { Form, FormButtonGroup, FormItem, FormLayout, FormStep, Input } from '@formily/next';
import { FormConsumer, createSchemaField } from '@formily/react';
import { useCreation, useMemoizedFn } from 'ahooks';
import React, { useEffect, useState } from 'react';
import commonLocale from '../../constants/common-locale';
import { EnumOtpType } from '../../pages/account/constants';
import VerificationCode from '../identify_verify/verification-code';
import NewPassword from './new-password';

// 由于有多个 OTP 流程，各个流程的接口不能复用，
// 这里把更新联系方式的 OTP 流程的组件全部放在一起
// 避免跟其他流程混淆

// udpate Login password OTP bizcode
const OTP_BIZ_CODE = 'verifyAccountCredential';

const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    VerificationCode,
    NewPassword,
  },
});

const PasswordEditContent = props => {
  const [tokenType, setTokenType] = useState('');
  useEffect(() => {
    axios.get('mtop.global.seller.member.getuserinfo').then(res => {
      formContact.setValues({
        newPassword: {
          ...formContact.values?.newPassword,
          hasPassword: res.data?.hasPassword,
        },
      });
      const currentTokenType = res.data?.hasPassword ? 'CHANGE_PASSWORD' : 'RESET_PASSWORD';
      setTokenType(currentTokenType);
      setToken(currentTokenType);
    });
  }, []);
  const setToken = type => {
    const params = {
      tokenType: type,
    };
    return request({
      url: 'mtop.global.seller.identification.verificationToken',
      data: params,
      v: '1.1',
      method: 'POST',
    }).then(({ data }) => {
      const { token } = data?.module;
      setCookie('token', '');
      setCookie('token', token);
    });
  };
  const [formContact, formStep, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'update_account_password',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
        },
      }),
      FormStep.createFormStep(),
      setError,
    ];
  }, [props.record?.name]);

  const onSubmit = useMemoizedFn(() => {
    const token = getCookie('token');
    if (tokenType === 'CHANGE_PASSWORD') {
      axios
        .post(
          'mtop.global.seller.passwordManage.changePassword',
          {
            oldPlainPassword: formContact.values?.newPassword?.oldpassword,
            newPlainPassword: formContact.values?.newPassword?.password,
            token: token,
          },
          {
            isSec: true,
          },
        )
        .then(() => {
          props.ok();
        })
        .catch(({ data: resp }) => {
          const message = resp?.data?.errorCode?.displayMessage;
          formContact.setValues({
            newPassword: {
              ...formContact.values?.newPassword,
              errorMessage: message,
            },
          });
          Message.error(message || 'unknown error');
          setTrackerError(message);
        });
    } else {
      axios
        .post('mtop.global.seller.passwordManage.completeResetPassword', {
          token: token,
          password: formContact.values?.newPassword?.password,
          platform: 'pc',
        })
        .then(() => {
          props.ok();
        })
        .catch(({ data: resp }) => {
          const message = resp?.data?.errorCode?.displayMessage;
          formContact.setValues({
            newPassword: {
              ...formContact.values?.newPassword,
              errorMessage: message,
            },
          });
          Message.error(message || 'unknown error');
          setTrackerError(message);
        });
    }
  });
  return (
    <div data-spm="login_password_dialog">
      <Form form={formContact} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical">
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.pleaseVerifyYourAccount',
                    defaultMessage: 'Please verify your account',
                  }),
                }}
              >
                <SchemaField.String
                  name="verification"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationCode.validator,
                  }}
                  x-component="VerificationCode"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                    updateType: EnumOtpType.PASSWORD,
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                type="void"
                x-component="FormStep.StepPane"
                x-component-props={{
                  bizCode: OTP_BIZ_CODE,
                  updateType: 'password',
                  title: i18n.formatMessage({
                    id: 'page.account.setting.modifyThePassword',
                    defaultMessage: 'Modify the Password',
                  }),
                }}
              >
                <SchemaField.String
                  name="newPassword"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: NewPassword.validator,
                  }}
                  x-component="NewPassword"
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
        </FormLayout>
        <FormConsumer>
          {() => (
            <FormButtonGroup className="dialog-footer-btn-group">
              <Button
                type="primary"
                disabled={!formStep.allowNext}
                loading={formContact.validating}
                onClick={() => {
                  formStep.next();
                }}
              >
                {commonLocale['page.common.btn.next']}
              </Button>
              <Button
                type="primary"
                loading={formContact.submitting}
                disabled={formStep.allowNext}
                onClick={() => {
                  formStep.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </div>
  );
};

export const useUpdateLoginPassword = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onPasswordEdit: showFunction }] => {
  const [PasswordDialog, { view }] = useFormDialog({ content: PasswordEditContent, ...config });
  return [PasswordDialog, { onPasswordEdit: view }];
};
