import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Input, Button, Message } from '@alifd/next';
import { useMemoizedFn } from 'ahooks';
import { useCountDown } from 'ahooks';
import { axios } from '@alife/workstation-utils';
import commonLocale from '../../constants/common-locale';

interface IOtpSendParams {
  email?: string;
  phone?: string;
  verifyCode: string;
  otpType: string;
  bizCode: string;
}

const VerificationContact = props => {
  // const [countdown, setTargetDate] = useCountDown();
  const [targetDate, setTargetDate] = useState<number>();

  const [countdown] = useCountDown({
    targetDate,
    onEnd: () => {
      // alert('End of the time');
    },
  });
  const [verifyCode, setVerifyCode] = useState('');
  const [inputValue, setInputValue] = useState('');

  const otpType = props.isUpdatePhone ? 'phone' : 'email';
  const getPhoneEmailValue = useMemoizedFn((currentValue?) => ({
    [otpType]: currentValue || inputValue,
  }));

  const onChange = useMemoizedFn(options => {
    const res = { verifyCode, otpType, bizCode: props.bizCode };
    props.onChange({ ...res, ...getPhoneEmailValue(), ...options });
  });

  const onCodeChange = useMemoizedFn(v => {
    setVerifyCode(v);
    onChange({ verifyCode: v });
  });

  const onFingerPrintChange = useMemoizedFn(v => {
    setInputValue(v);
    onChange(getPhoneEmailValue(v));
  });

  const sendCode = useMemoizedFn(() => {
    const umidtoken = AWSC?.configFY();
    if (!inputValue) {
      Message.error(
        props.isUpdatePhone
          ? i18n.formatMessage({
              id: 'page.account.setting.phoneNumberIsRequired',
              defaultMessage: 'Phone Number is required',
            })
          : i18n.formatMessage({ id: 'page.account.setting.emailIsRequired', defaultMessage: 'Email is required' }),
      );

      return;
    }

    const data = {
      bizCode: props.bizCode,
      fingerPrint: inputValue,
      [props.isUpdatePhone ? 'phone' : 'email']: inputValue,
      nationCode: props.countryNationCode?.toString().replace(/\+/, ''),
    };
    return axios
      .get('mtop.global.merchant.subaccount.otp.code.send', {
        data,
        headers: {
          'x-ua': umidtoken?.getUA(),
          'x-umidtoken': umidtoken?.umidToken,
          'x-osversion': umidtoken?.uabModule?.getVersion(),
          'x-entrance': 'pc',
        },
      })
      .then(() => {
        setTargetDate(Date.now() + 60000);
      })
      .catch(e => {
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        console.error(e);
      });
  });

  const phonePlaceholder =
    parseInt(props.countryNationCode) !== 66
      ? i18n.formatMessage({
          id: 'page.LZDregister.sms.phone.placeholder',
          defaultMessage: 'Phone number',
        })
      : '08XXXXXXXX';
  const emailPlaceholder = i18n.formatMessage({
    id: 's-wb-subaccount@email',
    defaultMessage: 'Email',
    app: 's-wb-subaccount',
  });
  const inputPlaceHolder = props.isUpdatePhone ? phonePlaceholder : emailPlaceholder;
  const fieldLable =
    props.fieldLabel ||
    (props.isUpdatePhone
      ? i18n.formatMessage({ id: 'page.account.setting.newPhoneNumber', defaultMessage: 'New Phone Number' })
      : i18n.formatMessage({ id: 'page.account.setting.newEmail', defaultMessage: 'New Email' }));

  return (
    <div>
      <div>
        <div>{fieldLable}</div>
        <div style={{ width: 265 }}>
          <Input
            addonTextBefore={props.isUpdatePhone ? props.countryNationCode : null}
            value={inputValue}
            onChange={onFingerPrintChange}
            placeholder={inputPlaceHolder}
          />
        </div>
      </div>
      <div>
        <div>{commonLocale['page.account.setting.verificationCode']}</div>
        <Input
          value={verifyCode}
          placeholder={commonLocale['page.LZDlogin.enterCode']}
          onChange={onCodeChange}
          className="mr10"
        />
        <Button onClick={sendCode} disabled={countdown !== 0} className="aplus-auto-exp" data-spm="d_contact_otp_send">
          {countdown === 0
            ? commonLocale['page.common.btn.send']
            : commonLocale['page.common.btn.resendAfter']({ time: Math.round(countdown / 1000) })}
        </Button>
      </div>
    </div>
  );
};

VerificationContact.validator = (opt: IOtpSendParams) => {
  return new Promise(resolve => {
    if (!opt) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    const data: any = {
      bizCode: opt.bizCode,
      [opt.otpType]: opt[opt.otpType],
      fingerPrint: opt[opt.otpType],
      verifyCode: opt.verifyCode,
    };

    if (!data.fingerPrint) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    if (!data.verifyCode) {
      return resolve(commonLocale['page.common.tips.required']);
    }
    return axios
      .post('mtop.global.merchant.subaccount.otp.code.verify', data)
      .then((res: any) => {
        if (res.success) {
          resolve('');
        } else {
          resolve(res.message);
        }
      })
      .catch(e => {
        console.error(e);
        resolve(e.data?.data?.error || e.message);
      });
  });
};

export default VerificationContact;
