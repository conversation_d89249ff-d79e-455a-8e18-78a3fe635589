import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Input } from '@alifd/next';
import commonLocale from '../../constants/common-locale';

const NewPassword = props => {
  const [password, setPassword] = useState('');
  const [oldpassword, setOldPassword] = useState('');
  const [repeatPassword, setRepeatPassword] = useState('');

  const onNewPasswordChange = e => {
    setPassword(e);
    props.onChange({ ...props.value, password: e, oldpassword, repeatPassword });
  };

  const onOldPasswordChange = e => {
    setOldPassword(e);
    props.onChange({ ...props.value, password, oldpassword: e, repeatPassword, errorMessage: '' });
  };

  const onRepeatPasswordChange = e => {
    setRepeatPassword(e);
    props.onChange({ ...props.value, password, oldpassword, repeatPassword: e });
  };

  return (
    <div>
      {props.value?.hasPassword && (
        <div>
          <div>{i18n.formatMessage({ id: 'page.account.setting.oldPassword', defaultMessage: 'Old Password' })}</div>
          <Input.Password value={oldpassword} onChange={onOldPasswordChange} />
        </div>
      )}
      <div>
        <div>{i18n.formatMessage({ id: 'page.account.setting.newPassword', defaultMessage: 'New Password' })}</div>
        <Input.Password value={password} onChange={onNewPasswordChange} />
      </div>
      <div>
        <div>
          {i18n.formatMessage({ id: 'page.account.setting.repeatPassword', defaultMessage: 'Repeat Password' })}
        </div>
        <Input.Password value={repeatPassword} onChange={onRepeatPasswordChange} />
      </div>
    </div>
  );
};

NewPassword.validator = (opt: any = {}) => {
  return new Promise(resolve => {
    if (!opt?.password) {
      return resolve(commonLocale['page.common.tips.required']);
    }

    if (opt.repeatPassword !== opt.password && opt.hasPassword) {
      return resolve('Repeat Password not same with new password');
    }

    // if (opt.errorMessage) {
    //   return resolve(opt.errorMessage);
    // }

    return resolve('');
  });
};

export default NewPassword;
