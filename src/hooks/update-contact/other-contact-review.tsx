import * as i18n from '@alife/lazada-i18n';
import React from 'react';

const OtherContactReview = props => {
  return (
    <div>
      <div>
        <span className="field-label mr10">
          {i18n.formatMessage({ id: 'page.account.setting.updateContact', defaultMessage: 'Update Contact' })}:
        </span>
        {props.isUpdatePhone ? `${props.countryNationCode} ` : null}
        {props.newContact}
      </div>
    </div>
  );
};

export default OtherContactReview;
