import * as i18n from '@alife/lazada-i18n';
import React, { useEffect } from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction } from '@alife/asc-components';
import { axios, request, setCookie } from '@alife/workstation-utils';
import { useCreation, useMemoizedFn } from 'ahooks';
import { createForm, onFormMount } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { FormConsumer, createSchemaField } from '@formily/react';
import { Button, Message } from '@alifd/next';
import { Form, FormStep, FormItem, Input, FormLayout, FormButtonGroup } from '@formily/next';

import commonLocale from '../../constants/common-locale';
import VerificationContact from '../identify_verify/verification-contact';
import VerificationCode from '../identify_verify/verification-code';
import ContactReview from './contact-review';
import { EnumOtpType } from '../../pages/account/constants';

// 由于有多个 OTP 流程，各个流程的接口不能复用，
// 这里把更新联系方式的 OTP 流程的组件全部放在一起
// 避免跟其他流程混淆

// update Login Account OTP bizcode
const OTP_BIZ_CODE = 'verifyAccountCredential';
const OTP_BIZ_NEW_CODE = 'verifyNewCredential';
const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    VerificationCode,
    VerificationContact,
    ContactReview,
  },
});

const ContactEditContent = props => {
  const record = props.record;
  const phoneFields = ['phone', 'individual_phone'];
  const isUpdatePhone = phoneFields.includes(record.fieldName);

  useEffect(() => {
    setToken();
  }, []);

  const setToken = () => {
    const params = {
      tokenType: isUpdatePhone ? 'CHANGE_PHONE' : 'CHANGE_EMAIL',
      platform: 'pc',
    };
    return request({
      url: 'mtop.global.seller.identification.verificationtoken',
      data: params,
      v: '1.1',
      method: 'POST',
    })
      .then(({ data }) => {
        const token = data.module?.token;
        setCookie('token', '');
        setCookie('token', token);
      })
      .catch(e => {
        console.error(e);
      });
  };

  const [formContact, formStep, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'update_account_contact',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
          onFormMount(() => {});
        },
      }),
      FormStep.createFormStep(),
      setError,
    ];
  }, [record?.name]);

  const onSubmit = useMemoizedFn(() => {
    // profile取countryNationCode, setting的loginphone 展示取nationCode，但触发修改的话，强制他只能改到countryNationCode
    let updateService;
    if (isUpdatePhone) {
      updateService = axios.post('mtop.global.merchant.account.setting.phone.update', {
        newPhone: formContact.values?.newContact?.phone,
        nationCode: formContact.values?.verification?.countryNationCode?.toString().replace(/\+/, ''),
      });
    } else {
      updateService = axios.post('mtop.global.merchant.account.setting.email.update', {
        newEmail: formContact.values?.newContact?.email,
      });
    }

    return updateService
      .then(() => {
        props.ok();
      })
      .catch(e => {
        console.error(e);
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        setTrackerError(message);
      });
  });

  return (
    <div data-spm="login_contact_dialog">
      <Form form={formContact} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical">
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.pleaseVerifyYourAccount',
                    defaultMessage: 'Please verify your account',
                  }),
                }}
              >
                <SchemaField.String
                  name="verification"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationCode.validator,
                  }}
                  x-component="VerificationCode"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                    updateType: isUpdatePhone ? EnumOtpType.PHONE : EnumOtpType.EMAIL,
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: isUpdatePhone
                    ? i18n.formatMessage({
                        id: 'page.account.setting.changeThePhone',
                        defaultMessage: 'Change the phone',
                      })
                    : i18n.formatMessage({
                        id: 'page.account.setting.changeTheEmail',
                        defaultMessage: 'Change the email',
                      }),
                }}
              >
                <SchemaField.String
                  name="newContact"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: otp => VerificationContact.validator(otp, props.ok),
                  }}
                  x-component="VerificationContact"
                  x-component-props={{
                    isUpdatePhone,
                    bizCode: OTP_BIZ_NEW_CODE,
                    countryNationCode: '',
                  }}
                  x-reactions={field => {
                    field.componentProps.countryNationCode = formContact?.values?.verification?.countryNationCode;
                  }}
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
        </FormLayout>
        <FormConsumer>
          {() => (
            <FormButtonGroup className="dialog-footer-btn-group">
              <Button
                disabled={!formStep.allowBack}
                onClick={() => {
                  formStep.back();
                  setToken(); //返回重新settoken
                  formContact.setValuesIn('verification', {});
                }}
              >
                {commonLocale['page.common.btn.previous']}
              </Button>
              <Button
                type="primary"
                // disabled={!formStep.allowNext}
                loading={formContact.validating}
                onClick={() => {
                  formStep.next();
                }}
              >
                {formStep.allowNext ? commonLocale['page.common.btn.next'] : commonLocale['page.common.btn.submit']}
              </Button>
              {/* <Button
                type="primary"
                loading={formContact.submitting}
                disabled={formStep.allowNext}
                onClick={() => {
                  formStep.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button> */}
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </div>
  );
};

export const useUpdateLoginContact = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onContactEdit: showFunction }] => {
  const [ContactDialog, { view }] = useFormDialog({
    content: ContactEditContent,
    ...config,
    locale: {
      successMessage: commonLocale['page.common.tips.success'],
      failMessage: commonLocale['page.common.tips.failed'],
    },
  });

  return [ContactDialog, { onContactEdit: view }];
};
