import * as i18n from '@alife/lazada-i18n';
import React from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import { createForm } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { FormConsumer, createSchemaField } from '@formily/react';
import { Button, Message } from '@alifd/next';
import { Form, FormStep, FormItem, Input, FormLayout, FormButtonGroup } from '@formily/next';

import commonLocale from '../../constants/common-locale';
import VerificationContact from './verification-contact';
import OtherContactReview from './other-contact-review';
import VerificationCode from './verification-code';
import { useCreation, useMemoizedFn } from 'ahooks';

// 由于有多个 OTP 流程，各个流程的接口不能复用，
// 这里把更新联系方式的 OTP 流程的组件全部放在一起
// 避免跟其他流程混淆

// udpate Seller Account OTP bizcode
const OTP_BIZ_CODE = 'verifyAccountCredential';
const OTP_BIZ_NEW_CODE = 'verifyNewCredential';
const ContactEditContent = props => {
  const record = props.record;
  const { optType } = record;
  const phoneFields = ['other_contact_phone', 'individual_other_contact_phone'];
  const isUpdatePhone = phoneFields.includes(record.fieldName);
  const SchemaField = createSchemaField({
    components: {
      FormItem,
      FormStep,
      Input,
      VerificationCode,
      VerificationContact,
      OtherContactReview,
    },
  });
  const [formContact, formStep, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'update_seller_contact',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
        },
      }),
      FormStep.createFormStep(),
      setError,
    ];
  }, [props.record?.name]);

  const onSubmit = useMemoizedFn(() => {
    const params: any = {
      fieldMap: {
        [record.name]: formContact.values?.newContact?.[isUpdatePhone ? 'phone' : 'email'],
      },
      groupId: 1,
    };
    if (optType === 'delete') {
      params.fieldMap[record.name] = record.optValue;
      params.operationType = 'RemoveContactInfo';
    }
    console.log('---- params ----', params);
    axios
      .post('mtop.global.merchant.subaccount.sellerprofile.credential.update', params)
      .then(() => {
        props.ok();
      })
      .catch(e => {
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        setTrackerError(message);
        console.error(e);
      });
  });

  return (
    <div data-spm="seller_contact_dialog">
      <Form form={formContact} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical">
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.pleaseVerifyYourAccount',
                    defaultMessage: 'Please verify your account',
                  }),
                }}
              >
                <SchemaField.String
                  name="verification"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationCode.validator,
                  }}
                  x-component="VerificationCode"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                  }}
                />
              </SchemaField.Void>
              {optType === 'add' && (
                <SchemaField.Void
                  x-component="FormStep.StepPane"
                  x-component-props={{
                    title: isUpdatePhone
                      ? i18n.formatMessage({
                          id: 'page.account.setting.addPhone',
                          defaultMessage: 'Add the phone',
                        })
                      : i18n.formatMessage({
                          id: 'page.account.setting.addEmail',
                          defaultMessage: 'Add the email',
                        }),
                  }}
                >
                  <SchemaField.String
                    name="newContact"
                    x-decorator="FormItem"
                    x-validator={{
                      triggerType: 'onBlur',
                      validator: VerificationContact.validator,
                    }}
                    x-component="VerificationContact"
                    x-component-props={{
                      isUpdatePhone,
                      countryNationCode: '',
                      bizCode: OTP_BIZ_NEW_CODE,
                    }}
                    x-reactions={field => {
                      field.componentProps.countryNationCode = formContact?.values?.verification?.countryNationCode;
                    }}
                  />
                </SchemaField.Void>
              )}
              <SchemaField.Void
                type="void"
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.modificationReview',
                    defaultMessage: 'Modification Review',
                  }),
                }}
              >
                <SchemaField.String
                  name="review"
                  x-decorator="FormItem"
                  x-component="OtherContactReview"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                    prefix: record.prefix,
                    countryNationCode: '',
                    isUpdatePhone,
                    newContact: record.optValue,
                    optType,
                  }}
                  x-reactions={field => {
                    if (optType === 'add') {
                      field.componentProps.newContact =
                        formContact.values?.newContact?.[isUpdatePhone ? 'phone' : 'email'];
                    } else {
                      field.componentProps.newContact = record.optValue;
                    }
                    field.componentProps.countryNationCode = formContact?.values?.verification?.countryNationCode;
                  }}
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
        </FormLayout>
        <FormConsumer>
          {() => (
            <FormButtonGroup className="dialog-footer-btn-group">
              <Button
                disabled={!formStep.allowBack}
                onClick={() => {
                  formStep.back();
                }}
              >
                {commonLocale['page.common.btn.previous']}
              </Button>
              <Button
                type="primary"
                disabled={!formStep.allowNext}
                loading={formContact.validating}
                onClick={() => {
                  formStep.next();
                }}
              >
                {commonLocale['page.common.btn.next']}
              </Button>
              <Button
                type="primary"
                loading={formContact.submitting}
                disabled={formStep.allowNext}
                onClick={() => {
                  formStep.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </div>
  );
};

export const useUpdateOtherSellerContact = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onContactEdit: showFunction }] => {
  const [ContactDialog, { view }] = useFormDialog({
    content: ContactEditContent,
    ...config,
    locale: {
      successMessage: commonLocale['page.common.tips.success'],
      failMessage: commonLocale['page.common.tips.failed'],
    },
  });

  return [ContactDialog, { onContactEdit: view }];
};
