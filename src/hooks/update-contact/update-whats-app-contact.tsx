import * as i18n from '@alife/lazada-i18n';
import React from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import { useCreation } from 'ahooks';
import { createForm } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { FormConsumer, createSchemaField } from '@formily/react';
import { Button, Message } from '@alifd/next';
import { Form, FormStep, FormItem, Input, FormLayout, FormButtonGroup } from '@formily/next';

import commonLocale from '../../constants/common-locale';
import VerificationContact from './verification-contact';
import ContactReview from './contact-review';
import VerificationCode from './verification-code';
import { useMemoizedFn } from 'ahooks';

// 由于有多个 OTP 流程，各个流程的接口不能复用，
// 这里把更新联系方式的 OTP 流程的组件全部放在一起
// 避免跟其他流程混淆

// udpate Login Account OTP bizcode
const OTP_BIZ_CODE = 'verifyAccountCredential';
const OTP_BIZ_NEW_CODE = 'verifyNewCredential';
// const OTP_BIZ_CODE_VERIFY = 'verifyAccountCredential';
const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    VerificationCode,
    VerificationContact,
    ContactReview,
  },
});

const ContactEditContent = props => {
  const record = props.record;
  const phoneFields = ['phone', 'individual_phone'];
  const isUpdatePhone = phoneFields.includes(record.fieldName);
  const [formContact, formStep, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'update_whatsapp_contact',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
        },
      }),

      FormStep.createFormStep(),
      setError,
    ];
  }, [record?.name]);
  const onSubmit = useMemoizedFn(() => {
    // profile取countryNationCode, setting的loginphone 展示取nationCode，但触发修改的话，强制他只能改到countryNationCode
    const updateService = axios.post('mtop.global.merchant.subaccount.sellerprofile.credential.update', {
      fieldMap: {
        [record.name]: formContact.values?.newContact?.phone,
      },
      groupId: 1,
      newPhone: formContact.values?.newContact?.phone,
      nationCode: formContact.values?.verification?.countryNationCode?.toString().replace(/\+/, ''),
    });

    return updateService
      .then(() => {
        props.ok();
      })
      .catch(e => {
        console.error(e);
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        setTrackerError(message);
      });
  });

  return (
    <div data-spm="whatsapp_contact_dialog">
      <Form form={formContact} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical">
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.pleaseVerifyYourAccount',
                    defaultMessage: 'Please verify your account',
                  }),
                }}
              >
                <SchemaField.String
                  name="verification"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationCode.validator,
                  }}
                  x-component="VerificationCode"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.changeWhatsApp',
                    defaultMessage: 'Change the WhatsApp',
                  }),
                }}
              >
                <SchemaField.String
                  name="newContact"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationContact.validator,
                  }}
                  x-component="VerificationContact"
                  x-component-props={{
                    isUpdatePhone: true,
                    bizCode: OTP_BIZ_NEW_CODE,
                    fieldLabel: i18n.formatMessage({
                      id: 'page.account.setting.newWhatsApp',
                      defaultMessage: 'New WhatsApp',
                    }),
                  }}
                  x-reactions={field => {
                    field.componentProps.countryNationCode = formContact?.values?.verification?.countryNationCode;
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                type="void"
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.modificationReview',
                    defaultMessage: 'Modification Review',
                  }),
                }}
              >
                <SchemaField.String
                  name="review"
                  x-decorator="FormItem"
                  x-component="ContactReview"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                    oldContact: record.value,
                    prefix: record.prefix,
                    countryNationCode: '',
                    isUpdatePhone,
                    newContact: '',
                  }}
                  x-reactions={field => {
                    field.componentProps.newContact = true
                      ? formContact.values.newContact?.phone
                      : formContact.values.newContact?.email;
                    field.componentProps.countryNationCode = formContact?.values?.verification?.countryNationCode;
                  }}
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
        </FormLayout>
        <FormConsumer>
          {() => (
            <FormButtonGroup className="dialog-footer-btn-group">
              <Button
                disabled={!formStep.allowBack}
                onClick={() => {
                  formStep.back();
                }}
              >
                {commonLocale['page.common.btn.previous']}
              </Button>
              <Button
                type="primary"
                disabled={!formStep.allowNext}
                loading={formContact.validating}
                onClick={() => {
                  formStep.next();
                }}
              >
                {commonLocale['page.common.btn.next']}
              </Button>
              <Button
                type="primary"
                loading={formContact.submitting}
                disabled={formStep.allowNext}
                onClick={() => {
                  formStep.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </div>
  );
};

export const useUpdateWhatsAppContact = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onContactEdit: showFunction }] => {
  const [ContactDialog, { view }] = useFormDialog({
    content: ContactEditContent,
    ...config,
    locale: {
      successMessage: commonLocale['page.common.tips.success'],
      failMessage: commonLocale['page.common.tips.failed'],
    },
  });

  return [ContactDialog, { onContactEdit: view }];
};
