import * as i18n from '@alife/lazada-i18n';

export default {
  // navigator
  'page.common.nav.setting': i18n.formatMessage({
    id: 'page.common.nav.setting',
    defaultMessage: 'Setting',
  }),
  'page.common.nav.home': i18n.formatMessage({
    id: 'page.common.nav.home',
    defaultMessage: 'Home',
  }),
  'page.common.nav.accountSetting': i18n.formatMessage({
    id: 'page.common.nav.accountSetting',
    defaultMessage: 'Account Setting',
  }),
  'page.common.nav.accountBinding': i18n.formatMessage({
    id: 'page.common.nav.accountBinding',
    defaultMessage: 'Social Account Binding',
  }),
  'page.common.nav.bankAccount': i18n.formatMessage({
    id: 'page.common.nav.bankAccount',
    defaultMessage: 'Bank Account',
  }),
  'page.common.nav.system': i18n.formatMessage({
    id: 'page.common.nav.system',
    defaultMessage: 'System',
  }),
  'page.common.nav.notification.settings': i18n.formatMessage({
    id: 'page.common.nav.notification.settings',
    defaultMessage: 'Notification Settings',
  }),
  'page.common.nav.model.settings': i18n.formatMessage({
    id: 'page.common.nav.model.settings',
    defaultMessage: 'Model Settings',
  }),
  'page.common.nav.businessInformation': i18n.formatMessage({
    id: 'page.common.nav.businessInformation',
    defaultMessage: 'Business Information',
  }),
  'page.common.nav.commission': i18n.formatMessage({
    id: 'page.common.nav.commission',
    defaultMessage: 'Commission',
  }),
  'page.common.nav.deliveryService': i18n.formatMessage({
    id: 'page.common.nav.deliveryService',
    defaultMessage: 'Delivery Service',
  }),
  'page.common.nav.servicePerWh': i18n.formatMessage({
    id: 'page.common.nav.servicePerWh',
    defaultMessage: 'Services per Warehouse',
  }),
  'page.common.nav.productDeliverOpt': i18n.formatMessage({
    id: 'page.common.nav.productDeliverOpt',
    defaultMessage: 'Product Delivery Options',
  }),
  'page.common.nav.deliveryOptions': i18n.formatMessage({
    id: 'page.common.nav.deliveryOptions',
    defaultMessage: 'Delivery Options',
  }),
  'page.common.nav.holiday': i18n.formatMessage({
    id: 'page.common.nav.holiday',
    defaultMessage: 'Holiday Mode',
  }),
  'page.common.nav.invoiceNumber': i18n.formatMessage({
    id: 'page.common.nav.invoiceNumber',
    defaultMessage: 'Invoice Number',
  }),
  'page.common.nav.operatingHours': i18n.formatMessage({
    id: 'page.common.nav.operatingHours',
    defaultMessage: 'Operating Hours',
  }),
  'page.common.nav.sellerProfile': i18n.formatMessage({
    id: 'page.common.nav.sellerProfile',
    defaultMessage: 'Seller Profile',
  }),
  'page.common.nav.serviceArea': i18n.formatMessage({
    id: 'page.common.nav.serviceArea',
    defaultMessage: 'Service Area',
  }),
  'page.common.nav.shippingProvider': i18n.formatMessage({
    id: 'page.common.nav.shippingProvider',
    defaultMessage: 'Shipping Provider',
  }),
  'page.common.nav.warehouse': i18n.formatMessage({
    id: 'page.common.nav.warehouse',
    defaultMessage: 'Warehouse',
  }),
  'page.common.nav.finance': i18n.formatMessage({
    id: 'page.common.nav.finance',
    defaultMessage: 'Finance',
  }),
  'page.common.nav.logistic': i18n.formatMessage({
    id: 'page.common.nav.logistic',
    defaultMessage: 'Logistic',
  }),
  'page.common.nav.storeList': i18n.formatMessage({
    id: 'page.common.nav.storeList',
    defaultMessage: 'Store List',
  }),
  'page.common.nav.manageUsers': i18n.formatMessage({
    id: 'page.common.nav.manageUsers',
    defaultMessage: 'Manage Users',
  }),
  'page.common.nav.manageRoles': i18n.formatMessage({
    id: 'page.common.nav.manageRoles',
    defaultMessage: 'Manage Roles',
  }),
  'page.common.nav.brand': i18n.formatMessage({
    id: 'page.common.nav.brand',
    defaultMessage: 'Brand',
  }),
  'page.common.nav.userManagement': i18n.formatMessage({
    id: 'page.common.nav.userManagement',
    defaultMessage: 'User Management',
  }),
  'page.common.nav.chat': i18n.formatMessage({
    id: 'page.common.nav.chat',
    defaultMessage: 'Chat',
  }),

  'page.common.nav.quickReply': i18n.formatMessage({
    id: 'page.common.nav.quickReply',
    defaultMessage: 'Quick Reply',
  }),
  'page.common.nav.autoReply': i18n.formatMessage({
    id: 'page.common.nav.autoReply',
    defaultMessage: 'Auto Reply',
  }),
  'page.common.nav.chatNotification': i18n.formatMessage({
    id: 'page.common.nav.chatNotification',
    defaultMessage: 'Notification',
  }),
  'page.common.nav.chatKeywordTag': i18n.formatMessage({
    id: 'page.common.nav.chatKeywordTag',
    defaultMessage: 'Chat Keyword Tag',
  }),
  'page.common.nav.assistantManagement': i18n.formatMessage({
    id: 'page.common.nav.assistantManagement',
    defaultMessage: 'Assistant Management',
  }),
  'page.common.nav.shipmentProvider': i18n.formatMessage({
    id: 'page.common.nav.shipmentProvider',
    defaultMessage: 'Shipment provider',
  }),

  // button
  'page.common.btn.cancel': i18n.formatMessage({
    id: 'page.common.btn.cancel',
    defaultMessage: 'Cancel',
  }),
  'page.common.btn.submit': i18n.formatMessage({
    id: 'page.common.btn.submit',
    defaultMessage: 'Submit',
  }),
  'page.common.btn.ok': i18n.formatMessage({
    id: 'page.common.btn.ok',
    defaultMessage: 'OK',
  }),
  'page.common.btn.next': i18n.formatMessage({
    id: 'page.common.btn.next',
    defaultMessage: 'Next',
  }),
  'page.common.btn.previous': i18n.formatMessage({
    id: 'page.common.btn.previous',
    defaultMessage: 'Previous',
  }),
  'page.common.btn.modify': i18n.formatMessage({
    id: 'page.common.btn.modify',
    defaultMessage: 'Modify',
  }),
  'page.common.btn.upload': i18n.formatMessage({
    id: 'page.common.btn.upload',
    defaultMessage: 'Upload',
  }),
  'page.common.btn.edit': i18n.formatMessage({
    id: 'page.common.btn.edit',
    defaultMessage: 'Edit',
  }),
  'page.common.btn.send': i18n.formatMessage({
    id: 'page.common.btn.send',
    defaultMessage: 'Send',
  }),
  'page.common.btn.resendAfter': variables => {
    return i18n.formatMessage(
      {
        id: 'page.common.btn.resendAfter',
        defaultMessage: 'Re-Send After {time}s',
      },
      variables || {},
    );
  },
  'page.common.btn.delete': i18n.formatMessage({
    id: 'page.common.btn.delete',
    defaultMessage: 'Delete',
    app: 'lazada-seller-center',
  }),
  'page.common.btn.confirm': i18n.formatMessage({
    id: 'page.common.btn.confirm',
    defaultMessage: 'Confirm',
    app: 'lazada-seller-center',
  }),

  // tips
  'page.common.tips.success': i18n.formatMessage({
    id: 'page.common.tips.success',
    defaultMessage: 'Success',
  }),
  'page.common.tips.error': i18n.formatMessage({
    id: 'page.common.tips.error',
    defaultMessage: 'Error',
  }),
  'page.common.tips.pending': i18n.formatMessage({
    id: 'page.common.tips.pending',
    defaultMessage: 'pending',
  }),
  'page.common.tips.effective': i18n.formatMessage({
    id: 'page.common.tips.effective',
    defaultMessage: 'Effective',
  }),
  'page.common.tips.rejected': i18n.formatMessage({
    id: 'page.common.tips.rejected',
    defaultMessage: 'Rejected',
  }),
  'page.common.tips.pre.effective': i18n.formatMessage({
    id: 'page.common.tips.pre.effective',
    defaultMessage: 'Pre-Effective',
  }),
  'page.common.tips.failed': i18n.formatMessage({
    id: 'page.common.tips.failed',
    defaultMessage: 'Failed',
  }),
  'page.common.tips.notSet': i18n.formatMessage({
    id: 'page.common.tips.notSet',
    defaultMessage: 'Not Set',
  }),
  'page.common.tips.required': i18n.formatMessage({
    id: 'page.common.tips.required',
    defaultMessage: 'Required',
  }),
  'page.index.legalAgree.notice': i18n.formatMessage({
    id: 'page.index.legalAgree.notice',
    defaultMessage: 'please check all agreements',
  }),
  'page.account.setting.verificationCode': i18n.formatMessage({
    id: 'page.account.setting.verificationCode',
    defaultMessage: 'Verification Code',
  }),

  'page.LZDlogin.enterCode': i18n.formatMessage({
    id: 'page.LZDlogin.enterCode',
    defaultMessage: 'Enter the Code',
  }),

  'page.account.setting.checkNow': i18n.formatMessage({
    id: 'page.account.setting.checkNow',
    defaultMessage: 'Check Now',
  }),
  'page.account.setting.progress': i18n.formatMessage({
    id: 'page.account.setting.progress',
    defaultMessage: 'Progress',
  }),
  'page.account.setting.updateTime': i18n.formatMessage({
    id: 'page.account.setting.updateTime',
    defaultMessage: 'UpdateTime',
  }),
  'page.account.setting.remark': i18n.formatMessage({
    id: 'page.account.setting.remark',
    defaultMessage: 'Remark',
  }),
  'page.account.setting.iGoIt': i18n.formatMessage({
    id: 'page.account.setting.iGoIt',
    defaultMessage: 'I go it',
  }),
  'page.account.setting.recall': i18n.formatMessage({
    id: 'page.account.setting.recall',
    defaultMessage: 'Recall',
  }),
  'page.account.setting.checkTheProgress': i18n.formatMessage({
    id: 'page.account.setting.checkTheProgress',
    defaultMessage: 'Check the progress',
  }),
  // business
  'page.account.experience.businessExperience': i18n.formatMessage({
    id: 'page.account.setting.businessExperience',
    defaultMessage: 'Business Experience',
  }),
  'page.account.experience.subTitle': i18n.formatMessage({
    id: 'page.account.setting.businessExperience',
    defaultMessage: 'Business Experience',
  }),
  'page.account.experience.noticeMessage': i18n.formatMessage({
    id: 'page.account.setting.businessExperience',
    defaultMessage: 'Business Experience',
  }),
  'page.account.experience.STORE_MANAGEMENT': i18n.formatMessage({
    id: 'page.account.setting.STORE_MANAGEMENT',
    defaultMessage: 'Lazada Store Plan',
  }),
  'page.account.experience.ONLINE_STORE': i18n.formatMessage({
    id: 'page.account.setting.ONLINE_STORE',
    defaultMessage: 'Online store experience',
  }),
  'page.account.experience.OFFLINE_STORE': i18n.formatMessage({
    id: 'page.account.setting.OFFLINE_STORE',
    defaultMessage: 'Offline store experience',
  }),
  'page.account.experience.SUPPLY_CHAIN': i18n.formatMessage({
    id: 'page.account.setting.SUPPLY_CHAIN',
    defaultMessage: 'Supply Chain',
  }),
  'page.account.experience.SOCIAL_MEDIA': i18n.formatMessage({
    id: 'page.account.setting.SOCIAL_MEDIA',
    defaultMessage: 'Social Media',
  }),
  'page.account.experience.BRAND_INFO': i18n.formatMessage({
    id: 'page.account.setting.BRAND_INFO',
    defaultMessage: 'Brand Info',
  }),
  'page.account.experience.TIKTOK_SHOP': i18n.formatMessage({
    id: 'page.account.setting.TIKTOK_SHOP',
    defaultMessage: 'TikTok Shop',
  }),
  'page.account.setting.strengthTips': i18n.formatMessage({
    id: 'page.account.setting.strengthTips',
    defaultMessage:
      '<span>Profile Strength &nbsp;\
    Weak: ≤30% task     Medium: 30%-70% &nbsp;\
    Strong: ≥70%</span>',
  }),
  'page.account.setting.profileStrength': i18n.formatMessage({
    id: 'page.account.setting.profileStrength',
    defaultMessage: 'Profile Strength',
  }),
  'page.account.setting.completeTask': i18n.formatMessage({
    id: 'page.account.setting.completeTask',
    defaultMessage: 'Complete Tasks',
  }),
  'page.account.experience.importantComment': i18n.formatMessage({
    id: 'page.account.experience.importantComment',
    defaultMessage:
      '"IMPORTANT: By submitting the form, the information we collect about you and your business, including your name, email address and phone number from the submission shall be verified, stored, processed and disclosed by Lazada, our affiliates and third-party vendors in accordance with the terms of use that you have entered into with Lazada and the Lazada Privacy Policy [INSERT HYPERLINK TO OUR PRIVACY POLICY]. Amongst others, the information you share with us will help to make the Services and the Platform more useful to you, and to help us to provide you with products, services and features which will improve your experience.',
  }),

  'page.account.setting.Enter6digit': i18n.formatMessage({
    id: 'page.account.setting.Enter6digit',
    defaultMessage: 'Enter the 6-digit code sent to',
  }),

  'page.account.setting.notReceiveTheCode': i18n.formatMessage({
    id: 'page.account.setting.notReceiveTheCode',
    defaultMessage: 'Did not receive the code?',
  }),
  'page.common.nav.nopermission': i18n.formatMessage({
    id: 'page.common.nav.nopermission',
    defaultMessage: 'No Permission',
  }),
  'page.account.setting.addressOutOfService': i18n.formatMessage({
    id: 'page.account.setting.addressOutOfService',
    defaultMessage: 'Address out of service',
  }),
  'page.account.setting.baseOnYourNewWarehouse': i18n.formatMessage({
    id: 'page.account.setting.baseOnYourNewWarehouse',
    defaultMessage:
      'Base on your new warehouse address, currently we have no 3PL can provide service for you,you need to apply for DBS',
  }),
  'page.account.setting.pleaseConfirmIfYouStill': i18n.formatMessage({
    id: 'page.account.setting.pleaseConfirmIfYouStill',
    defaultMessage: 'Please confirm if you still want to change your warehouse address',
  }),
  'page.account.setting.please.save.address': i18n.formatMessage({
    id: 'page.account.setting.please.save.address',
    defaultMessage: 'Save address',
  }),
  'page.todo.iddocAgreement': i18n.formatMessage({
    id: 'page.todo.iddocAgreement',
    defaultMessage: 'Save address',
  }),
  'page.common.edit.termsAndConditions': i18n.formatMessage({
    id: 'page.common.edit.termsAndConditions',
    defaultMessage: 'I have read, understood and agree to the terms and conditions of',
  }),
  'page.common.edit.termsAndConditions.and': i18n.formatMessage({
    id: 'page.common.edit.termsAndConditions.and',
    defaultMessage: 'and',
  }),
  'page.common.edit.termsAndConditions.link.ms': i18n.formatMessage({
    id: 'page.common.edit.termsAndConditions.link.ms',
    defaultMessage: '<a href="#" target="_blank">Alipay MS Terms and Conditions</a>',
  }),
  'page.common.edit.termsAndConditions.link.privacy': i18n.formatMessage({
    id: 'page.common.edit.termsAndConditions.link.privacy',
    defaultMessage: '<a href="#" target="_blank">Alipay Privacy Policy for Merchant Services</a>',
  }),
  'page.common.edit.termsAndConditions.link.agreement': i18n.formatMessage({
    id: 'page.common.edit.termsAndConditions.link.agreement',
    defaultMessage: '<a href="#" target="_blank">Alipay Merchant Acquisition Services Agreements</a>',
  }),
  'page.common.other.information.title': i18n.formatMessage({
    id: 'page.common.title.other.information',
    defaultMessage: 'Other Information',
  }),
};
