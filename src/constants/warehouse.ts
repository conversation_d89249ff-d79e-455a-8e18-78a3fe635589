import * as i18n from '@alife/lazada-i18n';

export default {
  'page.description': i18n.formatMessage({
    id: 'page.warehouse.address.page.description',
    defaultMessage:
      'New feature Pickup Address Group aims at solving multiple Shipping Provider and First Mile Type problems Click here for more info regarding this new feature',
  }),
  'address.not.found': i18n.formatMessage({
    id: 'page.warehouse.address.search.not.found',
    defaultMessage: 'No address founded, Click on map to pin!',
  }),
  'address.xss.error': i18n.formatMessage({
    id: 'page.warehouse.address.xss.error',
    defaultMessage: 'Cant contain rich text',
  }),
  'address.charactor.tip': i18n.formatMessage({
    id: 'page.warehouse.address.charactor.tip',
    defaultMessage: 'Address must contain charactor',
  }),
  'map.drag': i18n.formatMessage({
    id: 'page.warehouse.address.edit.map.drag',
    defaultMessage: 'Click On Map To Pin',
  }),
  'post.code.length': i18n.formatMessage({
    id: 'page.warehouse.address.post.code.length',
    defaultMessage: 'Post Code is not right',
  }),
  'can.not.start.with.zero': i18n.formatMessage({
    id: 'page.warehouse.address.can.not.start.with.zero',
    defaultMessage: 'Can not start with zero',
  }),
  'dialog.confirm.title': i18n.formatMessage({
    id: 'page.warehouse.address.edit.confirm.title',
    defaultMessage: 'Location Confirm',
  }),
  'dialog.confirm.address.content': i18n.formatMessage({
    id: 'page.warehouse.address.edit.confirm.address.content',
    defaultMessage: 'Are you sure to use this address?',
  }),
  'dialog.confirm.diff.title': i18n.formatMessage({
    id: 'page.warehouse.address.edit.confirm.diff.title',
    defaultMessage: 'District is not different Are you sure to use this address?',
  }),
  'phone.verify': i18n.formatMessage({
    id: 'page.warehouse.address.edit.phone.verify',
    defaultMessage: 'Phone number is not correct!',
  }),
  'email.verify': i18n.formatMessage({
    id: 'page.warehouse.address.edit.email.verify',
    defaultMessage: 'Email is not correct!',
  }),
  'address.too.long': i18n.formatMessage({
    id: 'page.warehouse.address.edit.address.error',
    defaultMessage: 'Address is too long!',
  }),
  'address.contain.number': i18n.formatMessage({
    id: 'page.warehouse.address.edit.address.contain.number',
    defaultMessage: 'Address must contain road number!',
  }),
  'address.too.short': i18n.formatMessage({
    id: 'page.warehouse.address.edit.address.too.short',
    defaultMessage: 'Address must greater than 10',
  }),
  required: i18n.formatMessage({
    id: 'page.warehouse.address.edit.form.field.verify',
    defaultMessage: 'required!',
  }),
  'input.floor.unit': i18n.formatMessage({
    id: 'page.warehouse.address.edit.floor.unit.number',
    defaultMessage: 'Please input Floor and Unit Number!',
  }),
  'phone.too.long': i18n.formatMessage({
    id: 'page.warehouse.address.edit.phone.error',
    defaultMessage: 'Phone number is not right',
  }),
  'no.result': i18n.formatMessage({
    id: 'page.warehouse.address.search.no.result',
    defaultMessage: 'No Result!',
  }),
  'search.placeholder': i18n.formatMessage({
    id: 'page.warehouse.address.search.placeholder',
    defaultMessage: 'search street or building',
  }),
  'search.attention': i18n.formatMessage({
    id: 'page.warehouse.address.search.attention',
    defaultMessage: 'Attention',
  }),
  'search.tip': i18n.formatMessage({
    id: 'page.warehouse.address.search.your.address',
    defaultMessage: 'Search Your Address',
  }),
  'search.attention.list1': i18n.formatMessage({
    id: 'page.warehouse.address.search.attention.list1',
    defaultMessage:
      '1.Please make sure this is <span style="color: #EB5E00">your address</span>, you can modify it manually if necessary',
  }),
  'search.attention.list2': i18n.formatMessage({
    id: 'page.warehouse.address.search.attention.list2',
    defaultMessage: '2.Please Insert <span style="color: #EB5E00">Unit Number</span>',
  }),
  'district.select.tip': i18n.formatMessage({
    id: 'page.warehouse.address.district.select.tip',
    defaultMessage: 'Please select Level 5',
  }),
  'district.different.tip': i18n.formatMessage({
    id: 'page.warehouse.address.district.different.tip',
    defaultMessage: 'District info have been auto updated!',
  }),
  'no.geo.code.tip': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.no.geo.code.tip',
    defaultMessage: 'You have no geoCode yet Please select',
  }),
  'warehouse.list': i18n.formatMessage({
    id: 'page.setting.warehouse.address.warehouse.list',
    defaultMessage: 'Warehouse List',
  }),
  'pickup.address.group': i18n.formatMessage({
    id: 'page.setting.warehouse.address.pickup.address.group',
    defaultMessage: 'Pickup Address Group',
  }),
  'address.edit.tip': i18n.formatMessage({
    id: 'page.setting.warehouse.address.edit.tip',
    defaultMessage:
      'Your address is in a group Are you sure you want to edit your address All addresses in the group will be changed',
  }),
  title: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.title',
    defaultMessage: 'Pickup Address Group List',
  }),
  create: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.create',
    defaultMessage: 'Create Group',
  }),
  action: i18n.formatMessage({
    id: 'page.account.setting.action',
    defaultMessage: 'Action',
  }),
  roleAccount: i18n.formatMessage({
    id: 'page.setting.warehouse.address.role.account',
    defaultMessage: 'Account Role',
  }),
  accountRole: i18n.formatMessage({
    id: 'page.setting.warehouse.address.account.role',
    defaultMessage: 'Group Owner',
  }),
  groupMember: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.member',
    defaultMessage: 'Group Member',
  }),
  sellerID: i18n.formatMessage({
    id: 'page.setting.warehouse.address.account.sellerid',
    defaultMessage: 'Seller ID',
  }),
  warehouseAddress: i18n.formatMessage({
    id: 'page.setting.warehouse.address.address',
    defaultMessage: 'Warehouse Address',
  }),
  loginPhoneNumber: i18n.formatMessage({
    id: 'page.setting.warehouse.address.login.number',
    defaultMessage: 'Login Phone Numebr',
  }),
  requestHistory: i18n.formatMessage({
    id: 'page.setting.warehouse.address.request.history',
    defaultMessage: 'Request History',
  }),
  bindingType: i18n.formatMessage({
    id: 'page.setting.warehouse.address.binding.type',
    defaultMessage: 'bindType',
  }),
  requestType: i18n.formatMessage({
    id: 'page.setting.warehouse.address.request.type',
    defaultMessage: 'Request Type',
  }),
  warehouseCode: i18n.formatMessage({
    id: 'page.setting.warehouse.address.warehouse.code',
    defaultMessage: 'Warehouse Code',
  }),
  status: i18n.formatMessage({
    id: 'page.setting.warehouse.address.status',
    defaultMessage: 'Status',
  }),
  warehouseName: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.warehouse.name',
    defaultMessage: 'Warehouse Name',
  }),
  createGroupTip: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.create.tip',
    defaultMessage:
      'Please be advised that your Shipping Provider or First Mile type might be changed after creating a group',
  }),
  requestStatus: i18n.formatMessage({
    id: 'page.setting.warehouse.address.request.status',
    defaultMessage: 'Request Status',
  }),
  requestTime: i18n.formatMessage({
    id: 'page.setting.warehouse.address.request.time',
    defaultMessage: 'Request Time',
  }),
  updateTime: i18n.formatMessage({
    id: 'page.setting.warehouse.address.update.time',
    defaultMessage: 'Update Time',
  }),
  viewDetail: i18n.formatMessage({
    id: 'page.setting.warehouse.address.view.detail',
    defaultMessage: 'View Detail',
  }),
  all: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.all',
    defaultMessage: 'All',
  }),
  inProcess: i18n.formatMessage({
    id: 'page.setting.warehouse.address.status.inprocess',
    defaultMessage: 'In Process',
  }),
  approved: i18n.formatMessage({
    id: 'page.setting.warehouse.address.status.approved',
    defaultMessage: 'Approved',
  }),
  rejected: i18n.formatMessage({
    id: 'page.setting.warehouse.address.status.rejected',
    defaultMessage: 'Rejectd',
  }),
  cancelled: i18n.formatMessage({
    id: 'page.setting.warehouse.address.status.cancelled',
    defaultMessage: 'Cancelled',
  }),
  removeMember: i18n.formatMessage({
    id: 'page.setting.warehouse.address.remove.member',
    defaultMessage: 'Remove',
  }),
  leaveGroup: i18n.formatMessage({
    id: 'page.setting.warehouse.address.leave.group',
    defaultMessage: 'Leave',
  }),
  disband: i18n.formatMessage({
    id: 'page.setting.warehouse.address.disband',
    defaultMessage: 'Disband',
  }),
  disbandTip: i18n.formatMessage({
    id: 'page.setting.warehouse.address.disbandTip',
    defaultMessage: 'Are you sure to disband the group,After disband,the group cant be recoverded',
  }),
  leaveGroupSuccess: i18n.formatMessage({
    id: 'page.setting.warehouse.address.leave.group.success',
    defaultMessage: 'Leave Success',
  }),
  removeGroupSuccess: i18n.formatMessage({
    id: 'page.setting.warehouse.address.remove.group.success',
    defaultMessage: 'Remove Success',
  }),
  historySuccess: i18n.formatMessage({
    id: 'page.setting.warehouse.address.history.success',
    defaultMessage:
      'Your have accepted this invitation Your Pick up address has been updated successfully Shipping Provider and First Mile Type changing will take few days',
  }),
  from: i18n.formatMessage({
    id: 'page.setting.warehouse.address.from',
    defaultMessage: 'From',
  }),
  toAdd: i18n.formatMessage({
    id: 'page.setting.warehouse.address.to.add',
    defaultMessage: 'To Add',
  }),
  toRemove: i18n.formatMessage({
    id: 'page.setting.warehouse.address.to.remove',
    defaultMessage: 'To Remove',
  }),
  addMembers: i18n.formatMessage({
    id: 'page.setting.warehouse.address.add.mebers',
    defaultMessage: 'Add Members',
  }),
  leaveConfirmTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.leave.title',
    defaultMessage: 'Leave Pickup Address Group',
  }),
  leaveConfirmSubTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.leave.subtitle',
    defaultMessage: 'You are going to leave the following Group',
  }),
  leaveConfirmBottom: i18n.formatMessage({
    id: 'page.setting.warehouse.address.leave.bottom',
    defaultMessage:
      'After leaving, your Shipping Provider and First Mile type might be changed However your warehouse address will not be changed, please edit the address if needed',
  }),
  removeConfirmTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.remove.title',
    defaultMessage: 'Remove Member from Pickup Address Group',
  }),
  removeConfirmSubTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.remove.subtitle',
    defaultMessage: 'You are going to remove the following Member',
  }),
  removeConfirmBottom: i18n.formatMessage({
    id: 'page.setting.warehouse.address.remove.bottom',
    defaultMessage:
      'After removing, Shipping Provider and First Mile type of the leaving member and the group might be changed',
  }),
  invitationConfirmTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.invitation.title',
    defaultMessage: 'Invitation to join Pickup Address Group',
  }),
  invitationConfirmSubTitle: i18n.formatMessage({
    id: 'page.setting.warehouse.address.invitation.subtitle',
    defaultMessage: 'After joining, your new pickup address will be',
  }),
  invitationConfirmBottom: i18n.formatMessage({
    id: 'page.setting.warehouse.address.invitation.bottom',
    defaultMessage: 'Your Shipping Provider and First Mile Type might also be changed in few days',
  }),
  invitationReject: i18n.formatMessage({
    id: 'page.setting.warehouse.address.invitation.reject',
    defaultMessage: 'You have rejected the invitation',
  }),
  binding: i18n.formatMessage({
    id: 'page.setting.warehouse.address.binding',
    defaultMessage: 'BINDING',
  }),
  unBinding: i18n.formatMessage({
    id: 'page.setting.warehouse.address.unbinding',
    defaultMessage: 'UNBINDING',
  }),
  sender: i18n.formatMessage({
    id: 'page.setting.warehouse.address.sender',
    defaultMessage: 'SENDER',
  }),
  notes: i18n.formatMessage({
    id: 'page.setting.warehouse.address.notes',
    defaultMessage: 'notes',
  }),
  receiver: i18n.formatMessage({
    id: 'page.setting.warehouse.address.receiver',
    defaultMessage: 'RECEIVER',
  }),
  cancelReason: i18n.formatMessage({
    id: 'page.setting.warehouse.address.cancel.reason',
    defaultMessage: 'Please Input Reajected Reason',
  }),
  editDisableTip: i18n.formatMessage({
    id: 'page.setting.warehouse.address.editDisableTip',
    defaultMessage: 'Sub cant edit',
  }),
  finishSubAccount: i18n.formatMessage({
    id: 'page.setting.warehouse.address.finish.sub.account',
    defaultMessage: 'Please finish current Sub Account',
  }),
  cantDisband: i18n.formatMessage({
    id: 'page.setting.warehouse.address.group.cant.disband',
    defaultMessage: 'Cant Disband',
  }),
};
