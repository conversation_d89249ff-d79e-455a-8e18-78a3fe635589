import { axios } from '@alife/workstation-utils';

export const subscribe = ({ warehouseId, solutionCode }) => {
  return axios.post('mtop.lazada.asc.seller.deliveryservice.subscribe', {
    operatorType: 'seller',
    warehouseId,
    solutionCode,
  });
};

export const getDBS = () => {
  return axios.get('mtop.lazada.asc.seller.product.deliveryoptions.dbs.query').then(({ data: res }) => {
    if (res?.data[0]) {
      return res?.data[0].dataSource;
    }
    return res?.data.dataSource;
  });
};

export const updateGeocode = ({ warehouseId, latitude, longitude }) => {
  return axios.post('mtop.lazada.asc.seller.geocode.update', {
    warehouseCode: warehouseId,
    latitude,
    longitude,
  });
};

export const unsubscribe = ({ warehouseId, solutionCode, otherReason = '', chooseReason = '' }) => {
  otherReason = chooseReason === 'other' ? otherReason : '';

  return axios.post('mtop.lazada.asc.seller.deliveryservice.unsubscribe', {
    operatorType: 'seller',
    warehouseId,
    solutionCode,
    otherReason,
    chooseReason,
    reason: otherReason || chooseReason,
  });
};
