import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { Message, Button, Card } from '@alifd/next';
import { useCreation, useMemoizedFn, useRequest } from 'ahooks';
import { createForm } from '@formily/core';
import { Field, FormConsumer } from '@formily/react';
import { Form, FormItem, Input, Select, Reset, Submit, FormLayout, FormButtonGroup } from '@formily/next';
import { withTrackEffects } from '@alife/form-track-plugin';
import commonLocale from '../../constants/common-locale';
import { updateGeocode, subscribe } from './services';

const UnsubscribeDialogContent = props => {
  const { record = {} } = props;
  const hasLngLat = record?.longitude && record?.latitude && !!record?.validateGeoCodes;

  const [form, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'delivery_service_geocode',
    });

    const formInstance = createForm({
      effects() {
        autoFormTrack();
      },
    });
    formInstance.setInitialValues({
      latitude: props.record?.latitude,
      longitude: props.record?.longitude,
    });
    return [formInstance, setError];
  }, [props.record?.warehouseId]);

  const completeWarehouse = () => {
    return (
      <Card className="need-complete-warehouse">
        <div className="tip-content">
          {i18n.formatMessage({
            id: 'page.delivery.modal.tip.content',
            defaultMessage:
              'To subscribe instant delivery we need you to confirm the geo-coordinate of your warehouse address so we can pick up the parcel from your smoothly!',
          })}
        </div>

        <a
          className="aplus-auto-exp link"
          data-spm="d_add_address"
          href={`/apps/setting/warehouse_address_map/edit?code=${props.record?.warehouseId}&groupId=7`}
        >
          {i18n.formatMessage({
            id: 'page.delivery.modal.tip.link',
            defaultMessage: 'Click here update your address and geo-coordinate',
          })}
        </a>

        <div className="address-button">
          <Button
            type="primary"
            onClick={() => {
              onCancel();
            }}
            className="aplus-auto-exp"
            data-spm="d_add_ok"
          >
            {commonLocale['page.common.btn.ok']}
          </Button>
        </div>
      </Card>
    );
  };

  const renderForm = () => {
    return (
      <>
        {errorMsg ? <div className="operation-error">Error: {errorMsg}</div> : null}
        <div
          style={{ marginBottom: 16, width: 450, color: '#999' }}
          dangerouslySetInnerHTML={{
            __html: i18n.formatMessage({
              id: 'page.delivery.modal.instant.longitude.tip',
              defaultMessage: 'Please refer to the <a target="_blank" href="http://lzd.co/geocodeinstant">guide</a>',
            }),
          }}
        />
        <Form form={form} labelCol={6} wrapperCol={10}>
          <FormLayout layout="vertical" wrapperWidth={450} colon={false}>
            <Field
              name="latitude"
              title={i18n.formatMessage({
                id: 'page.delivery.modal.instant.latitude',
                defaultMessage: 'Latitude',
              })}
              required
              decorator={[FormItem]}
              component={[Input]}
            />
            <Field
              name="longitude"
              title={i18n.formatMessage({
                id: 'page.delivery.modal.instant.longitude',
                defaultMessage: 'Longitude',
              })}
              required
              decorator={[FormItem]}
              component={[Input]}
            />
          </FormLayout>
          <FormConsumer>
            {currentForm => (
              <FormButtonGroup className="address-dialog-footer">
                <Button loading={currentForm.submitting} onClick={onCancel}>
                  {commonLocale['page.common.btn.cancel']}
                </Button>
                <Button
                  type="primary"
                  loading={currentForm.submitting}
                  onClick={() => {
                    form.submit(onSubmit);
                  }}
                >
                  {commonLocale['page.common.btn.submit']}
                </Button>
              </FormButtonGroup>
            )}
          </FormConsumer>
        </Form>
      </>
    );
  };

  const [errorMsg, setErrorMsg] = useState('');

  const onCancel = useMemoizedFn(() => {
    record?.callback && record?.callback();
    props.close();
  });

  const onSubmit = useMemoizedFn(() =>
    updateGeocode({
      warehouseId: props.record?.warehouseId,
      latitude: form.values?.latitude,
      longitude: form.values?.longitude,
    })
      .then(() => {
        // debugger;
        subscribe({
          warehouseId: props.record?.warehouseId,
          solutionCode: props.record?.solutionCode,
        })
          .then(() => {
            setErrorMsg('');
            Message.success(commonLocale['page.common.tips.success']);
            props.ok();
          })
          .catch(e => {
            const message = e.message;
            setErrorMsg(message);
            setTrackerError(message);
          });
      })
      .catch(e => {
        const message = e.message;
        setErrorMsg(message);
        setTrackerError(message);
      }),
  );

  return <>{!!hasLngLat ? renderForm() : completeWarehouse()}</>;
};

export default UnsubscribeDialogContent;
