import * as i18n from '@alife/lazada-i18n';
import React from 'react';
import { axios } from '@alife/workstation-utils';
import { useRequest, useMemoizedFn } from 'ahooks';
import { Table, Icon, Card, Switch } from '@alifd/next';

function ResultPage() {
  const { data = {}, error, loading, run } = useRequest(() =>
    // mtop.lazada.asc.seller.product.deliveryoptions.query mtop.lazada.asc.seller.product.deliveryoptions.dbs.query
    axios.get('mtop.lazada.asc.seller.product.deliveryoptions.query.new').then(({ data: res }) => {
      // if (res?.data[0]) {
      //   return res?.data[0].dataSource;
      // }
      return res?.module.dataSource;
    }),
  );

  const { run: updateStaus } = useRequest(
    params =>
      axios.post('mtop.lazada.asc.seller.product.deliveryoptions.update', {
        data: {
          sellerId: window?.lzdCommonData?.user?.sellerId || '',
          ...params,
        },
      }),
    {
      manual: true,
      onSuccess: () => {
        run();
      },
    },
  );

  const renderStatus = useMemoizedFn(val => {
    if (val) {
      return (
        <span className="text-green">
          <Icon type="success" className="mr5" />
        </span>
      );
    }
    return (
      <span className="text-gray">
        <Icon type="error" className="mr5" />
      </span>
    );
  });
  return (
    <>
      <Card className="mb-standard">
        <div
          dangerouslySetInnerHTML={{
            __html: i18n.formatMessage({
              id: 'page.delivery.table.productDeliveryOptions@notice',
              defaultMessage:
                'On this page, you can see the delivery services which you can activate/deactivate on product level. You can do so by going to Product > Manage Product > Actions > Edit Details > Service & Delivery',
            }),
          }}
        ></div>
      </Card>
      <Card>
        <Table dataSource={data}>
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.type', defaultMessage: 'Type' })}
            dataIndex="type"
          />
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.available', defaultMessage: 'Available' })}
            dataIndex="available"
            cell={renderStatus}
          />
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.available', defaultMessage: 'Available' })}
            dataIndex="enableEdit"
            cell={(val, index, record) => {
              return (
                <Switch
                  checked={!!record.available}
                  disabled={!record?.enableEdit}
                  onChange={val => {
                    const optionList = data?.map(item => {
                      if (item.deliveryOption === record.deliveryOption) {
                        return {
                          ...item,
                          productDeliveryOption: record.deliveryOption,
                          status: val ? 1 : 0,
                        };
                      }
                      return {
                        ...item,
                        productDeliveryOption: item.deliveryOption,
                        status: item.available ? 1 : 0,
                      };
                    });
                    updateStaus({
                      productSubscriptionList: [...optionList],
                    });
                  }}
                />
              );
            }}
          />
        </Table>
      </Card>
    </>
  );
}

export default ResultPage;
