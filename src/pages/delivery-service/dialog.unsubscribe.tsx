import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { axios } from '@alife/workstation-utils';
import { useCreation, useMemoizedFn } from 'ahooks';
import { Message, Button } from '@alifd/next';
import { createForm } from '@formily/core';
import { Field, FormConsumer } from '@formily/react';
import { Form, FormItem, Input, Select, Reset, Submit, FormLayout, FormButtonGroup } from '@formily/next';
import { withTrackEffects } from '@alife/form-track-plugin';
import { unsubscribe } from './services';
import commonLocale from '../../constants/common-locale';

const UnsubscribeDialogContent = props => {
  // console.log(props.record);

  const [form, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'unscribe_delivery_service',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
        },
      }),
      setError,
    ];
  }, [props.record?.warehouseId]);

  const [errorMsg, setErrorMsg] = useState('');

  const onCancel = useMemoizedFn(() => {
    const { record } = props;
    record?.callback && record?.callback();
    props.close();
  });

  const onSubmit = useMemoizedFn(() =>
    unsubscribe({
      ...form.values,
      warehouseId: props.record?.warehouseId,
      solutionCode: props.record?.solutionCode,
    })
      .then(() => {
        setErrorMsg('');
        Message.success(commonLocale['page.common.tips.success']);
        props.ok();
      })
      .catch(e => {
        const message = e.message;
        setErrorMsg(message);
        setTrackerError(message);
      }),
  );

  return (
    <>
      {errorMsg ? <div className="operation-error">Error: {errorMsg}</div> : null}
      <div style={{ marginBottom: 16, width: 450 }}>
        {i18n.formatMessage({
          id: 'page.account.setting.unsubscribeServiceTip',
          defaultMessage:
            'Before unsubscribing the delivery service, please ensure that you have deactivated this delivery service type on your products in your product listing page (if applicable)',
        })}
      </div>
      <Form form={form} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical" wrapperWidth={450} colon={false}>
          <Field
            name="chooseReason"
            title={i18n.formatMessage({
              id: 'page.account.setting.pleaseSelectTheReasonFor',
              defaultMessage: 'Please select the reason for unsubscribing',
            })}
            required
            decorator={[FormItem]}
            component={[Select, { dataSource: props?.record?.reasonOptions }]}
          />

          <Field
            name="otherReason"
            title={i18n.formatMessage({
              id: 'page.account.setting.enterTheReason',
              defaultMessage: 'Enter the reason',
            })}
            required
            decorator={[FormItem]}
            component={[Input.TextArea]}
            reactions={field => {
              const visible = form.values.chooseReason === 'other';
              field.visible = visible;
              field.required = visible;
            }}
          />
        </FormLayout>
        <FormConsumer>
          {currentForm => (
            <FormButtonGroup className="address-dialog-footer">
              <Button loading={currentForm.submitting} onClick={onCancel}>
                {commonLocale['page.common.btn.cancel']}
              </Button>
              <Button
                type="primary"
                loading={currentForm.submitting}
                onClick={() => {
                  form.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </>
  );
};

export default UnsubscribeDialogContent;
