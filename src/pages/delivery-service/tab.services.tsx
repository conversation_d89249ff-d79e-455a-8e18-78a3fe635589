import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { axios, ASCTools, When } from '@alife/workstation-utils';
import { useRequest, useMemoizedFn, usePrevious } from 'ahooks';
import { Table, Icon, Switch, Card, Balloon, Message, Dialog } from '@alifd/next';
import { useFormDialog, SafeHtmlText } from '@alife/asc-components';
import commonLocale from '../../constants/common-locale';
import UnsubscribeDialogContent from './dialog.unsubscribe';
import LongitudeDialogContent from './dialog.longitude';
import { subscribe, unsubscribe, getDBS } from './services';

function ResultPage() {
  const [data, setData] = useState([]);
  const [switchLoading, setLoading] = useState({});
  const [codData, setCodData] = useState({});
  const [isNonDefaultArea, setIsNonDefaultArea] = useState<boolean>(false);
  const [warehouseNames, setWarehouseName] = useState<Array<string>>([]);

  const [EditDialog, { edit }] = useFormDialog({
    content: UnsubscribeDialogContent,
    dialogProps: {
      title: i18n.formatMessage({
        id: 'page.account.setting.unsubscribeService',
        defaultMessage: 'Unsubscribe Service',
      }),
      footer: false,
    },
  });

  const [EditLongitudeDialog, { edit: editLongitude }] = useFormDialog({
    content: LongitudeDialogContent,
    dialogProps: {
      footer: false,
    },
  });

  useRequest(() =>
    axios
      .get('mtop.lazada.asc.seller.cod.query')
      .then(({ data: res }) => {
        setCodData(res?.data);
      })
      .catch(e => {
        console.error(e);
      }),
  );

  const { data: dbsData } = useRequest(getDBS);

  const { run } = useRequest(() =>
    axios
      .get('mtop.lazada.asc.seller.deliveryservice.query')
      .then(({ data: res }) => {
        if (res?.data[0]) {
          return res?.data[0].dataSource;
        }
        return res?.data.dataSource;
      })
      .then(d => {
        setData(d);
        //防止没有notDisplay
        const haveProperty = Object.getOwnPropertyNames(d[0]).indexOf('notDisplay');

        //表格过滤notDisplay === false ,提示框warnNames相同的元素notDisplay === true要去掉此元素
        if (haveProperty !== -1 && ASCTools.getCountry() === 'PH') {
          let warnNames: Array<string> = [];
          let displayNames: Array<string> = [];
          const datasource = d.filter(item => {
            let flag = displayNames.find(e => e === item.warehouseName);
            if (flag === undefined) {
              if (item.notDisplay === true) {
                if (warnNames.find(e => e === item.warehouseName) === undefined) {
                  warnNames.push(item.warehouseName);
                }
              } else {
                displayNames.push(item.warehouseName);
                warnNames = warnNames.filter(e => e !== item.warehouseName);
              }
            }
            return item.notDisplay === false;
          });
          if (warnNames.length > 0) {
            setIsNonDefaultArea(true);
          } else {
            setIsNonDefaultArea(false);
          }
          setWarehouseName(warnNames);
          setData(datasource);
        }
      }),
  );

  const onSwitch = useMemoizedFn((toOpen, row, callback) => {
    const subscribeCode = () => {
      return subscribe({ warehouseId: row.warehouseId, solutionCode: row.solutionCode })
        .then(() => {
          Message.success(commonLocale['page.common.tips.success']);
          run();
        })
        .catch(e => {
          Message.error(e.message);
        })
        .finally(() => callback && callback());
    };
    if (
      row.solutionCode?.indexOf('LAZADA_INSTANT') === 0 ||
      (row.solutionCode?.indexOf('LAZADA_EXPRESS') === 0 && ASCTools.getCountry() === 'ID')
    ) {
      if (toOpen) {
        // todo show longitude and latiude input dialog
        const hasLngLat = !!row?.latitude && !!row?.longitude;
        // If need validate geocodes
        const validateGeoCodes = !!row?.validateGeoCodes;
        const dontEdit = !validateGeoCodes || hasLngLat;
        if (dontEdit) {
          return subscribeCode();
        }
        return editLongitude(
          { ...row, callback },
          {
            title: dontEdit
              ? i18n.formatMessage({
                  id: 'page.delivery.modal.instant.title',
                  defaultMessage: 'Subscribe the Instant Delivery',
                  app: 'lazada-seller-center',
                })
              : i18n.formatMessage({
                  id: 'page.delivery.modal.instant.on.lnglat.title',
                  defaultMessage: 'Warehouse address incomplete',
                  app: 'lazada-seller-center',
                }),
          },
        )
          .then(() => {
            Message.success(commonLocale['page.common.tips.success']);
            run();
          })
          .catch(() => {})
          .finally(() => callback && callback());
      } else {
        return edit({ ...row, callback })
          .then(() => {
            // after switch success, reload the list
            run();
          })
          .catch(() => {})
          .finally(() => callback && callback());
      }
    }

    if (toOpen) {
      return subscribeCode();
    }
    return unsubscribe({ warehouseId: row.warehouseId, solutionCode: row.solutionCode })
      .then(() => {
        Message.success(commonLocale['page.common.tips.success']);
        run();
      })
      .catch(e => {
        Message.error(e.message);
      })
      .finally(() => callback && callback());
  });

  const renderStatus = useMemoizedFn((val, idx, row) => {
    if (val === 1 || !!val) {
      return (
        <span className="text-green">
          <Icon type="success" className="mr5" />
          {row.statusText}
        </span>
      );
    }
    return (
      <span className="text-gray">
        <Icon type="error" className="mr5" />
        {row.statusText}
      </span>
    );
  });

  const renderAction = useMemoizedFn((val, idx, row) => {
    const key = `${row?.solutionCode}-${row?.warehouseId}-${idx}`;
    const spmd = 'd_switch';
    if (row.blockSubscriptionText) {
      return (
        <Balloon
          align="tl"
          triggerType="hover"
          trigger={
            <Switch
              disabled={true}
              checked={!!val}
              className="aplus-auto-exp"
              data-spm={spmd}
              data-more={row.solutionCode}
            />
          }
        >
          {row.blockSubscriptionText}
        </Balloon>
      );
    }
    return (
      <Switch
        onChange={e => {
          setLoading({ ...switchLoading, [key]: true });
          onSwitch(e, row, () => setLoading({ ...switchLoading, [key]: false }));
        }}
        loading={!!switchLoading?.[key]}
        checked={!!val}
        className="aplus-auto-exp"
        data-spm={spmd}
        data-more={row.solutionCode}
      />
    );
  });
  const doToggleCod = useMemoizedFn(status => {
    return axios
      .post('mtop.lazada.asc.seller.cod.update', {
        status,
      })
      .then(() => {
        // loadCod();
        setCodData({ ...codData, checked: status });
        Message.success(commonLocale['page.common.tips.success']);
      })
      .catch(e => {
        Message.error(e?.data?.data?.error || e.message);
      });
  });
  const toggleCod = useMemoizedFn(status => {
    if (status) {
      return doToggleCod(status);
    }
    Dialog.confirm({
      content: i18n.formatMessage({
        id: 'page.delivery.cod.confirm.content',
        defaultMessage: 'Are you sure to cancel cash on delivery?',
      }),
      messageProps: { type: 'warning' },
      onOk: () => {
        doToggleCod(status);
      },
    });
  });

  const onSort = useMemoizedFn((idx, order) => {
    const newData = [...data];
    newData.sort((a, b) => {
      let res = 'eq';
      if (a[idx] > b[idx]) {
        res = 'bt';
      } else if (a[idx] < b[idx]) {
        res = 'lt';
      }

      if (res === 'eq') {
        return 0;
      }

      if (order === 'asc') {
        if (res === 'lt') {
          return -1;
        }
        return 1;
      } else {
        if (res === 'lt') {
          return 1;
        }
        return -1;
      }
    });
    setData(newData);
  });

  const sortable = data?.length >= 6;

  //未覆盖的提示
  const showNonDefaultArea = () => {
    return (
      <>
        {isNonDefaultArea && ASCTools.getCountry() === 'PH' ? (
          <Message type="warning" style={{ marginTop: 6 }}>
            <span>Base on your warehouse's location，currently you have {warehouseNames.length} warehouses(</span>
            {warehouseNames?.map((item, index) => {
              return (
                <span>
                  {index === 0 ? null : ', '} {item}
                </span>
              );
            })}
            <span>) which we have no 3PL can provide service for you. You can click here to </span>
            <a href="https://sellercenter.lazada.com.ph/seller/helpcenter/what-is-delivered-by-seller-10556.html?spm=a2a15.helpcenter-psc.search-box.2.29e5731avwmp9q">
              apply for DBS
            </a>
          </Message>
        ) : null}
      </>
    );
  };

  return (
    <>
      <Card className="mb-standard">
        <p>
          <div
            dangerouslySetInnerHTML={{
              __html: i18n.formatMessage({
                id: 'page.delivery.cod.notice.description',
                defaultMessage:
                  'In this section, you can see the services activated for each of your warehouses. You can decide to activate or deactivate services by toggling the switch per row.',
              }),
            }}
          ></div>
        </p>

        {codData?.visible ? (
          <div>
            <p className="cod-switch-text">
              {i18n.formatMessage({
                id: 'page.delivery.cod.cashOnDelivery@label',
                defaultMessage: 'Cash on delivery',
                app: 'lazada-seller-center',
              })}
              <Switch className="ml5" onChange={toggleCod} checked={codData?.checked} />
            </p>
            <Message type="warning" style={{ marginTop: -6 }}>
              <SafeHtmlText
                style={{ display: 'flex' }}
                html={i18n.formatMessage({
                  id: 'page.delivery.cod.notice.content',
                  defaultMessage:
                    'Disable COD means you will not receive any COD orders, please be aware the impact on your order volume',
                  app: 'lazada-seller-center',
                })}
              />
            </Message>
            {showNonDefaultArea()}
          </div>
        ) : null}
      </Card>
      <Card className="mb-standard">
        <Table
          rowProps={(record, idx) => ({
            ['data-spm']: `service_row_${idx}`,
          })}
          onSort={onSort}
          dataSource={data}
        >
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.warehouseCode', defaultMessage: 'Warehouse Code' })}
            dataIndex="warehouseId"
            sortable={sortable}
          />

          <Table.Column
            title={i18n.formatMessage({
              id: 'page.account.setting.logisticsService',
              defaultMessage: 'Logistics Service',
            })}
            dataIndex="serviceName"
            sortable={sortable}
          />

          <Table.Column
            title={i18n.formatMessage({
              id: 'page.account.setting.subscribeStatus',
              defaultMessage: 'Subscribe Status',
            })}
            dataIndex="statusCode"
            sortable={sortable}
            cell={renderStatus}
          />

          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.action', defaultMessage: 'Action' })}
            dataIndex="statusCode"
            cell={renderAction}
          />
        </Table>
      </Card>
      <When condition={!!dbsData?.length}>
        <Card>
          <Table
            rowProps={(record, idx) => ({
              ['data-spm']: `dbs_row_${idx}`,
            })}
            dataSource={dbsData}
          >
            <Table.Column
              title={i18n.formatMessage({ id: 'page.account.setting.warehouse', defaultMessage: 'Warehouse' })}
              dataIndex=""
              cell={(val, idx, row) => {
                return i18n.formatMessage({
                  id: 'page.account.setting.all.warehouse',
                  defaultMessage: 'All Warehouse',
                });
              }}
            />

            <Table.Column
              title={i18n.formatMessage({
                id: 'page.account.setting.logisticsService',
                defaultMessage: 'Logistics Service',
              })}
              dataIndex="type"
            />

            <Table.Column
              title={i18n.formatMessage({
                id: 'page.account.setting.service.status',
                defaultMessage: 'Status',
              })}
              dataIndex="available"
              cell={renderStatus}
            />
          </Table>
        </Card>
      </When>
      <EditDialog />
      <EditLongitudeDialog />
    </>
  );
}

export default ResultPage;
