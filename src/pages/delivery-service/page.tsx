import commonLocale from '../../constants/common-locale';
import React from 'react';
import * as i18n from '@alife/lazada-i18n';
import { NavTab as Tab, PageContainer } from '@alife/asc-components';
import useUrlState from '@ahooksjs/use-url-state';
import SettingPageTitle from '../../components/setting-page-title';
import Services from './tab.services';
import { Button, Tag } from '@alifd/next';
import Options from './tab.options';
import { ASCTools } from '@alife/workstation-utils';

import './page.scss';

const useTabState = () => {
  const [{ tab }, _setState] = useUrlState({ tab: undefined });
  const setTab = tab => _setState({ tab });
  return [tab, setTab];
};

const currentBU = ASCTools.getBU();

function ResultPage() {
  const [currentTab, setTab] = useTabState();
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle
        actions={
          currentBU !== 'DARAZ' && (
            <Button
              text
              className="aplus-auto-exp"
              data-spm="d_delivery_to_myshipment"
              component="a"
              href="/apps/fulfillment/shipment"
            >
              <Tag style={{ marginRight: '4px' }} size="small" color="red">
                {i18n.formatMessage({
                  id: 'page.shipping.provider.title.action.tag',
                  defaultMessage: 'New',
                })}
              </Tag>
              {i18n.formatMessage({
                id: 'page.shipping.provider.title.action',
                defaultMessage: 'Go to My Shipment',
              })}
            </Button>
          )
        }
        title={commonLocale['page.common.nav.deliveryService']}
      />

      <Tab data-spm="deliver_tabs" shape="pure" activeKey={currentTab} onChange={setTab}>
        {currentBU !== 'DARAZ' && (
          <Tab.Item
            className="aplus-auto-exp"
            data-spm="d_services"
            key="services"
            title={commonLocale['page.common.nav.servicePerWh']}
          >
            <Services />
          </Tab.Item>
        )}
        <Tab.Item
          className="aplus-auto-exp"
          data-spm="d_options"
          key="options"
          title={commonLocale['page.common.nav.productDeliverOpt']}
        >
          <Options />
        </Tab.Item>
      </Tab>
    </PageContainer>
  );
}

export default ResultPage;
