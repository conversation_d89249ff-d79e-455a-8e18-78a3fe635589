import commonLocale from '../../constants/common-locale';
import React from 'react';
import { PageContainer } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import MRender from '../../components/mrender';
import SettingPageTitle from '../../components/setting-page-title';

import './page.scss';

function ResultPage() {
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.bankAccount']} />

      <MRender
        pannelKey="bank_panel"
        services={{
          render: () =>
            axios.get('mtop.global.merchant.subaccount.profile.render.lazada', {
              params: {
                groupId: 5,
              },
            }),
          precheck: data => {
            return axios.post('mtop.global.merchant.subaccount.profile.update.precheck.lazada', {
              fieldMap: JSON.stringify(data),
              groupId: 5,
            });
          },
          submit: data => {
            const { otpCode, ...rest } = data;
            return axios.post('mtop.global.merchant.subaccount.profile.update.lazada', {
              fieldMap: JSON.stringify(rest),
              otpCode,
              groupId: 5,
            });
          },
        }}
        needTips
      />
    </PageContainer>
  );
}

export default ResultPage;
