import * as i18n from '@alife/lazada-i18n'; // import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React from 'react';
import { HelpTip, NavTab, PageContainer, PopConfirm } from '@alife/asc-components';
import { Card, Table, Box, Button, Switch, Icon, Select, Form, Field, Input, Pagination, Message } from '@alifd/next';
import SettingPageTitle from '../../components/setting-page-title';
import { useFusionTable, useRequest } from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import * as locale from './locale';
import { changeStatus, deleteAccount, getRolesData, getSubAccountList, initRequest, deleterUserRole } from './service';
import get from 'lodash/get';
import { awaitCatch } from '@alife/workstation-utils';
import './page.scss';

const Column = Table.Column;

const useTabState = () => {
  const [{ tab }, _setState] = useUrlState({ tab: undefined });
  const setTab = x => _setState({ tab: x });
  return [tab, setTab];
};

function App() {
  const field = Field.useField([]);
  const { tableProps, search, paginationProps } = useFusionTable(getSubAccountList, {
    field,
  });

  const {
    tableProps: rolesTableProps,
    paginationProps: rolesPaginationProps,
    search: searchRolesData,
  } = useFusionTable(getRolesData, {});
  const { reset: resetRolesData } = searchRolesData;
  const { reset, submit } = search;
  const { data } = useRequest(initRequest);
  const [tab, setTab] = useTabState();

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle
        title={locale.manageSubAccount}
        actions={[
          <a href="/apps/setting/member/create" data-spm="d_subaccount">
            <Button type="secondary">
              {i18n.formatMessage({
                id: 's-wb-subaccount@addSubAccount',
                defaultMessage: 'Add Sub Account',
                app: 's-wb-subaccount',
              })}
            </Button>
          </a>,
          <a href="/apps/member/job" data-spm="d_job">
            <Button type="secondary">
              {i18n.formatMessage({
                id: 's-wb-subaccount@createNewRole',
                defaultMessage: 'Add New Role',
                app: 's-wb-subaccount',
              })}
            </Button>
          </a>,
        ]}
      />
      <NavTab activeKey={tab} onChange={setTab}>
        <NavTab.Item
          key="sub_account"
          title={i18n.formatMessage({
            id: 's-wb-subaccount@manageSubAccounts',
            defaultMessage: 'Manage Sub Accounts',
            app: 's-wb-subaccount',
          })}
        >
          <Card>
            <Box spacing={16}>
              <Form inline field={field} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                <Form.Item>
                  <Select
                    name="roles"
                    placeholder={i18n.formatMessage({
                      id: 's-wb-subaccount@roles',
                      defaultMessage: 'Roles',
                      app: 's-wb-subaccount',
                    })}
                    hasClear
                    dataSource={get(data, 'roles.options', [])}
                  />
                </Form.Item>
                <Form.Item>
                  <Select
                    name="status"
                    hasClear
                    placeholder={i18n.formatMessage({
                      id: 's-wb-subaccount@status',
                      defaultMessage: 'Status',
                      app: 's-wb-subaccount',
                    })}
                    dataSource={get(data, 'status.options', [])}
                  />
                </Form.Item>
                <Form.Item>
                  <Input
                    name="searchValue"
                    hasClear
                    placeholder={i18n.formatMessage({
                      id: 's-wb-subaccount@email',
                      defaultMessage: 'Email',
                      app: 's-wb-subaccount',
                    })}
                  />
                </Form.Item>
                <Form.Item>
                  <Button type="normal" onClick={reset}>
                    {i18n.formatMessage({
                      id: 'page.common.btn.reset',
                      defaultMessage: 'Reset',
                    })}
                  </Button>
                </Form.Item>
                <Form.Item>
                  <Button data-spm="d_view_history" className="aplus-auto-exp" type="primary" onClick={submit}>
                    {i18n.formatMessage({
                      id: 'page.common.btn.search',
                      defaultMessage: 'Search',
                    })}
                  </Button>
                </Form.Item>
              </Form>
              <Table
                {...tableProps}
                rowProps={(record, index) => ({
                  'data-spm': `tablerow_${index}`,
                })}
              >
                <Column
                  width={100}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@name',
                    defaultMessage: 'Name',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="userName"
                />

                <Column
                  width={100}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@email',
                    defaultMessage: 'Email',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="email"
                />

                <Column
                  width={100}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@roles',
                    defaultMessage: 'Roles',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="roleNames"
                  cell={(value, idx, record) => (
                    <Button text component="a" href={`/apps/member/jobview?id=${record.roleId}`}>
                      {value}
                    </Button>
                  )}
                />

                <Column
                  width={50}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@status',
                    defaultMessage: 'Status',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="isActiveStatus"
                  cell={(value: boolean, idx, record) => {
                    return (
                      <Box direction="row" justify="center">
                        <Switch
                          disabled={record?.isOwnerAccount}
                          checked={value}
                          onChange={async () => {
                            const [err] = await awaitCatch(
                              changeStatus({
                                id: record.id,
                              }),
                            );

                            if (!err) {
                              reset();
                            }
                          }}
                        />
                      </Box>
                    );
                  }}
                />

                <Column
                  width={80}
                  title={
                    <HelpTip
                      help={i18n.formatMessage({
                        id: '<EMAIL>',
                        defaultMessage:
                          'Owner account has full access and cannot be deleted or deactivated. Each shop only has 1 owner account',
                        app: 's-wb-subaccount',
                      })}
                    >
                      {i18n.formatMessage({
                        id: '<EMAIL>',
                        defaultMessage: 'Owner Account',
                        app: 's-wb-subaccount',
                      })}
                    </HelpTip>
                  }
                  dataIndex="masterIcon"
                  cell={value => {
                    return (
                      <Box direction="row" justify="center">
                        <Icon size="small" type={value} className={`.next-icon-${value}`} />
                      </Box>
                    );
                  }}
                />

                <Column
                  width={150}
                  lock="right"
                  title={i18n.formatMessage({ id: 'page.account.setting.actions', defaultMessage: 'Actions' })}
                  cell={(value, index, record) => {
                    const { id } = record;
                    return (
                      <Box direction="row" spacing={16}>
                        <a href={`/apps/setting/member/create?id=${id}`} data-spm="d_edit">
                          <Button text>{commonLocale['page.common.btn.edit']}</Button>
                        </a>
                        <PopConfirm
                          defaultVisible={false}
                          title={i18n.formatMessage({
                            id: 'lzd-seller-cem.components.product-selector.AreYouSureToDelete',
                            defaultMessage: 'Are you sure to delete?',
                          })}
                          onConfirm={async () => {
                            const res = await deleteAccount({ id })
                              .then(data => data)
                              .catch(err => err);
                            if (res.type === 'success') {
                              Message.success(res?.content ?? 'Save successfully');
                              reset();
                            } else {
                              Message.error(res?.data?.data?.error ?? 'Save Failed');
                            }
                          }}
                        >
                          <Button warning text>
                            {commonLocale['page.common.btn.delete']}
                          </Button>
                        </PopConfirm>
                        {/* <Button text data-spm="d_set_as_owner" disabled={isSetMasterDisable} onClick={() => {
                        }}>
                         {i18n.formatMessage({
                           id: 's-wb-subaccount@setAsOwnerAccount',
                           defaultMessage: 'Set as owner account',
                           app: 's-wb-subaccount',
                         })}
                        </Button> */}
                      </Box>
                    );
                  }}
                />
              </Table>
              <Pagination {...paginationProps} />
            </Box>
          </Card>
        </NavTab.Item>
        <NavTab.Item
          key="roles"
          data-spm="d_role_table"
          title={i18n.formatMessage({
            id: 's-wb-subaccount@manageRoles',
            defaultMessage: 'Manage Roles',
            app: 's-wb-subaccount',
          })}
        >
          <Card>
            <Box spacing={16}>
              <Table
                {...rolesTableProps}
                rowProps={(record, index) => ({
                  'data-spm': `roleTable_tablerow_${index}`,
                })}
              >
                <Column
                  width={200}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@roles',
                    defaultMessage: 'Roles',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="roleName"
                />

                <Column
                  width={200}
                  title={i18n.formatMessage({
                    id: 's-wb-subaccount@roleDescription',
                    defaultMessage: 'Role Description',
                    app: 's-wb-subaccount',
                  })}
                  dataIndex="description"
                />

                <Column
                  width={200}
                  title={i18n.formatMessage({ id: 'page.account.setting.actions', defaultMessage: 'Actions' })}
                  dataIndex="description"
                  cell={(value, idx, record) => (
                    <Box spacing={20} direction="row">
                      {record.allowChange ? (
                        <Button text component="a" href={`/apps/member/job?id=${record.id}`}>
                          {i18n.formatMessage({ id: 'page.common.btn.edit', defaultMessage: 'Edit' })}
                        </Button>
                      ) : null}
                      {record.allowChange ? (
                        <PopConfirm
                          defaultVisible={false}
                          title={i18n.formatMessage({
                            id: 'lzd-seller-cem.components.product-selector.AreYouSureToDelete',
                            defaultMessage: 'Are you sure to delete?',
                          })}
                          onConfirm={() => {
                            deleterUserRole({ id: record.id })
                              .then(data => {
                                Message.success(data?.content ?? 'Delete successfully');
                                resetRolesData();
                              })
                              .catch(err => {
                                Message.error(err?.data?.data?.error ?? 'Save Failed');
                              });
                          }}
                        >
                          <Button warning text>
                            {commonLocale['page.common.btn.delete']}
                          </Button>
                        </PopConfirm>
                      ) : null}

                      <Button text component="a" href={`/apps/member/jobview?id=${record.id}`}>
                        {i18n.formatMessage({
                          id: 's-wb-subaccount@viewPermission',
                          defaultMessage: 'View Permission',
                          app: 's-wb-subaccount',
                        })}
                      </Button>
                    </Box>
                  )}
                />
              </Table>
              <Pagination {...rolesPaginationProps} />
            </Box>
          </Card>
        </NavTab.Item>
      </NavTab>
      {/* <ShippingDialog title={locale['page.sellerProfile.shipping.provider.dialog.3pl.title']} /> */}
      {/* <HistoryDialog
         title={i18n.formatMessage({
           id: 'page.sellerProfile.shipping.rhTitle',
           defaultMessage: 'Request History',
         })}
        /> */}
    </PageContainer>
  );
}

export default App;
