import { axios } from '@alife/workstation-utils';

export const getSubAccountList = (data = {}, formData = {}) => {
  return axios
    .get('mtop.global.merchant.subaccount.user.list', {
      data: {
        ...data,
        ...formData,
      },
    })
    .then(res => ({
      total: res.data.data.pageInfo.total,
      list: res.data.data.dataSource,
    }));
};

export const changeStatus = data => {
  return axios
    .get('mtop.global.merchant.subaccount.user.status.change', {
      data,
    })
    .then(res => res?.data?.data);
};

export const initRequest = data => {
  return axios
    .get('mtop.global.merchant.subaccount.list.render', {
      data,
    })
    .then(res => {
      const map = (res.data?.data ?? [])?.reduce(
        (prev, { name, ...rest }) => Object.assign({}, prev, { [name]: rest }),
        {},
      );
      return map;
    });
};

export const deleteAccount = async data => {
  return axios.post('mtop.global.merchant.subaccount.user.delete', data).then(res => {
    return res.data?.data;
  });
};

export const getRolesData = async (data = {}, formData = {}) => {
  return axios
    .get('mtop.global.merchant.subaccount.role.list', {
      data: {
        ...data,
        ...formData,
      },
    })
    .then(res => {
      return {
        total: res.data.data.pageInfo.total,
        list: res.data.data.dataSource,
      };
    });
};

export const deleterUserRole = async data => {
  return axios.post('mtop.global.merchant.subaccount.role.delete', data).then(res => {
    return res.data?.data;
  });
};
