export interface saveParams {
  warehouseCode?: string;
  saveSchedule?: boolean;
  operatingHourNormalDTOList?: Array<{
    dayOfWeek?: string;
    fromTime: string;
    selected?: boolean;
    toTime?: string;
    warehouseCode?: string;
  }>;
  operatingHourCampaignDTOList?: Array<{
    name?: string;
    warehouseCode?: string;
    fromDay?: string;
    toDay?: string;
    fromTime?: string;
    toTime?: string;
  }>;
  sellerId?: string;
  validationError?: Array<any>;
  isAcceptedSuggestion?: boolean;
}

import { axios } from '@alife/workstation-utils';

//// mtop.lazada.rc.operating.hour.get  mtop.alibaba.logistics.firstmile.operationtime.getoperationtime
export const getSchedules = params => {
  const { warehouseCode } = params || {};

  if (!warehouseCode) return Promise.resolve({});
  return axios
    .get('mtop.lazada.rc.operating.hour.get', {
      data: {
        operatingHourQueryDtoJson: JSON.stringify({
          warehouseCode,
          ...params,
          sellerId: window?.dadaConfig?.sellerId,
        }),
      },
    })
    .then(res => res?.data?.module)
    .catch(e => console.log(e));
};

export const getWarehouseList = () =>
  axios
    .get('mtop.global.merchant.subaccount.multi.address.warehouse.list', {
      data: {
        current: 1,
        pageSize: 100,
      },
    })
    .then(res => res?.data?.data)
    .catch(e => console.log(e));

////// mtop.alibaba.logistics.firstmile.operationtime.saveoperationtime -> mtop.lazada.rc.operating.hour.save
export const saveSchedules = (params: saveParams) => {
  return axios
    .post('mtop.lazada.rc.operating.hour.save', {
      operatingHourSaveDtoJson: JSON.stringify({
        warehouseCode: '',
        sellerId: window?.dadaConfig?.sellerId,
        ...params,
      }),
    })
    .then(res => res?.data?.module)
    .catch(e => e);
};

export const getSuggestions = () => {
  return axios.get('mtop.alibaba.logistics.firstmile.operationtime.suggestions').then(res => res?.data?.data);
};
