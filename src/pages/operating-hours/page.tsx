import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React, { useEffect, useRef, useState } from 'react';
import * as locale from './locale';
import { NavTab as Tab, PageContainer, PageTitle, SubmitAffix, useFormDialog } from '@alife/asc-components';
import { Card, Button, Tag, Message, Box } from '@alifd/next';
import SettingPageTitle from '../../components/setting-page-title';
import CampaignSchedule from './components/campaign-schedule';
import NormalSchedule from './components/normal-schedule';
import DialogContent, { dialogFooter } from './components/dialog-content';
import { getWarehouseList, getSchedules, saveSchedules, getSuggestions } from './service';
import { validateCampaign, validateNormal } from './until';

import './page.scss';
import { useMemoizedFn, useMount, useRequest } from 'ahooks';
import { When } from '@alife/workstation-utils';

// const defaultWarehouse = { code: 'DEFAULT', title: locale.masterConfig };

interface SaveParams {
  normalSchedule?: Array<CampaignItems>;
  saveSchedule?: boolean;
  warehouseCode?: string;
  pickupStopExtId?: string;
  campaignSchedule?: Array<CampaignItems>;
}
export interface CampaignItems {
  name?: string;
  fromDay?: string;
  warehouseCode?: string;
  toDay?: string;
  fromTime?: string;
  toTime?: string;
}

function ResultPage() {
  useMount(() => {
    location.href = '/portal/setting/operating_hours';
  });
  const { data: warehouseList } = useRequest(getWarehouseList);
  const dataSource = warehouseList?.dataSource || [];

  const { data: suggestionsData } = useRequest(getSuggestions);
  const [disabled, setDisabled] = useState(true);
  const [message, setMessage] = useState('');
  const { data, loading, run, refresh } = useRequest(params =>
    getSchedules({ warehouseCode: dataSource?.[0]?.code, ...params }),
  );
  const campaigns = useRef<Array<CampaignItems>>();
  const warehouseCode = useRef<string>('');

  const { operatingHourNormalDTOList = [], operatingHourCampaignDTOList = [] } = data || {};
  const { suggestions = [] } = suggestionsData || {};

  const [SuggestionDialog, { edit }] = useFormDialog({
    content: DialogContent,
    footer: dialogFooter,
    hasMessage: false,
    dialogProps: {
      closeable: false,
    },
  });

  useEffect(() => {
    if (warehouseList?.dataSource?.length) {
      const code = dataSource?.[0]?.code;
      warehouseCode.current = code;
      run({ warehouseCode: code });
      suggestions?.length && edit({ suggestions, warehouses: warehouseList?.dataSource, disabled: true, refresh });
    }
  }, [suggestions, warehouseList]);

  const onChange = useMemoizedFn(v => {
    if (!v) return;
    warehouseCode.current = v;
    run({ warehouseCode: v });
  });

  const getCampaigns = useMemoizedFn(v => {
    campaigns.current = v;
  });

  const saveSchedule = useMemoizedFn((params?: SaveParams) => {
    if (disabled && !params) {
      setDisabled(false);
      return;
    }

    const finalCampaignSchedule = (params?.campaignSchedule || campaigns.current)?.map(el => {
      el.warehouseCode = warehouseCode.current;
      return el;
    });
    const schedule = (params?.normalSchedule || operatingHourNormalDTOList)?.filter(el => {
      el.warehouseCode = warehouseCode.current;
      return !!el;
    });

    if (!validateCampaign(finalCampaignSchedule) || !validateNormal(schedule)) {
      Message.error(locale.scheduleEmptyValue);
      return;
    }

    saveSchedules({
      warehouseCode: warehouseCode.current,
      ...params,
      operatingHourNormalDTOList: schedule,
      operatingHourCampaignDTOList: finalCampaignSchedule,
    }).then(res => {
      const { type, ret = '' } = res || {};
      const { validationErrorCodes = [] } = res?.data?.data?.module || {};
      if (!!validationErrorCodes?.length || type === 'MtopError') {
        const msg = validationErrorCodes?.map(el => locale[el?.code] || '').join(';');
        setMessage(msg || ret);
      } else {
        setMessage('');
        refresh();
        setDisabled(true);
        Message.success(commonLocale['page.common.tips.success']);
      }
    });
  });

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle
        actions={
          <Button
            text
            className="aplus-auto-exp"
            data-spm="d_operating_to_myshipment"
            component="a"
            href="/apps/fulfillment/shipment"
          >
            <Tag style={{ marginRight: '4px' }} size="small" color="red">
              {i18n.formatMessage({
                id: 'page.shipping.provider.title.action.tag',
                defaultMessage: 'New',
              })}
            </Tag>
            {i18n.formatMessage({
              id: 'page.shipping.provider.title.action',
              defaultMessage: 'Go to My Shipment',
            })}
          </Button>
        }
        title={commonLocale['page.common.nav.operatingHours']}
      />

      <Message type="notice" children={locale.globalTip} />

      <Card className="aplus-module-auto-exp" data-spm="operating-hours">
        <Tab onChange={onChange} size="small" excessMode="dropdown" shape="capsule">
          {[...dataSource].map(item => (
            <Tab.Item key={item.code} title={item.title}></Tab.Item>
          ))}
        </Tab>

        <When condition={!!message}>
          <Message style={{ marginTop: '12px' }} type="error">
            {message}
          </Message>
        </When>

        <SuggestionDialog title={locale.suggestionTitle} />

        <NormalSchedule
          key={`normal_${warehouseCode.current}_${loading ? 1 : 0}`}
          disabled={disabled}
          normalSchedule={operatingHourNormalDTOList}
          message={locale.normalRuleTip}
        />
        <CampaignSchedule
          disabled={disabled}
          key={`campaign_${warehouseCode.current}_${loading ? 1 : 0}`}
          getCampaigns={getCampaigns}
          text={locale.addOneMore}
          campaignSchedule={operatingHourCampaignDTOList}
          message={locale.campaignRuleTip}
        />
      </Card>

      <SubmitAffix>
        <When condition={!disabled}>
          <Button onClick={() => setDisabled(true)} type="secondary" className="aplus-auto-exp" data-spm="d_cancel">
            {locale.cancel}
          </Button>
        </When>
        <Button onClick={() => saveSchedule()} type="primary" className="aplus-auto-exp" data-spm="d_modify">
          {disabled ? locale.modify : commonLocale['page.common.btn.submit']}
        </Button>
      </SubmitAffix>
    </PageContainer>
  );
}

export default ResultPage;
