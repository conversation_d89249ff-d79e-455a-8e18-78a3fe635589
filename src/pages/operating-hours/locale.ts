import * as i18n from '@alife/lazada-i18n';

export const globalTip = i18n.formatMessage({
  id: 'operating.schedule@global-rule-prompt',
  defaultMessage: 'Global will apply to all warehouse as default',
});

export const masterConfig = i18n.formatMessage({
  id: 'operating.schedule@global-config',
  defaultMessage: 'Global config',
});

export const normalSchedule = i18n.formatMessage({
  id: 'operating.schedule@normal-schedule',
  defaultMessage: 'Normal Schedule',
});

export const Monday = i18n.formatMessage({
  id: 'operating.schedule@monday',
  defaultMessage: 'Monday',
});

export const Tuesday = i18n.formatMessage({
  id: 'operating.schedule@tuesday',
  defaultMessage: 'Tuesday',
});

export const Wednesday = i18n.formatMessage({
  id: 'operating.schedule@wednesday',
  defaultMessage: 'Wednesday',
});

export const Thursday = i18n.formatMessage({
  id: 'operating.schedule@thursday',
  defaultMessage: 'Thursday',
});

export const Friday = i18n.formatMessage({
  id: 'operating.schedule@friday',
  defaultMessage: 'Friday',
});

export const Sunday = i18n.formatMessage({
  id: 'operating.schedule@sunday',
  defaultMessage: 'Sunday',
});

export const normalRuleTip = i18n.formatMessage({
  id: 'operating.schedule@normal-schedule-rule-tip',
  defaultMessage: 'normal schedule Rule Tip',
});

export const scheduleEmptyValue = i18n.formatMessage({
  id: 'operating.schedule@scheduel-has-empty-value',
  defaultMessage: 'Schedule has empty value',
});

export const campaignSchedule = i18n.formatMessage({
  id: 'operating.schedule@campaign-schedule',
  defaultMessage: 'Campaign Schedule',
});

export const operatingHours = i18n.formatMessage({
  id: 'operating.schedule@operating-hours',
  defaultMessage: 'Operating Hours',
});

export const campaignRuleTip = i18n.formatMessage({
  id: 'operating.schedule@campaign-schedule-rule-tip',
  defaultMessage: 'campaign schedule Rule Tip',
});

export const campaign = i18n.formatMessage({
  id: 'operating.schedule@campaign',
  defaultMessage: 'Campaign',
});

export const campaignPeriod = i18n.formatMessage({
  id: 'operating.schedule@campaign-period',
  defaultMessage: 'Campaign Period',
});

export const addOneMore = i18n.formatMessage({
  id: 'operating.schedule@add-one-more',
  defaultMessage: '+ Add One More',
});

export const applyToAllWarehouse = i18n.formatMessage({
  id: 'operating.schedule@apply-to-all-warehouse',
  defaultMessage: 'apply to all warehouse',
});

export const deleteSchedule = i18n.formatMessage({
  id: 'operating.schedule@delete-schedule',
  defaultMessage: 'Delete Schedule',
});

export const cancel = i18n.formatMessage({
  id: 'operating.schedule@cancel',
  defaultMessage: 'Cancel',
});

export const sureDeleteSchedule = i18n.formatMessage({
  id: 'operating.schedule@are-you-sure-to-delete-this-schedule',
  defaultMessage: 'Are you sure to delete this schedule',
});

export const campaignName = i18n.formatMessage({
  id: 'operating.schedule@campaign-name',
  defaultMessage: 'Campaign Name',
});

export const ok = i18n.formatMessage({
  id: 'operating.schedule@ok',
  defaultMessage: 'OK',
});

export const modify = i18n.formatMessage({
  id: 'operating.schedule@modify',
  defaultMessage: 'Modify',
});

export const OP_NORMAL_MIN_WORK_HOUR_WEDNESDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_WEDNESDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_WEDNESDAY',
});

export const OP_INVALID_WORK_HOUR_MONDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_MONDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_MONDAY',
});

export const OP_INVALID_WORK_HOUR_WEDNESDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_WEDNESDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_WEDNESDAY',
});

export const OP_INVALID_WORK_HOUR_SUNDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_SUNDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_SUNDAY',
});

export const OP_NORMAL_MIN_WORK_HOUR_MONDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_MONDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_MONDAY',
});

export const OP_NORMAL_MIN_WORK_HOUR_SUNDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_SUNDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_SUNDAY',
});

export const OP_INVALID_WORK_HOUR_SATURDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_SATURDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_SATURDAY',
});

export const OP_INVALID_WORK_HOUR_THURSDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_THURSDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_THURSDAY',
});

export const OP_NORMAL_MIN_WORK_HOUR_SATURDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_SATURDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_SATURDAY',
});

export const OP_NORMAL_MIN_WORK_HOUR_THURSDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_THURSDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_THURSDAY',
});

export const OP_INVALID_WORK_HOUR_FRIDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_FRIDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_FRIDAY',
});

export const OP_NORMAL_MIN_WORK_HOUR_FRIDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_FRIDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_FRIDAY',
});

export const OP_CAMPAIGN_MIN_WORK_HOUR = i18n.formatMessage({
  id: 'OP_CAMPAIGN_MIN_WORK_HOUR',
  defaultMessage: 'OP_CAMPAIGN_MIN_WORK_HOUR',
});

export const OP_NORMAL_MIN_WORK_DAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_DAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_DAY',
});

export const OP_CAMPAIGN_OVERLAP = i18n.formatMessage({
  id: 'OP_CAMPAIGN_OVERLAP',
  defaultMessage: 'OP_CAMPAIGN_OVERLAP',
});

export const OP_NORMAL_MIN_WORK_HOUR_TUESDAY = i18n.formatMessage({
  id: 'OP_NORMAL_MIN_WORK_HOUR_TUESDAY',
  defaultMessage: 'OP_NORMAL_MIN_WORK_HOUR_TUESDAY',
});

export const OP_INVALID_WORK_HOUR_TUESDAY = i18n.formatMessage({
  id: 'OP_INVALID_WORK_HOUR_TUESDAY',
  defaultMessage: 'OP_INVALID_WORK_HOUR_TUESDAY',
});

export const LZD_RC_OPERATING_HOUR_REPEAT_CLICK = i18n.formatMessage({
  id: 'LZD_RC_OPERATING_HOUR_REPEAT_CLICK',
  defaultMessage: 'LZD_RC_OPERATING_HOUR_REPEAT_CLICK',
});

export const Confirm = i18n.formatMessage({
  id: 'page.common.btn.confirm',
  defaultMessage: 'Confirm',
  app: 'lazada-seller-center',
});

export const reject = i18n.formatMessage({
  id: 'operating.schedule.suggestion.reject',
  defaultMessage: 'Reject',
});

export const suggestionTitle = i18n.formatMessage({
  id: 'operating.schedule.suggestion.title',
  defaultMessage: 'Suggestion Schedule',
});

export const popupTitle = i18n.formatMessage({
  id: 'operating.schedule.suggestion.popup.title',
  defaultMessage: 'Operating Hours Update',
});

export const popupSubTitle = i18n.formatMessage({
  id: 'operating.schedule.suggestion.popup.subtitle',
  defaultMessage:
    'Base on your last 30 days operation activities (pickup, etc),Our system algorithm has recommended the following update for your acknowledge',
});

export const popupNotice = i18n.formatMessage({
  id: 'operating.schedule.suggestion.popup.notice',
  defaultMessage: 'Note: content to be updated later',
});

export const campainScheduleTitle = i18n.formatMessage({
  id: 'page.account.setting.campaignSchedule',
  defaultMessage: 'Campaign Schedule',
});

export const normalShecduleTitle = i18n.formatMessage({
  id: 'page.account.setting.normalSchedule',
  defaultMessage: 'Normal Schedule',
});
