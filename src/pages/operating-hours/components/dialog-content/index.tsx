import { Card, Message, Tab, Box, Button } from '@alifd/next';
import React, { useEffect } from 'react';
import NormalSchedule from '../normal-schedule';
import { validateCampaign, validateNormal } from '../../until';
import { saveSchedules } from '../../service';
import * as locale from '../../locale';

export const dialogFooter = props => {
  const { close, record } = props || {};
  const { suggestions = [], refresh } = record || {};

  const confirm = () => {
    const promises: Array<Promise<object>> = [];
    // Most situation have only one suggestion
    suggestions?.map(el => {
      const { normalSchedule = [], campaignSchedule, warehouseCode } = el || {};
      promises.push(saveSchedule({ campaignSchedule, normalSchedule, warehouseCode }));
    });

    Promise.all(promises).finally(() => {
      refresh && refresh();
      close && close();
    });
  };

  const saveSchedule = ({ campaignSchedule, normalSchedule, warehouseCode }) => {
    if (!validateCampaign(campaignSchedule) || !validateNormal(normalSchedule)) {
      Message.error(locale.scheduleEmptyValue);
      return Promise.reject({});
    }

    return saveSchedules({
      campaignSchedule,
      normalSchedule,
      warehouseCode,
      isAcceptedSuggestion: true,
    }).then(res => {
      const { validationError = [] } = res || {};
      if (!!validationError?.length) {
        const message = validationError?.map(el => locale[el?.code] || '').join(';');
        Message.error(message);
      }
      return res;
    });
  };

  return (
    <Box spacing={[0, 12]} direction="row" justify="flex-end" align="flex-end">
      <Button
        data-more={`${suggestions?.map(el => el?.warehouseCode)?.join(';')}`}
        className="aplus-auto-exp"
        data-spm="d_accept_suggesstion"
        type="primary"
        onClick={confirm}
      >
        {locale.Confirm}
      </Button>
      <Button className="aplus-auto-exp" data-spm="d_cancel_suggesstion" onClick={close}>
        {locale.reject}
      </Button>
    </Box>
  );
};

function DialogContent({ record, close }) {
  const { disabled, suggestions = [], warehouses = [] } = record || {};
  // remove close icon
  useEffect(() => {
    const el = document.querySelector('.next-dialog-close');
    el && el.remove();
  }, []);

  const getWarehouseName = (code, warehouseList) => {
    const warehouse = warehouseList?.find(el => el?.code === code);
    return warehouse?.title || code || '';
  };

  return (
    <Card>
      <Card.Header title={locale.popupTitle} subTitle={locale.popupSubTitle}></Card.Header>

      <Card.Content>
        <Box spacing={[12, 0]}>
          <Message type="notice">{locale.popupNotice}</Message>

          <Tab size="small" excessMode="dropdown" shape="capsule">
            {[...suggestions].map((item, index) => {
              const { normalSchedule = [], warehouseCode } = item || {};
              return (
                <Tab.Item
                  style={{ borderRadius: '8px', marginRight: '8px', borderRightColor: '#c4c6cf' }}
                  key={`pickupStopExtId_${index}`}
                  title={getWarehouseName(warehouseCode, warehouses)}
                >
                  <NormalSchedule title="Schedules" normalSchedule={normalSchedule} disabled={disabled} />
                </Tab.Item>
              );
            })}
          </Tab>
        </Box>
      </Card.Content>
    </Card>
  );
}

export default DialogContent;
