import React from 'react';
import { Box, TimePicker } from '@alifd/next';

interface IProps {
  fromTime?: string;
  toTime?: string;
  minuteStep?: number;
  className?: string;
  onChange?: (v) => void;
  disabled?: boolean;
  name?: string;
}

function OperatingHours({ fromTime, toTime, disabled, minuteStep = 30, className = '', onChange }: IProps) {
  const valueChange = (v, type) => {
    onChange && onChange({ [type]: v });
  };
  return (
    <Box className={className} direction="row">
      <TimePicker
        disabled={disabled}
        onChange={v => valueChange(v, 'fromTime')}
        defaultValue={fromTime || ''}
        format="HH:mm"
        minuteStep={minuteStep}
      />
      <Box align="center" justify="center" margin={[0, 8]}>
        -
      </Box>
      <TimePicker
        disabled={disabled}
        onChange={v => valueChange(v, 'toTime')}
        defaultValue={toTime || ''}
        format="HH:mm"
        minuteStep={minuteStep}
      />
    </Box>
  );
}

export default OperatingHours;
