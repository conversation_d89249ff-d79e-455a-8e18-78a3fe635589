import React from 'react';
import { Box, Message, Typography } from '@alifd/next';
import ScheduleLine from './line';
import { completeWeeks } from './default-schedule';
import '../../service';
import { When } from '@alife/workstation-utils';
import * as locale from '../../locale';

interface IProps {
  normalSchedule?: Array<{
    dayOfWeek?: string;
    fromTime?: string;
    id?: string | number;
    selected?: boolean;
    toTime?: string;
    warehouseCode?: string;
  }>;
  title?: string;
  message?: string;
  disabled?: boolean;
}

function NormalSchedule({ title = locale.normalShecduleTitle, disabled, message, normalSchedule = [] }: IProps) {
  const schedules = completeWeeks(normalSchedule);
  return (
    <Box margin={[24, 0]}>
      <Typography.H4>{title}</Typography.H4>
      <When condition={!!message}>
        <Message type="notice" children={message} />
      </When>
      {schedules?.map((el, index) => {
        return <ScheduleLine disabled={disabled} item={el} index={index} />;
      })}
    </Box>
  );
}

export default NormalSchedule;
