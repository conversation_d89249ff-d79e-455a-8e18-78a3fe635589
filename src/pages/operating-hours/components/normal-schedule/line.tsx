import React from 'react';
import { Box, Checkbox, TimePicker } from '@alifd/next';
import OperatingHours from '../operating-hours';

interface IProps {
  item: {
    dayOfWeek?: string;
    fromTime?: string;
    id?: string | number;
    selected?: boolean;
    toTime?: string;
    warehouseCode?: string;
  };
  index: number;
  disabled?: boolean;
}

function ScheduleLine({ item, index, disabled }: IProps) {
  const valueChange = v => {
    const { fromTime, toTime } = v || {};
    if (!!fromTime) item.fromTime = fromTime;
    if (!!toTime) item.toTime = toTime;
  };

  const checkChange = v => {
    item.selected = !!v;
  };

  return (
    <Box margin={[8, 0]} align="center" direction="row">
      <Checkbox
        disabled={disabled}
        defaultChecked={!!item.selected}
        onChange={checkChange}
        value={item?.id}
        style={{ width: '150px' }}
        label={item?.dayOfWeek}
      ></Checkbox>
      <OperatingHours disabled={disabled} onChange={valueChange} fromTime={item.fromTime} toTime={item.toTime} />
    </Box>
  );
}

export default ScheduleLine;
