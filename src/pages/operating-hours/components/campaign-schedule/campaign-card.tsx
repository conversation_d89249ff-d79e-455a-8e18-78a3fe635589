import React from 'react';
import { Box, Icon, Form, Input, DatePicker, Button } from '@alifd/next';
import OperatingHours from '../operating-hours';
import moment from 'moment';
import { setValue, getValue, formatDate } from '../../until';
import * as locale from '../../locale';

const Item = Form.Item;
const RangePicker = DatePicker.RangePicker;

export interface CampaignItems {
  name?: string;
  fromDay?: string;
  toDay?: string;
  fromTime?: string;
  toTime?: string;
}

interface IProps {
  title?: string;
  name?: string;
  item: CampaignItems;
  deleteItem?: (v, index) => void;
  index?: number;
  campaigns: Array<CampaignItems>;
  disabled?: boolean;
}

function campaignCard({ title = 'campaign', campaigns = [], disabled, item, index = 0, deleteItem }: IProps) {
  const valueChange = (v, element) => {
    const date = getValue('date', v);
    const hours = getValue('hours', v);
    const name = getValue('name', v);
    if (!!date) {
      !!date?.[0] && setValue(formatDate(date?.[0], 'MM/DD/YYYY'), 'fromDay', item);
      !!date?.[1] && setValue(formatDate(date?.[1], 'MM/DD/YYYY'), 'toDay', item);
    }

    if (!!hours) {
      let { fromTime = '', toTime = '' } = { ...item, ...hours };
      !!fromTime && setValue(fromTime, 'fromTime', item);
      !!toTime && setValue(toTime, 'toTime', item);
      console.log(item, fromTime, toTime);
    }

    if (!!name) item = { ...item, name };

    campaigns.splice(index, 1, item);
  };

  const startValue = moment(item?.fromDay, 'MM/DD/YYYY', true);
  const endValue = moment(item?.toDay, 'MM/DD/YYYY', true);

  return (
    <Box className="campaign-item">
      <Box className="item-title" direction="row" align="center" justify="space-between">
        {`${title} ${index + 1}`}
        <Button disabled={disabled} onClick={() => deleteItem && deleteItem(item, index)} text>
          <Icon type="delete" />
        </Button>
      </Box>

      <Form disabled={disabled} key={Math.random().toFixed(5)} className="form-wrap" onChange={valueChange}>
        <Item required label={locale.campaignName}>
          <Input defaultValue={item?.name} name="name" />
        </Item>
        <Item required label={locale.campaignPeriod}>
          <RangePicker defaultValue={[startValue, endValue]} name="date" format="MM/DD/YYYY" />
        </Item>
        <Item required label={locale.operatingHours}>
          <OperatingHours name="hours" fromTime={item?.fromTime} toTime={item?.toTime} />
        </Item>
      </Form>
    </Box>
  );
}

export default campaignCard;
