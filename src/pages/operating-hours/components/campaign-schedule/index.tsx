import React, { useEffect, useRef, useState } from 'react';
import { Box, Button, Message, Typography } from '@alifd/next';
import CampaignCard, { CampaignItems } from './campaign-card';
import { When } from '@alife/workstation-utils';
import * as locale from '../../locale';
import './index.scss';

interface IProps {
  campaignSchedule?: Array<CampaignItems>;
  title?: string;
  message?: string;
  text?: string;
  getCampaigns?: (v) => void;
  disabled?: boolean;
}

function CampaignSchedule({
  text,
  disabled,
  title = locale.campainScheduleTitle,
  message,
  campaignSchedule = [],
  getCampaigns,
}: IProps) {
  const [campaigns, setCampaign] = useState<Array<CampaignItems>>([]);
  !!getCampaigns && getCampaigns(campaigns);

  useEffect(() => {
    setCampaign(campaignSchedule);
  }, [campaignSchedule]);

  const add = () => {
    setCampaign(campaigns.concat({}));
  };

  const deleteItem = (item, index) => {
    campaigns.splice(index, 1);
    setCampaign([...campaigns]);
  };

  return (
    <Box className="campaign-schedule-area">
      <Typography.H4>{title}</Typography.H4>
      <Message type="notice" children={message} />
      <Box className="campaigns-area">
        {campaigns?.map((item, index) => (
          <CampaignCard
            disabled={disabled}
            title={locale.campaign}
            campaigns={campaigns}
            deleteItem={deleteItem}
            item={item}
            index={index}
          />
        ))}
      </Box>
      <When condition={!disabled}>
        <Box className="bottom-button">
          <Button onClick={add} type="secondary">
            {text}
          </Button>
        </Box>
      </When>
    </Box>
  );
}

export default CampaignSchedule;
