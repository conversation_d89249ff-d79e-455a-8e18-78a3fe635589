export const setValue = (value, key, obj) => {
  if (!!value) obj[key] = value;
  return obj;
};

export const getValue = (key, obj) => {
  return obj?.[key];
};

export const formatDate = (v, format) => {
  return v?.format?.(format);
};

export const validateCampaign = value => {
  if (Array.isArray(value)) {
    const isAllValidated = value.every(row => row.fromTime && row.toTime && row.fromDay && row.toDay && row.name);
    return isAllValidated;
  }
  return false;
};

export const validateNormal = value => {
  if (Array.isArray(value)) {
    return value.filter(row => row.selected).every(row => !!row.fromTime && !!row.toTime);
  }
  return false;
};
