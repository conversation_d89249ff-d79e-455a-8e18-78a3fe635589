import React from 'react';
import { Card, Message, Typography } from '@alifd/next';
import { PageContainer, PageTitle } from '@alife/asc-components';
import { useRequest } from 'ahooks';
import { getStatus } from './service';
import { Case, Switch, Url } from '@alife/workstation-utils';
import { ExperiencePage } from './experience-detail';
import commonLocale from '../../constants/common-locale';
import { formMaps } from './experience-detail/constants';
import SocialMedia from './social-media';
import OfflineStore from './offline-store';
import SupplyChain from './suplly-chain';
import TikTokShop from './tt-shop';
import './page.scss';

export type FromCodeType = keyof typeof formMaps;
// 由于后期迭代原因，新增了后端接口及一些字段，无法在原来的代码上改造，所以这里这部分 code 是另外的Case
const FROM_CODE_CASE = ['SOCIAL_MEDIA', 'OFFLINE_STORE', 'SUPPLY_CHAIN', 'TIKTOK_SHOP'];

function Page() {
  const formCode = Url.getQueryParams()['formCode'] || '';
  const { data } = useRequest(getStatus);
  const taskList = data?.taskList || [];
  const getProgress = () => {
    let done = 0;
    taskList &&
      taskList.map(item => {
        if (item.taskStatus === 'done') {
          done++;
        }
      });
    return '（' + done + ' / 6）';
  };

  return (
    <div data-spm={'account_setting_experience'}>
      <Switch>
        <Case when={!formCode}>
          <PageContainer data-spm="experience_list">
            <PageTitle
              data-spm="title"
              breadcrumb={[
                {
                  label: commonLocale['page.common.nav.home'],
                  link: '/apps/home/<USER>',
                },
                {
                  label: commonLocale['page.common.nav.setting'],
                  link: '/apps/setting/index',
                },
                {
                  label: commonLocale['page.account.experience.businessExperience'],
                },
              ]}
              title={commonLocale['page.account.experience.businessExperience'] + getProgress()}
              subTitle={commonLocale['page.account.experience.subtitle']}
            />
            <Message type="notice" className="notice">
              {commonLocale['page.account.experience.noticeMessage']}
            </Message>
            <Card>
              <div className="list aplus-auto-exp" data-spm="experience_form_list">
                {taskList &&
                  taskList.map((item, index) => {
                    return (
                      <div
                        className="button aplus-auto-exp"
                        key={index}
                        data-spm={`d_${item?.taskCode?.toLowerCase()}_click`}
                        onClick={() => {
                          location.href = Url.addQueryString('formCode', item?.taskCode);
                        }}
                      >
                        <span className={`add-icon ${item.taskCode}`}>
                          <span
                            className="complete"
                            style={{ display: item.taskStatus === 'done' ? '' : 'none' }}
                          ></span>
                        </span>
                        <div className="config-item-info">
                          <Typography.H5>{item.taskName}</Typography.H5>
                          <p className="config-item-desc">{item.desc}</p>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </Card>
          </PageContainer>
        </Case>
        <Case when={formCode === 'SOCIAL_MEDIA' && FROM_CODE_CASE.includes(formCode)}>
          <SocialMedia />
        </Case>
        <Case when={formCode === 'OFFLINE_STORE' && FROM_CODE_CASE.includes(formCode)}>
          <OfflineStore />
        </Case>
        <Case when={formCode === 'SUPPLY_CHAIN' && FROM_CODE_CASE.includes(formCode)}>
          <SupplyChain />
        </Case>
        <Case when={formCode === 'TIKTOK_SHOP' && FROM_CODE_CASE.includes(formCode)}>
          <TikTokShop />
        </Case>
        <Case when={!!formCode && !FROM_CODE_CASE.includes(formCode)}>
          <ExperiencePage id={formCode} />
        </Case>
      </Switch>
    </div>
  );
}

export default Page;
