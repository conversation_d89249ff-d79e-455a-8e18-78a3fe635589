import { <PERSON>, <PERSON>ton, Card, Checkbox, Message, Typography } from '@alifd/next';
import { AddressSelector, SubmitAffix } from '@alife/asc-components';
import withTrackEffects from '@alife/form-track-plugin';
import { createForm, onFieldMount, onFieldValueChange, onFormMount, Field as IField } from '@formily/core';
import { Form, Input, FormItem, Submit, Radio } from '@formily/next';
import { Field, FormConsumer } from '@formily/react';
import React, { useMemo } from 'react';
import ExperienceContainer from '../components/page-container';
import { EnumSellerStatus, LOCATION_LIST, OSC_CONFIG_MAP, SELLER_STATUS_LIST } from '../constants';
import * as locale from '../locale';
import { querySpecialtyInfo, SpecialtyInfo, submitFactoryInfo } from '../service';
import commonLocale from '../../../constants/common-locale';
import { useRequest, useSetState } from 'ahooks';
import UploadDocumentFormily from '../components/upload-document';
import pick from 'lodash/pick';
import QualityCheckMessage from '../components/msg';
import Banner from '../components/banner';
import { useOsc } from '@alife/lago-utils';
import '../page.scss';

const SupplyChain: React.FC = () => {
  const [state, setState] = useSetState<{ agree: boolean }>({ agree: false });

  const { data } = useOsc<{ list: any }>('38');
  const config = OSC_CONFIG_MAP['SUPPLY_CHAIN'];
  const banner = data?.[config.banner];
  const benefitList = data?.[config.benefit];

  const { autoFormTrack, setError } = withTrackEffects({
    formKey: `business-experience-SUPPLY_CHAIN`,
  });

  const { loading, run } = useRequest(submitFactoryInfo, {
    manual: true,
    onSuccess: data => {
      Message.success(commonLocale['page.common.tips.success']);
      // 当前页刷新
      location.href = `/apps/setting/experience?formCode=SUPPLY_CHAIN`;
    },
    onError: (error: any) => {
      setError(error.data?.errorMsg);
      Message.error(error.data?.errorMsg ?? commonLocale['page.common.tips.error']);
    },
  });

  const form = useMemo(() => {
    return createForm({
      effects: () => {
        autoFormTrack();
        onFormMount(async form => {
          const { isSuccess, result = {} as SpecialtyInfo } = await querySpecialtyInfo();
          if (isSuccess) {
            if (result) {
              form.setValues({
                ...pick(result, [
                  'factoryTagStatus',
                  'factoryLocation',
                  'factoryAddressDetail',
                  'factoryAddress',
                  'factoryTagQCStatus',
                ]),
                factoryFileName:
                  result.factoryFileLink && result.factoryFileName
                    ? [{ url: result.factoryFileLink, filename: result.factoryFileName }]
                    : [],
              });
              if (result.factoryTagQCStatus === EnumSellerStatus.YES) form.disabled = true;
            }
          } else {
            Message.error('Information acquisition failed, please try again');
          }
        });
        onFieldMount('factoryTagStatus', (field: IField, form) => {
          form.query('*(factoryFileName, factoryLocation, factoryAddress, factoryAddressDetail)').forEach(f => {
            f.visible = field.value === EnumSellerStatus.YES;
          });
        });
        onFieldValueChange('factoryTagStatus', (field, form) => {
          form.query('*(factoryFileName, factoryLocation, factoryAddress, factoryAddressDetail)').forEach(f => {
            f.visible = field.value === EnumSellerStatus.YES;
          });
        });
      },
    });
  }, []);

  return (
    <ExperienceContainer id="SUPPLY_CHAIN" benefitList={benefitList}>
      <Form form={form} layout="vertical">
        <Box direction="column" spacing={16} className="supply-chain-box">
          <FormConsumer>
            {currentForm => <QualityCheckMessage status={currentForm.values.factoryTagQCStatus} />}
          </FormConsumer>
          <Banner img={banner} />
          <Card className="supply-chain-form">
            <Typography.H4>{locale.FactoryTitle}</Typography.H4>
            <Field
              name="factoryTagStatus"
              title={locale.FactoryTagStatusText}
              required
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={SELLER_STATUS_LIST}
            />
            <Field
              name="factoryFileName"
              title={locale.FactoryUploadText}
              required
              decorator={[FormItem, { style: { width: '60%' } }]}
              visible={false}
              component={[UploadDocumentFormily]}
            />
            <Field
              name="factoryLocation"
              title={locale.FactoryLocationText}
              required
              decorator={[FormItem]}
              visible={false}
              component={[Radio.Group]}
              dataSource={LOCATION_LIST}
            />
            <Field
              name="factoryAddressDetail"
              title={locale.FactoryAddressDetailText}
              required
              decorator={[FormItem, { style: { width: '60%' } }]}
              visible={false}
              component={[Input]}
            />
            <Field
              name="factoryAddress"
              title={locale.FactoryAddressText}
              required
              decorator={[FormItem, { style: { width: '60%' } }]}
              visible={false}
              component={[AddressSelector]}
            />
          </Card>
          <SubmitAffix>
            <FormConsumer>
              {currentForm =>
                currentForm.values.factoryTagQCStatus !== EnumSellerStatus.YES ? (
                  <Checkbox onChange={checked => setState({ agree: checked })}>
                    <span
                      style={{ fontSize: '14px' }}
                      dangerouslySetInnerHTML={{ __html: locale.IAgreeTermCondition }}
                    />
                  </Checkbox>
                ) : (
                  <div></div>
                )
              }
            </FormConsumer>
            <FormConsumer>
              {currentForm => (
                <Box direction="row" spacing={20}>
                  {currentForm.values.factoryTagQCStatus === EnumSellerStatus.YES && (
                    <Button
                      type="secondary"
                      onClick={() => {
                        form.disabled = false;
                      }}
                      disabled={!currentForm.disabled}
                    >
                      {commonLocale['page.common.btn.edit']}
                    </Button>
                  )}
                  <Submit
                    data-spm={`d_click_submit_SUPPLY_CHAIN`}
                    className="aplus-auto-exp"
                    loading={loading}
                    disabled={
                      (currentForm.values.factoryTagQCStatus === EnumSellerStatus.YES && currentForm.disabled) ||
                      (!state.agree && currentForm.values.factoryTagQCStatus !== EnumSellerStatus.YES)
                    }
                    onSubmit={values => {
                      run({ ...values, factoryFileName: values?.factoryFileName?.[0]?.filename });
                    }}
                  >
                    {commonLocale['page.common.btn.submit']}
                  </Submit>
                </Box>
              )}
            </FormConsumer>
          </SubmitAffix>
        </Box>
      </Form>
    </ExperienceContainer>
  );
};

export default SupplyChain;
