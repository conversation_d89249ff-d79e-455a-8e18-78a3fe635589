import { <PERSON>, <PERSON><PERSON>, Card, Checkbox, Message, Typography } from '@alifd/next';
import { AddressSelector, SubmitAffix } from '@alife/asc-components';
import withTrackEffects from '@alife/form-track-plugin';
import { createForm, onFieldMount, onFieldValueChange, onFormMount, Field as IField } from '@formily/core';
import { Form, Input, FormItem, Submit, Radio } from '@formily/next';
import { Field, FormConsumer } from '@formily/react';
import React, { useMemo } from 'react';
import ExperienceContainer from '../components/page-container';
import { EnumSellerStatus, LOCATION_LIST, OSC_CONFIG_MAP, SELLER_STATUS_LIST } from '../constants';
import * as locale from '../locale';
import { querySpecialtyInfo, submitOfflineStoreInfo } from '../service';
import commonLocale from '../../../constants/common-locale';
import { useRequest, useSetState } from 'ahooks';
import UploadDocumentFormily from '../components/upload-document';
import pick from 'lodash/pick';
import QualityCheckMessage from '../components/msg';
import Banner from '../components/banner';
import { useOsc } from '@alife/lago-utils';
import '../page.scss';

const OfflineStore: React.FC = () => {
  const [state, setState] = useSetState<{ agree: boolean }>({ agree: false });

  const { data } = useOsc<{ list: any }>('38');
  const config = OSC_CONFIG_MAP['SOCIAL_MEDIA'];
  const banner = data?.[config.banner];
  const benefitList = data?.[config.benefit];

  const { autoFormTrack, setError } = withTrackEffects({
    formKey: `business-experience-OFFLINE_STORE`,
  });

  const { loading, run } = useRequest(submitOfflineStoreInfo, {
    manual: true,
    onSuccess: data => {
      Message.success(commonLocale['page.common.tips.success']);
      // 当前页刷新
      location.href = `/apps/setting/experience?formCode=OFFLINE_STORE`;
    },
    onError: (error: any) => {
      setError(error.data?.errorMsg);
      Message.error(error.data?.errorMsg ?? commonLocale['page.common.tips.error']);
    },
  });

  const form = useMemo(() => {
    return createForm({
      effects: () => {
        autoFormTrack();
        onFormMount(async form => {
          const { isSuccess, result } = await querySpecialtyInfo();
          if (isSuccess) {
            if (result) {
              form.setValues({
                ...pick(result, [
                  'offlineStoreTagStatus',
                  'offlineStoreLocation',
                  'offlineStoreAddressDetail',
                  'offlineStoreAddress',
                  'offlineStoreTagQCStatus',
                ]),
                offlineStoreFileName:
                  result.offlineStoreFileLink && result.offlineStoreFileName
                    ? [{ url: result.offlineStoreFileLink, filename: result.offlineStoreFileName }]
                    : [],
              });
              if (result.offlineStoreTagQCStatus === EnumSellerStatus.YES) form.disabled = true;
            }
          } else {
            Message.error('Information acquisition failed, please try again');
          }
        });
        onFieldMount('offlineStoreTagStatus', (field: IField, form) => {
          form
            .query('*(offlineStoreFileName, offlineStoreLocation, offlineStoreAddress, offlineStoreAddressDetail)')
            .forEach(f => {
              f.visible = field.value === EnumSellerStatus.YES;
            });
        });
        onFieldValueChange('offlineStoreTagStatus', (field, form) => {
          form
            .query('*(offlineStoreFileName, offlineStoreLocation, offlineStoreAddress, offlineStoreAddressDetail)')
            .forEach(f => {
              f.visible = field.value === EnumSellerStatus.YES;
            });
        });
      },
    });
  }, []);

  return (
    <ExperienceContainer id="OFFLINE_STORE" benefitList={benefitList}>
      <Form form={form} layout="vertical">
        <Box direction="column" spacing={16} className="offline-store-box">
          <FormConsumer>
            {currentForm => <QualityCheckMessage status={currentForm.values.offlineStoreTagQCStatus} />}
          </FormConsumer>
          <Banner img={banner} />
          <Card className="offline-store-form">
            <Typography.H4>{locale.OfflineStoreTitle}</Typography.H4>
            <Field
              name="offlineStoreTagStatus"
              title={locale.OfflineStoreTagStatusText}
              required
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={SELLER_STATUS_LIST}
            />
            <Field
              name="offlineStoreFileName"
              title={locale.OfflineStoreUploadText}
              required
              visible={false}
              decorator={[FormItem, { style: { width: '60%' } }]}
              component={[UploadDocumentFormily]}
            />
            <Field
              name="offlineStoreLocation"
              title={locale.OfflineStoreLocationText}
              required
              visible={false}
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={LOCATION_LIST}
            />
            <Field
              name="offlineStoreAddressDetail"
              title={locale.OfflineStoreAddressDetailText}
              required
              visible={false}
              decorator={[FormItem, { style: { width: '60%' } }]}
              component={[Input]}
            />
            <Field
              name="offlineStoreAddress"
              title={locale.OfflineStoreAddressText}
              required
              visible={false}
              decorator={[FormItem, { style: { width: '60%' } }]}
              component={[AddressSelector]}
            />
          </Card>
          <SubmitAffix>
            <FormConsumer>
              {currentForm =>
                currentForm.values.offlineStoreTagQCStatus !== EnumSellerStatus.YES ? (
                  <Checkbox onChange={checked => setState({ agree: checked })}>
                    <span
                      style={{ fontSize: '14px' }}
                      dangerouslySetInnerHTML={{ __html: locale.IAgreeTermCondition }}
                    />
                  </Checkbox>
                ) : (
                  <div></div>
                )
              }
            </FormConsumer>
            <FormConsumer>
              {currentForm => (
                <Box direction="row" spacing={20}>
                  {currentForm.values.offlineStoreTagQCStatus === EnumSellerStatus.YES && (
                    <Button
                      type="secondary"
                      onClick={() => {
                        currentForm.disabled = false;
                      }}
                      disabled={!currentForm.disabled}
                    >
                      {commonLocale['page.common.btn.edit']}
                    </Button>
                  )}
                  <Submit
                    data-spm={`d_click_submit_OFFLINE_STORE`}
                    className="aplus-auto-exp"
                    loading={loading}
                    disabled={
                      (!state.agree && currentForm.values.offlineStoreTagQCStatus !== EnumSellerStatus.YES) ||
                      (currentForm.values.offlineStoreTagQCStatus === EnumSellerStatus.YES && currentForm.disabled)
                    }
                    onSubmit={values => {
                      run({ ...values, offlineStoreFileName: values?.offlineStoreFileName?.[0]?.filename });
                    }}
                  >
                    {commonLocale['page.common.btn.submit']}
                  </Submit>
                </Box>
              )}
            </FormConsumer>
          </SubmitAffix>
        </Box>
      </Form>
    </ExperienceContainer>
  );
};

export default OfflineStore;
