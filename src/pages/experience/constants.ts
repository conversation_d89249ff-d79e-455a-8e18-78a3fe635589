import { FromCodeType } from './page';
import * as locale from './locale';

export const SOCIAL_MEDIA_LIST = [
  {
    value: 'Facebook',
    label: 'Facebook',
    icon: 'https://img.alicdn.com/imgextra/i3/O1CN01T15Zty22mFS9gy08O_!!6000000007162-55-tps-12-12.svg',
  },
  {
    value: 'Instagram',
    label: 'Instagram',
    icon: 'https://img.alicdn.com/imgextra/i3/O1CN01T15Zty22mFS9gy08O_!!6000000007162-55-tps-12-12.svg',
  },
  {
    value: 'Tiktok',
    label: 'Tiktok',
    icon: 'https://img.alicdn.com/imgextra/i3/O1CN01T15Zty22mFS9gy08O_!!6000000007162-55-tps-12-12.svg',
  },
  {
    value: 'Line',
    label: 'Line',
    icon: 'https://img.alicdn.com/imgextra/i3/O1CN01T15Zty22mFS9gy08O_!!6000000007162-55-tps-12-12.svg',
  },
];

export enum EnumSellerStatus {
  YES = 1,
  NO = 0,
  PENDING = -1,
}

export const SELLER_STATUS_LIST = [
  { value: EnumSellerStatus.YES, label: locale.TagYes },
  { value: EnumSellerStatus.NO, label: locale.TagNo },
];

export const LOCATION_LIST = [
  { value: 'Local', label: locale.TagLocationLocal },
  { value: 'Oversea', label: locale.TagLocationOversea },
];

export const OSC_CONFIG_MAP: Record<FromCodeType, { banner: string; benefit: string; status?: string; tag: string }> = {
  SOCIAL_MEDIA: {
    banner: 'socialMediaImg',
    benefit: 'socialMediaTips',
    status: 'socialTagQCStatus',
    tag: 'socialTagStatus',
  },
  ONLINE_STORE: {
    banner: '',
    benefit: '',
    status: '',
    tag: '',
  },
  OFFLINE_STORE: {
    banner: 'offlineStoreImg',
    benefit: 'offlineStoreTips',
    status: 'offlineStoreTagQCStatus',
    tag: 'offlineStoreTagStatus',
  },
  SUPPLY_CHAIN: {
    banner: 'supplyChainImg',
    benefit: 'supplyChainTips',
    status: 'factoryTagQCStatus',
    tag: 'factoryTagStatus',
  },
  STORE_MANAGEMENT: {
    banner: '',
    benefit: '',
    status: '',
    tag: '',
  },
  BRAND_INFO: {
    banner: '',
    benefit: '',
    status: '',
    tag: '',
  },
  TIKTOK_SHOP: {
    banner: 'ttsImg',
    benefit: 'ttsTips',
    status: 'ttsTagQCStatus',
    tag: 'ttsTagStatus',
  },
};
