import { <PERSON>, Button, Card, Checkbox, Message, Typography } from '@alifd/next';
import { SubmitAffix } from '@alife/asc-components';
import withTrackEffects from '@alife/form-track-plugin';
import { createForm, onFieldMount, onFieldValueChange, onFormMount, Field as IField } from '@formily/core';
import { Form, FormItem, Submit, Radio } from '@formily/next';
import { Field, FormConsumer } from '@formily/react';
import React, { useMemo } from 'react';
import ExperienceContainer from '../components/page-container';
import { EnumSellerStatus, OSC_CONFIG_MAP, SELLER_STATUS_LIST } from '../constants';
import * as locale from '../locale';
import { querySpecialtyInfo, submitTikTokShopInfo } from '../service';
import commonLocale from '../../../constants/common-locale';
import { useRequest, useSetState } from 'ahooks';
import UploadDocumentFormily from '../components/upload-document';
import pick from 'lodash/pick';
import QualityCheckMessage from '../components/msg';
import Banner from '../components/banner';
import { useOsc } from '@alife/lago-utils';
import '../page.scss';

const TikTokShop: React.FC = () => {
  const [state, setState] = useSetState<{ agree: boolean }>({ agree: false });

  const { data } = useOsc<{ list: any }>('38');
  const config = OSC_CONFIG_MAP['TIKTOK_SHOP'];
  const banner = data?.[config.banner];
  const benefitList = data?.[config.benefit];

  const { autoFormTrack, setError } = withTrackEffects({
    formKey: `business-experience-TIKTOK_SHOP`,
  });

  const { loading, run } = useRequest(submitTikTokShopInfo, {
    manual: true,
    onSuccess: data => {
      Message.success(commonLocale['page.common.tips.success']);
      // 当前页刷新
      location.href = `/apps/setting/experience?formCode=TIKTOK_SHOP`;
    },
    onError: (error: any) => {
      setError(error.data?.errorMsg);
      Message.error(error.data?.errorMsg ?? commonLocale['page.common.tips.error']);
    },
  });

  const form = useMemo(() => {
    return createForm({
      effects: () => {
        autoFormTrack();
        onFormMount(async form => {
          const { isSuccess, result } = await querySpecialtyInfo();
          if (isSuccess) {
            if (result) {
              form.setValues({
                ...pick(result, ['ttsTagStatus']),
                ttsFileName:
                  result.ttsFileLink && result.ttsFileName
                    ? [{ url: result.ttsFileLink, filename: result.ttsFileName }]
                    : [],
              });
              if (result.ttsTagQCStatus === EnumSellerStatus.YES) form.disabled = true;
            }
          } else {
            Message.error('Information acquisition failed, please try again');
          }
        });
        onFieldMount('ttsTagStatus', (field: IField, form) => {
          form.setFieldState('ttsFileName', f => {
            f.visible = field.value === EnumSellerStatus.YES;
          });
        });
        onFieldValueChange('ttsTagStatus', (field, form) => {
          form.setFieldState('ttsFileName', f => {
            f.visible = field.value === EnumSellerStatus.YES;
          });
        });
      },
    });
  }, []);

  return (
    <ExperienceContainer id="TIKTOK_SHOP" benefitList={benefitList}>
      <Form form={form} layout="vertical">
        <Box direction="column" spacing={16} className="tt-shop-box">
          <FormConsumer>
            {currentForm => <QualityCheckMessage status={currentForm.values.ttsTagQCStatus} />}
          </FormConsumer>
          <Banner img={banner} />
          <Card className="tt-shop-form">
            <Typography.H4>{locale.TikTokShopTitle}</Typography.H4>
            <Field
              name="ttsTagStatus"
              title={locale.TikTokShopStatusText}
              required
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={SELLER_STATUS_LIST}
            />
            <Field
              name="ttsFileName"
              title={locale.TikTokShopUploadText}
              required
              visible={false}
              decorator={[FormItem, { style: { width: '60%' } }]}
              component={[UploadDocumentFormily]}
            />
          </Card>
          <SubmitAffix>
            <FormConsumer>
              {currentForm =>
                currentForm.values.ttsTagQCStatus !== EnumSellerStatus.YES ? (
                  <Checkbox onChange={checked => setState({ agree: checked })}>
                    <span
                      style={{ fontSize: '14px' }}
                      dangerouslySetInnerHTML={{ __html: locale.IAgreeTermCondition }}
                    />
                  </Checkbox>
                ) : (
                  <div></div>
                )
              }
            </FormConsumer>
            <FormConsumer>
              {currentForm => (
                <Box direction="row" spacing={20}>
                  {currentForm.values.ttsTagQCStatus === EnumSellerStatus.YES && (
                    <Button
                      type="secondary"
                      onClick={() => {
                        currentForm.disabled = false;
                      }}
                      disabled={!currentForm.disabled}
                    >
                      {commonLocale['page.common.btn.edit']}
                    </Button>
                  )}
                  <Submit
                    data-spm={`d_click_submit_TTS`}
                    className="aplus-auto-exp"
                    loading={loading}
                    disabled={
                      (!state.agree && currentForm.values.ttsTagQCStatus === EnumSellerStatus.YES) ||
                      (currentForm.values.ttsTagQCStatus === EnumSellerStatus.YES && currentForm.disabled)
                    }
                    onSubmit={values => {
                      run({ ...values, ttsFileName: values?.ttsFileName?.[0]?.filename });
                    }}
                  >
                    {commonLocale['page.common.btn.submit']}
                  </Submit>
                </Box>
              )}
            </FormConsumer>
          </SubmitAffix>
        </Box>
      </Form>
    </ExperienceContainer>
  );
};

export default TikTokShop;
