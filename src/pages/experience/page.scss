.list {
  display: flex;
  flex-wrap: wrap;
  margin-right: -16px;
  .button {
    display: flex;
    height: 96px;
    line-height: 96px;
    width: 347px;
    background-color: #f8f9fd;
    border-radius: 12px;
    box-sizing: border-box;
    padding: 16px 8px;
    margin-bottom: 16px;
    margin-right: 16px;
    cursor: pointer;
    &:hover {
      box-shadow: 2px 4px 10px 0 rgb(0 0 0 / 12%);
      background-color: #eeeff0;
    }
    .add-icon {
      position: relative;
      display: block;
      height: 60px;
      width: 60px;
      margin-right: 13px;
      background-color: rgba(228, 238, 251, 1);
      background-size: 70% 70%;
      background-position: center;
      background-repeat: no-repeat;
      border-radius: 4px;
      &.ONLINE_STORE {
        background-image: url('https://img.alicdn.com/imgextra/i3/O1CN01oFtbAj1Tj23JdsfH9_!!6000000002417-2-tps-180-180.png');
      }
      &.OFFLINE_STORE {
        background-image: url('https://img.alicdn.com/imgextra/i3/O1CN011eyeJ91S86V6kur4c_!!6000000002201-2-tps-180-180.png');
      }
      &.SUPPLY_CHAIN {
        background-image: url('https://img.alicdn.com/imgextra/i4/O1CN01BZRlSH26ATAxoh3EX_!!6000000007621-2-tps-180-180.png');
      }
      &.SOCIAL_MEDIA {
        background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01CDZsjQ1DwHewXMm0N_!!6000000000280-2-tps-180-180.png');
      }
      &.BRAND_INFO {
        background-image: url('https://img.alicdn.com/imgextra/i2/O1CN01SM47jl1Rnx4YJuqys_!!6000000002157-2-tps-180-180.png');
      }
      &.STORE_MANAGEMENT {
        background-image: url('https://img.alicdn.com/imgextra/i3/O1CN018YlUT11bo5LTyJGEB_!!6000000003511-2-tps-204-174.png');
      }
      &.TIKTOK_SHOP {
        background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01nVmoEl1nEcC4VP4r2_!!6000000005058-2-tps-180-180.png');
      }
      .complete {
        position: absolute;
        display: block;
        width: 16px;
        height: 16px;
        background-image: url('https://img.alicdn.com/imgextra/i1/O1CN01mMizYQ1oaQXMGYsj1_!!6000000005241-55-tps-16-16.svg');
        background-size: 16px 16px;
        background-repeat: no-repeat;
        right: -4px;
        top: -4px;
      }
    }
    .config-item-desc {
      color: #858b9c;
      font-size: 12px;
      height: 40px;
      line-height: 20px;
      overflow: hidden;
    }
    .text {
      display: block;
      font-family: PingFangSC;
      font-weight: 500;
      font-size: 14px;
      color: #4f5669;
    }
  }
}

.social-media-box {
  .social-media-sub-title {
    display: inline-block;
    margin-bottom: 10px;
  }

  .submit-affix-container {
    .next-card-content {
      justify-content: space-between;
    }
  }
}

.offline-store-box {
  .submit-affix-container {
    .next-card-content {
      justify-content: space-between;
    }
  }
}

.supply-chain-box {
  .submit-affix-container {
    .next-card-content {
      justify-content: space-between;
    }
  }
}

.tt-shop-box {
  .submit-affix-container {
    .next-card-content {
      justify-content: space-between;
    }
  }
}

.next-upload-drag {
  padding: 0 100px;
}

.review {
  .lzd-result-scope {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 24px;
    min-height: 300px;
  }

  .review-box {
    background: #f9f9f9;
    padding: 24px 44px 44px 44px;
    margin: 40px;
    border-radius: 16px;
  }

  .review-box-item {
    width: 350px;
    margin-top: 20px;

    .help-tip {
      .next-icon {
        margin-top: 8px;
      }
    }

    .review-box-title {
      margin: 20px 0 10px 0;
      font-size: 16px;
      font-weight: 600;
      text-align: center;
    }

    .review-box-text {
      text-align: center;
    }
  }
}

.experience-banner {
  .experience-banner-box {
    width: 100%;
    background-repeat: no-repeat;
    background-size: 100% auto;
    padding-top: 13%;
    height: 0px;

    &:before {
      content: '';
      display: block;
      padding-top: 100%;
    }
  }
}
