import { axios } from '@alife/workstation-utils';
import { EnumSellerStatus } from './constants';

export const getStatus = () =>
  axios({
    url: 'mtop.global.merchant.subaccount.seller.task.status',
    params: {
      onlyIncludeBusinessExperience: true,
    },
  }).then(({ data }) => data?.data);

export const getExperienceForm = data =>
  axios({
    url: 'mtop.lazada.merchant.business.experience.readform',
    params: data,
  }).then(({ data }) => data);
export const saveExperienceForm = data =>
  axios({
    url: 'mtop.lazada.merchant.business.experience.writeform',
    params: data,
    method: 'post',
  }).then(({ data }) => data);

export interface SocialMediaTypeItem {
  name: string;
  link: string;
  followers: number;
}

export interface SpecialtyInfo {
  sellerId: string;
  socialTagStatus: EnumSellerStatus;
  socialTagQCStatus: EnumSellerStatus;
  socialInfo: SocialMediaTypeItem[];
  socialInfoAllowEdit: boolean;
  factoryTagStatus: EnumSellerStatus;
  factoryTagQCStatus: EnumSellerStatus;
  factoryFileName: string;
  factoryFileLink: string;
  factoryLocation: string;
  factoryAddress: string;
  factoryAddressDetail: string;
  offlineStoreTagStatus: EnumSellerStatus;
  offlineStoreTagQCStatus: EnumSellerStatus;
  offlineStoreFileName: string;
  offlineStoreFileLink: string;
  offlineStoreLocation: string;
  offlineStoreAddress: string;
  offlineStoreAddressDetail: string;
  ttsTagStatus: EnumSellerStatus;
  ttsTagQCStatus: EnumSellerStatus;
  ttsTagQCResult: string;
  ttsFileName: string;
  ttsFileLink: string;
  ttsTagQCReason: string;
}

export const querySpecialtyInfo = async (): Promise<{ result: SpecialtyInfo; isSuccess: boolean }> => {
  return axios({
    url: 'mtop.lazada.sdc.asc.specialty.info.query',
  }).then(({ data }) => data);
};

export const submitSocialMediaInfo = async (data: { socialInfo: string }) => {
  return axios({
    url: 'mtop.lazada.sdc.asc.social.media.info.submit',
    data,
  }).then(({ data }) => data);
};

export const submitFactoryInfo = async (data: {
  tagStatus: string;
  fileName: string;
  location: string;
  addressDetail: string;
  address: string;
}) => {
  return axios({
    url: 'mtop.lazada.sdc.asc.factory.info.submit',
    data,
  }).then(({ data }) => data);
};

export const submitOfflineStoreInfo = async (data: {
  tagStatus: string;
  fileName: string;
  location: string;
  addressDetail: string;
  address: string;
}) => {
  return axios({
    url: 'mtop.lazada.sdc.asc.offline.store.info.submit',
    data,
  }).then(({ data }) => data);
};

export const submitTikTokShopInfo = async (data: { tagStatus: string; fileName: string }) => {
  return axios({
    url: 'mtop.lazada.sdc.asc.tts.info.submit',
    data,
  }).then(({ data }) => data);
};
