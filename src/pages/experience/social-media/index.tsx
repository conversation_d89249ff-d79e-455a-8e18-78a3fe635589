import { <PERSON>, <PERSON>ton, Card, Checkbox, Message, Typography } from '@alifd/next';
import { SubmitAffix } from '@alife/asc-components';
import withTrackEffects from '@alife/form-track-plugin';
import { createForm, isField, onFieldMount, onFieldValueChange, onFormMount } from '@formily/core';
import { Form, FormItem, Radio, Submit } from '@formily/next';
import { Field, FormConsumer, ObjectField } from '@formily/react';
import React, { useMemo } from 'react';
import ExperienceContainer from '../components/page-container';
import { EnumSellerStatus, OSC_CONFIG_MAP, SELLER_STATUS_LIST, SOCIAL_MEDIA_LIST } from '../constants';
import * as locale from '../locale';
import { querySpecialtyInfo, submitSocialMediaInfo, SocialMediaTypeItem } from '../service';
import commonLocale from '../../../constants/common-locale';
import { useRequest, useSetState } from 'ahooks';
import { useOsc } from '@alife/lago-utils';
import Banner from '../components/banner';
import SocialMediaItem from '../components/social-media';
import QualityCheckMessage from '../components/msg';
import isEmpty from 'lodash/isEmpty';
import '../page.scss';

const SocialMedia: React.FC = () => {
  const [state, setState] = useSetState<{ agree: boolean }>({ agree: false });

  const { data } = useOsc<{ list: any }>('38');
  const config = OSC_CONFIG_MAP['SOCIAL_MEDIA'];
  const banner = data?.[config.banner];
  const benefitList = data?.[config.benefit];

  const { autoFormTrack, setError } = withTrackEffects({
    formKey: `business-experience-SOCIAL_MEDIA`,
  });

  const { loading, run } = useRequest(submitSocialMediaInfo, {
    manual: true,
    onSuccess: data => {
      Message.success(locale.SubmitSuccessful);
      // 当前页刷新
      location.href = `/apps/setting/experience?formCode=SOCIAL_MEDIA`;
    },
    onError: (error: any) => {
      setError(error.data?.errorMsg);
      Message.error(error.data?.errorMsg ?? commonLocale['page.common.tips.error']);
    },
  });

  const form = useMemo(() => {
    return createForm({
      effects: () => {
        autoFormTrack();
        onFormMount(async form => {
          const { isSuccess, result } = await querySpecialtyInfo();
          if (isSuccess) {
            if (result) {
              if (result.socialTagQCStatus === EnumSellerStatus.YES) {
                form.disabled = true;
                setState({ agree: true });
              }
              form.setValues({
                socialTagStatus: result.socialTagStatus,
                socialInfo: (result.socialInfo || []).reduce((r, p) => {
                  const idx = SOCIAL_MEDIA_LIST.findIndex(x => x.value === p?.name);
                  if (idx || idx == 0) {
                    return { ...r, [idx]: p };
                  } else {
                    return r;
                  }
                }, {}),
                socialTagQCStatus: result.socialTagQCStatus,
              });
            }
          } else {
            Message.error('Information acquisition failed, please try again');
          }
        });

        onFieldValueChange('socialTagStatus', (field, form) => {
          form.setFieldState('socialInfo', state => {
            state.visible = field.value === EnumSellerStatus.YES;
          });
        });

        onFieldMount('socialTagStatus', (field, form) => {
          if (!isField(field)) return;
          form.setFieldState('socialInfo', state => {
            state.visible = field.value === EnumSellerStatus.YES;
          });
        });
      },
    });
  }, []);

  return (
    <ExperienceContainer id="SOCIAL_MEDIA" benefitList={benefitList}>
      <Form form={form} layout="vertical">
        <Box direction="column" spacing={16} className="social-media-box">
          <FormConsumer>
            {currentForm => <QualityCheckMessage status={currentForm.values.socialTagQCStatus} />}
          </FormConsumer>
          <Banner img={banner} />
          <Card>
            <Typography.H4>{locale.SocialMediaTitle}</Typography.H4>
            <Field
              name="socialTagStatus"
              title={locale.SocialMediaTagStatusText}
              required
              decorator={[FormItem]}
              component={[Radio.Group]}
              dataSource={SELLER_STATUS_LIST}
            />
            <ObjectField
              name="socialInfo"
              visible={false}
              required
              component={[SocialMediaItem]}
              decorator={[FormItem]}
              validator={(value: Record<string, SocialMediaTypeItem>) => {
                const isComplete = Object.entries(value).some(
                  ([, v]) => 'name' in v && 'link' in v && 'followers' in v,
                );
                if (isComplete) {
                  return;
                } else {
                  return locale.SocialMediaInfoRequired;
                }
              }}
            />
          </Card>
          <SubmitAffix>
            <FormConsumer>
              {currentForm =>
                currentForm.values.socialTagQCStatus !== EnumSellerStatus.YES ? (
                  <Checkbox onChange={checked => setState({ agree: checked })}>
                    <span
                      style={{ fontSize: '14px' }}
                      dangerouslySetInnerHTML={{ __html: locale.IAgreeTermCondition }}
                    />
                  </Checkbox>
                ) : (
                  <div></div>
                )
              }
            </FormConsumer>
            <FormConsumer>
              {currentForm => (
                <Box direction="row" spacing={20}>
                  {form.values.socialTagQCStatus === EnumSellerStatus.YES && (
                    <Button
                      type="secondary"
                      onClick={() => {
                        form.disabled = false;
                      }}
                      disabled={!currentForm.disabled}
                    >
                      {commonLocale['page.common.btn.edit']}
                    </Button>
                  )}
                  <Submit
                    data-spm={`d_click_submit_SOCIAL_MEDIA`}
                    className="aplus-auto-exp"
                    loading={loading}
                    disabled={
                      (form.values.socialTagQCStatus !== EnumSellerStatus.YES && !state.agree) ||
                      (form.values.socialTagQCStatus === EnumSellerStatus.YES && currentForm.disabled)
                    }
                    onSubmit={values => {
                      const socialInfo = Object.entries<SocialMediaTypeItem>(values.socialInfo)
                        .filter(([, item]) => (isEmpty(item.name) ? false : true))
                        .map(([, x]) => ({ ...x, name: Array.isArray(x?.name) ? x?.name?.[0] : x?.name }));
                      run({ socialInfo: JSON.stringify(socialInfo) });
                    }}
                  >
                    {commonLocale['page.common.btn.submit']}
                  </Submit>
                </Box>
              )}
            </FormConsumer>
          </SubmitAffix>
        </Box>
      </Form>
    </ExperienceContainer>
  );
};

export default SocialMedia;
