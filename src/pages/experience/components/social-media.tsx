import { <PERSON><PERSON>, Box, Grid, Input, Typography } from '@alifd/next';
import { ArrayField, Field as FieldType, onFieldValueChange } from '@formily/core';
import { Checkbox, FormItem } from '@formily/next';
import { mapStatus } from '@formily/next/esm/__builtins__';
import { connect, Field, mapProps, ObjectField, observer, useField, useForm, useFormEffects } from '@formily/react';
import isEmpty from 'lodash/isEmpty';
import difference from 'lodash/difference';
import React from 'react';
import { SOCIAL_MEDIA_LIST } from '../constants';
import * as locale from '../locale';
import { useUpdateEffect } from 'ahooks';
import { SocialMediaTypeItem } from '../service';

const { Row, Col } = Grid;

const ALL_INDEX = SOCIAL_MEDIA_LIST.map((x, i) => i);

const SocialMediaItem = observer<{ max?: number }>(({ max = 3 }) => {
  const { path, disabled, value } = useField<ArrayField>();
  const form = useForm();

  // 初始化的禁用兼容
  useUpdateEffect(() => {
    if (!disabled && value.length >= max) {
      form.query('socialInfo.*.name').forEach((field: FieldType) => {
        const f = form.query(field.path.parent().toString()).take() as FieldType;
        if (!field.value) {
          f.disabled = true;
        } else {
          f.disabled = false;
        }
      });
    }
  }, [disabled]);

  useFormEffects(() => {
    onFieldValueChange(`${path.toString()}.*.name`, (field, form) => {
      const effect = Object.entries<SocialMediaTypeItem>(form.values.socialInfo).filter(
        ([, item]) => !isEmpty(item.name),
      );
      const effectIndex = effect.reduce<number[]>((result, [key, item]) => {
        const i = SOCIAL_MEDIA_LIST.findIndex(x => x.value === item.name?.[0] || x.value === item.name);
        result.push(i);
        return result;
      }, []);

      // 当前选中的行
      const index = field.path.segments[1] as number;
      // 取消勾选
      if (isEmpty(field.value)) {
        const diffIndex = difference(ALL_INDEX, [...effectIndex, index]);
        form.query(`${path.toString()}.${index}.*(link, followers)`).forEach((f: FieldType) => {
          f.required = false;
        });
        if (effectIndex.length < max) {
          diffIndex.forEach(i => {
            form.query(`${path.toString()}.${i}.*`).forEach((f: FieldType) => {
              f.disabled = false;
              if (f.path.segments[2] === 'name')
                f.dataSource = f.dataSource.map(x => ({
                  ...x,
                  label: SOCIAL_MEDIA_LIST.find(y => x.value === y.value)?.label,
                }));
            });
          });
        }
      } else {
        const diffIndex = difference(ALL_INDEX, effectIndex);
        form.query(`${path.toString()}.${index}.*(link, followers)`).forEach((f: FieldType) => {
          f.required = true;
        });
        if (effectIndex.length >= max) {
          diffIndex.forEach(i => {
            form.query(`${path.toString()}.${i}.*`).forEach((f: FieldType) => {
              f.disabled = true;
              if (f.path.segments[2] === 'name')
                f.dataSource = f.dataSource.map(x => ({
                  ...x,
                  label: (
                    <Balloon v2 trigger={x.label} triggerType="hover" closable={false} align="r">
                      {locale.MaxPlatformsSelected}
                    </Balloon>
                  ),
                }));
            });
          });
        }
      }
    });
  });

  return (
    <Box>
      <Typography.Text className="social-media-sub-title">{locale.SocialMediaSubTitle}</Typography.Text>
      {SOCIAL_MEDIA_LIST.map((item, index) => {
        return (
          <ObjectField name={index} decorator={[FormItem]}>
            <Row gutter={20}>
              <Col span={3}>
                <Field
                  name="name"
                  component={[Checkbox.Group]}
                  dataSource={[{ label: item.label, value: item.value }]}
                  decorator={[FormItem]}
                />
              </Col>
              <Col span={13}>
                <Field name="link" component={[Input, { addonTextBefore: 'Link' }]} decorator={[FormItem]} />
              </Col>
              <Col span={8}>
                <Field
                  name="followers"
                  component={[Input, { addonTextBefore: locale.NumberFollowersText }]}
                  decorator={[FormItem]}
                  validator={{ format: 'number' }}
                />
              </Col>
            </Row>
          </ObjectField>
        );
      })}
    </Box>
  );
});

export default connect(SocialMediaItem, mapProps(mapStatus));
