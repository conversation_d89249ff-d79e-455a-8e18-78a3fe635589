import { Card } from '@alifd/next';
import React, { CSSProperties } from 'react';
import { EnumSellerStatus } from '../constants';
import * as locale from '../locale';

const QualityCheckMessage: React.FC<{ status: EnumSellerStatus; style?: CSSProperties }> = ({ status, style }) => {
  if (status === EnumSellerStatus.YES) {
    return <Card style={style}>{locale.QualityCheckSuccessText}</Card>;
  }

  return null;
};

export default QualityCheckMessage;
