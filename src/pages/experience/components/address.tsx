import { AddressSelector, useValueConvert } from '@alife/asc-components';
import { ASCTools, axios, request } from '@alife/workstation-utils';
import { connect, mapProps } from '@formily/react';
import { last } from 'lodash';
import React from 'react';

const AddressFormily = connect(
  props => {
    const [value, onChange] = useValueConvert<string[], string>(props, {
      // @ts-ignore
      convert(val) {
        return last(val);
      },
      reverse(v, item, extra) {
        let newValue: string[] = [];
        if (extra.selectedPath?.length) {
          newValue = extra.selectedPath.map(sitem => sitem.value);
        }
        if (props.iso === 'SG' && newValue.length && newValue[0] !== 'R80020282') {
          // SG first level fix it as R80020282
          newValue.unshift('R80020282');
        }
        return newValue;
      },
    });
    // const value = props.value?.length ? props.value[props.value.length - 1] : null;
    const newProps = { ...props, value, onChange };

    // if (props.iso === 'SG') {
    //   // show the postcode for SG only
    //   newProps.maxLevel = 6;
    // }
    const loadPathDataService = props => {
      const { level, id } = props;
      return axios({
        url: 'mtop.lazada.merchant.onboard.task.address.list',
        method: 'POST',
        data: {
          queryLevel: level,
          locationId: id,
        },
      }).then(res =>
        res.data.data.map(({ value: id, label, isLeaf, parentId }) => ({
          id: id,
          iso: props.iso || ASCTools.getCountry(),
          leaf: isLeaf,
          isLeaf,
          language: 'EN',
          level: level,
          names: { EN: label },
          parentId,
        })),
      );
    };
    return (
      <AddressSelector
        maxLevel={6}
        loadEachData={async record => {
          const { level, id } = record;
          const { data: res = [] } = await request({
            url: 'mtop.lazada.merchant.onboard.task.address.list',
            method: 'POST',
            data: {
              queryLevel: level,
              locationId: id,
            },
          });
          return res.map(({ value: leafId, label, isLeaf }) => ({
            id: leafId,
            leaf: isLeaf,
            language: 'EN',
            names: { EN: label },
          }));
        }}
        loadRegionChain={async () => {
          const { value: address = [] } = props;
          return Promise.all(
            address.map((id, idx) =>
              loadPathDataService({ level: idx + 1, id: address[idx - 1] }).then(data =>
                data?.find(item => item.id == id),
              ),
            ),
          );
        }}
        {...newProps}
      />
    );
  },
  mapProps({}, (props, field) => {
    return {
      ...props,
      disabled: field.readPretty || props.disabled,
    };
  }),
);

export default AddressFormily;
