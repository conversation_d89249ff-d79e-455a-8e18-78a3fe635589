import { Box, Card } from '@alifd/next';
import { HelpTip, Image, Result } from '@alife/asc-components';
import { When } from '@alife/workstation-utils';
import React from 'react';
import '../page.scss';

const Review: React.FC<{ benefitList: any[] }> = ({ benefitList = [] }) => {
  return (
    <Card className="review">
      <Result
        type="notice"
        title="Qualification is under review"
        subTitle="Participation requests will take 5-7 business day from the date of request to be reviewed. Please come back later
        after you successfully join the project, you will get the following benefits."
      />
      <When condition={benefitList?.length > 0}>
        <Box direction="row" align="center" justify="space-between" wrap className="review-box">
          {benefitList.map(item => {
            return (
              <Box direction="column" justify="center" className="review-box-item">
                <Image src={item.icon} height={80} />
                <Box direction="row" align="center" justify="center">
                  {item?.tips ? (
                    <HelpTip help={item.tips}>
                      <div className="review-box-title">{item.title}</div>
                    </HelpTip>
                  ) : (
                    <div className="review-box-title">{item.title}</div>
                  )}
                </Box>
                <div className="review-box-text">{item.subTitle}</div>
              </Box>
            );
          })}
        </Box>
      </When>
    </Card>
  );
};

export default Review;
