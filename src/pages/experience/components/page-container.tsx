import { Loading } from '@alifd/next';
import { Page<PERSON>ontainer, PageTitle } from '@alife/asc-components';
import { Url } from '@alife/workstation-utils';
import { useRequest } from 'ahooks';
import React, { useState } from 'react';
import commonLocale from '../../../constants/common-locale';
import { EnumSellerStatus, OSC_CONFIG_MAP } from '../constants';
import { FromCodeType } from '../page';
import { querySpecialtyInfo } from '../service';
import ReviewNotApproved from './rejected';
import Review from './review';

const { refresh } = Url.getQueryParams() as any;

const ExperienceContainer: React.FC<{ id: FromCodeType; benefitList?: any }> = ({ children, id, benefitList }) => {
  const [currentStatus, setStatus] = useState<number | undefined>(undefined);
  const { loading } = useRequest(querySpecialtyInfo, {
    onSuccess: data => {
      const key = OSC_CONFIG_MAP[id].status;
      const tag = OSC_CONFIG_MAP[id].tag;
      if (refresh !== 'true' && key) {
        // 如果 xxxTag 选择为 no，就不展示 Review 或者 Reject
        if (data.result[tag] === EnumSellerStatus.NO) {
          setStatus(undefined);
        } else {
          setStatus(data.result[key]);
        }
      }
    },
  });

  return (
    <PageContainer data-spm={'d_experience_form'} className="aplus-auto-exp">
      <PageTitle
        data-spm="title"
        breadcrumb={[
          {
            label: commonLocale['page.common.nav.home'],
            link: '/apps/home/<USER>',
          },
          {
            label: commonLocale['page.common.nav.setting'],
            link: '/apps/setting/index',
          },
          {
            label: commonLocale['page.account.experience.businessExperience'],
            link: '/apps/setting/experience',
          },
          {
            label: commonLocale[`page.account.experience.${id}`],
          },
        ]}
        title={commonLocale[`page.account.experience.${id}`]}
      />
      <Loading visible={loading} style={{ width: '100%' }}>
        {currentStatus === EnumSellerStatus.PENDING && !loading && <Review benefitList={benefitList} />}
        {currentStatus === EnumSellerStatus.NO && !loading && <ReviewNotApproved id={id} />}
        {/* undefined 为初始状态或者 xxxTag 选择为 no */}
        {[EnumSellerStatus.YES, undefined].includes(currentStatus) && !loading && children}
      </Loading>
    </PageContainer>
  );
};

export default ExperienceContainer;
