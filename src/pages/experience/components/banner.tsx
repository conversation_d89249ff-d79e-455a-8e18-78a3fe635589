import { Box, Card } from '@alifd/next';
import { Image } from '@alife/asc-components';
import { dealCDNImage } from '@alife/asc-components/lib/components/image';
import React, { CSSProperties } from 'react';

const Banner: React.FC<{ img: string; style?: CSSProperties }> = ({ img, style }) => {
  const contentStyle: React.CSSProperties = {
    backgroundImage: `url(${dealCDNImage(img, {})})`,
  };

  return img ? (
    <div style={style} className="experience-banner">
      <div className="experience-banner-box" style={contentStyle}></div>
    </div>
  ) : null;
};

export default Banner;
