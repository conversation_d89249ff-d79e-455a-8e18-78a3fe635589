import React from 'react';
import { Button, Dialog, Icon, Message, Upload } from '@alifd/next';
import { UploadProps } from '@alifd/next/types/upload';
import upload from '@ali/filebroker';
import { ASCTools } from '@alife/workstation-utils';
import { connect, mapProps, observer } from '@formily/react';
import * as locale from '../locale';
import '../page.scss';

interface UploadDocumentProps extends Omit<UploadProps, 'onChange' | 'value'> {
  onChange?: (value: UploadItem[]) => void;
  value?: UploadItem[];
  infoSource: string;
}

export interface UploadItem {
  url: string;
  filename: string;
  file?: File;
}

const country: string = ASCTools.getCountry();

const HOST = {
  SG: 'filebroker.lazada.sg',
  MY: 'filebroker.lazada.com.my',
  TH: 'filebroker.lazada.co.th',
  VN: 'filebroker.lazada.vn',
  PH: 'filebroker.lazada.com.ph',
  ID: 'filebroker.lazada.co.id',
}[country];

const UploadDocument = observer<UploadDocumentProps>(({ onChange, value, infoSource, ...arg }) => {
  const fileNameRender = file => {
    return file?.name || file?.file?.name || file?.filename;
  };

  const showImg = url => {
    Dialog.show({
      title: 'img preview',
      content: <img src={url} style={{ width: 400, height: 400 }} />,
      footer: false,
    });
  };

  const actionRender = file => {
    return (
      <span style={{ marginRight: 20 }}>
        <Button
          text
          onClick={e => {
            e.preventDefault();
            showImg(value?.[0]?.url);
          }}
          size="large"
        >
          <Icon type="eye" style={{ marginRight: 8, cursor: 'pointer' }} />
        </Button>

        <Button text component="a" href={value?.[0]?.url} target="_blank">
          <Icon type="download" style={{ cursor: 'pointer' }} />
        </Button>
      </span>
    );
  };

  return (
    <Upload.Dragger
      {...arg}
      action={`//${HOST}/upload/public`}
      data={{ bizCode: `lzd_merchant_sdc_asc_upload_${country.toLocaleLowerCase()}` }}
      accept="image/png, image/jpg, image/jpeg, image/gif, image/bmp, .pdf"
      value={value}
      limit={1}
      listType="image"
      request={options => {
        const {
          action,
          file,
          data: { bizCode },
          onProgress,
          onSuccess,
          onError,
        } = options;
        upload({
          file,
          url: `${action}`,
          bizCode,
          onData: progress => {
            const { total, percent: uploadPercent, loaded } = progress;
            onProgress({ total, percent: uploadPercent, loaded });
          },
        })
          .then(data => {
            // @ts-ignore
            const { filename, code, url } = data?.data;
            onChange && onChange([{ url, filename, file }]);

            if (code === 0) {
              onSuccess('Upload Success');
              Message.success('Upload Success');
            } else {
              onError('Upload Failed');
              Message.error('Upload Failed');
            }
          })
          .catch(() => {
            onError('Upload Failed');
            Message.error('Upload Failed');
          });
      }}
      fileNameRender={fileNameRender}
      actionRender={actionRender}
    >
      <div className="next-upload-drag">
        <p className="next-upload-drag-icon">
          <Icon type="upload" />
        </p>
        <p className="next-upload-drag-text">{locale.UploadDocumentTitle}</p>
        <p className="next-upload-drag-hint">{locale.UploadDocumentType}</p>
      </div>
    </Upload.Dragger>
  );
});

const UploadDocumentFormily = connect(
  UploadDocument,
  mapProps(props => props),
);

export default UploadDocumentFormily;
