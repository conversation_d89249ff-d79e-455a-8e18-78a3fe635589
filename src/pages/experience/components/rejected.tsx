import { But<PERSON>, <PERSON> } from '@alifd/next';
import { Result } from '@alife/asc-components';
import React from 'react';
import { FromCodeType } from '../page';

const ReviewNotApproved: React.FC<{ id: FromCodeType }> = ({ id }) => {
  return (
    <Card>
      <Result
        type="error"
        title="Review Not Approved"
        subTitle="Your request to participate in the Lazada Seller Commissions Program has not been approved."
      >
        <Button
          type="primary"
          onClick={() => {
            // 跳转刷新
            location.href = `/apps/setting/experience?formCode=${id}&refresh=true`;
          }}
        >
          Re-Apply
        </Button>
      </Result>
    </Card>
  );
};

export default ReviewNotApproved;
