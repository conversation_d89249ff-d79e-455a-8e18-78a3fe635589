import * as i18n from '@alife/lazada-i18n';

export const handleSuccess = i18n.formatMessage({
  id: 'page.demo.index.handleSuccess',
  defaultMessage: 'You’ve successfully joined the Free Shipping Cashback Program!',
});
export const Yes = i18n.formatMessage({
  id: 'page.common.btn.yes',
  defaultMessage: 'Yes',
});
export const No = i18n.formatMessage({
  id: 'page.common.btn.no',
  defaultMessage: 'No',
});
export const lessThan1year = i18n.formatMessage({
  id: 'page.account.setting.lessThan1year',
  defaultMessage: 'less than 1 year',
});
export const lessThan3year = i18n.formatMessage({
  id: 'page.account.setting.lessThan3year',
  defaultMessage: '1 to 3 year',
});
export const lessThan5year = i18n.formatMessage({
  id: 'page.account.setting.lessThan5year',
  defaultMessage: '3 to 5 year',
});
export const moreThan5year = i18n.formatMessage({
  id: 'page.account.setting.moreThan5year',
  defaultMessage: 'more than 5 year',
});

export const UP_TO_USD_10_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_USD_10_THOUSAND',
  defaultMessage: 'up to USD10,000',
});

export const UP_TO_USD_50_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_USD_50_THOUSAND',
  defaultMessage: 'up to USD50,000',
});

export const UP_TO_USD_100_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_USD_100_THOUSAND',
  defaultMessage: 'up to USD100,000',
});

export const UP_TO_USD_500_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_USD_500_THOUSAND',
  defaultMessage: 'up to USD500,000',
});
export const MORE_THAN_500_USD_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.MORE_THAN_500_USD_THOUSAND',
  defaultMessage: 'more than USD500,000',
});

export const UP_TO_1_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_1_THOUSAND',
  defaultMessage: 'up to 1000',
});
export const UP_TO_10_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.moreThan5year',
  defaultMessage: 'up to 10,000',
});
export const UP_TO_20_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_20_THOUSAND',
  defaultMessage: 'up to 20,000',
});
export const UP_TO_50_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_50_THOUSAND',
  defaultMessage: 'up to 50,000',
});
export const UP_TO_100_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.UP_TO_100_THOUSAND',
  defaultMessage: 'up to 100,000',
});
export const MORE_THAN_100_THOUSAND = i18n.formatMessage({
  id: 'page.account.setting.MORE_THAN_100_THOUSAND',
  defaultMessage: 'more than 100,000',
});

export const SocialMediaTagStatusText = i18n.formatMessage({
  id: 'page.account.setting.social.media.tag.status',
  defaultMessage: 'Do you have a social media account?',
});

export const SocialMediaInfoRequired = i18n.formatMessage({
  id: 'page.account.setting.social.media.info.message',
  defaultMessage: 'select at least one account and fill it out completely',
});

export const SocialMediaTitle = i18n.formatMessage({
  id: 'page.account.setting.social.media.title',
  defaultMessage: 'Social Media Information',
});

export const SocialMediaSubTitle = i18n.formatMessage({
  id: 'page.account.setting.social.media.sub.title',
  defaultMessage:
    'Please select your store’s media platform and fill in your social media information, max three platforms can be selected',
});

export const UploadDocumentTitle = i18n.formatMessage({
  id: 'page.account.setting.upload.document.title',
  defaultMessage: 'click to download template or drag file here',
});

export const UploadDocumentType = i18n.formatMessage({
  id: 'page.account.setting.upload.document.type',
  defaultMessage: 'supports docx, xls, PDF ',
});

export const FactoryTitle = i18n.formatMessage({
  id: 'page.account.setting.factory.title',
  defaultMessage: 'Factory Information',
});

export const FactoryTagStatusText = i18n.formatMessage({
  id: 'page.account.setting.factory.tag.status',
  defaultMessage: 'Do you have an offline store?',
});

export const FactoryUploadText = i18n.formatMessage({
  id: 'page.account.setting.factory.upload',
  defaultMessage: 'Please upload your store business license',
});

export const FactoryLocationText = i18n.formatMessage({
  id: 'page.account.setting.factory.location',
  defaultMessage: 'Store Location',
});

export const FactoryAddressDetailText = i18n.formatMessage({
  id: 'page.account.setting.factory.address.detail',
  defaultMessage: 'Detail Address',
});

export const FactoryAddressText = i18n.formatMessage({
  id: 'page.account.setting.factory.address',
  defaultMessage: 'Region/City/District',
});

export const OfflineStoreTitle = i18n.formatMessage({
  id: 'page.account.setting.offline.store.title',
  defaultMessage: 'Offline Store Information',
});

export const OfflineStoreTagStatusText = i18n.formatMessage({
  id: 'page.account.setting.offline.store.tag.status',
  defaultMessage: 'Do you have an offline store?',
});

export const OfflineStoreUploadText = i18n.formatMessage({
  id: 'page.account.setting.offline.store.upload',
  defaultMessage: 'Please upload your store business license',
});

export const OfflineStoreLocationText = i18n.formatMessage({
  id: 'page.account.setting.offline.store.location',
  defaultMessage: 'Store Location',
});

export const OfflineStoreAddressDetailText = i18n.formatMessage({
  id: 'page.account.setting.offline.store.address.detail',
  defaultMessage: 'Detail Address',
});

export const TikTokShopTitle = i18n.formatMessage({
  id: 'page.account.setting.tiktok.shop.title',
  defaultMessage: 'TikTok Shop',
});

export const TikTokShopStatusText = i18n.formatMessage({
  id: 'page.account.setting.tiktok.shop.status.tag',
  defaultMessage: 'Do you have a Tiktok Shop?',
});

export const TikTokShopUploadText = i18n.formatMessage({
  id: 'page.account.setting.tiktok.shop.upload',
  defaultMessage: 'Please upload your tiktok shop screenshot',
});

export const OfflineStoreAddressText = i18n.formatMessage({
  id: 'page.account.setting.offline.store.address',
  defaultMessage: 'Region/City/District',
});

export const QualityCheckPendingTitle = i18n.formatMessage({
  id: 'page.account.setting.quality.check.pending.title',
  defaultMessage: 'Qualification is under review',
});

export const QualityCheckPendingText = i18n.formatMessage({
  id: 'page.account.setting.quality.check.pending.text',
  defaultMessage: 'Qualification is under review',
});

export const QualityCheckSuccessText = i18n.formatMessage({
  id: 'page.account.setting.quality.check.success.text',
  defaultMessage: '🥳 Congratulations on becoming an affiliate seller, go share the products and earn commission',
});

export const QualityCheckRejectTitle = i18n.formatMessage({
  id: 'page.account.setting.quality.check.reject.title',
  defaultMessage: 'Review Not Approved',
});

export const QualityCheckRejectText = i18n.formatMessage({
  id: 'page.account.setting.quality.check.reject.text',
  defaultMessage: 'Your request to participate in the Lazada Seller Commissions Program has not been approved',
});

export const NumberFollowersText = i18n.formatMessage({
  id: 'page.account.setting.number.of.followers',
  defaultMessage: 'Number of followers',
});

export const MaxPlatformsSelected = i18n.formatMessage({
  id: 'page.account.setting.max.platforms.selected',
  defaultMessage: 'Max three platforms can be selected',
});

export const SubmitSuccessful = i18n.formatMessage({
  id: 'page.account.setting.submit.successful',
  defaultMessage: 'The submission was successful. It takes 3-7 days to review.',
});

export const IAgreeTermCondition = i18n.formatMessage({
  id: 'page.account.setting.agree.term.condition',
  defaultMessage: 'I Agree',
});

export const TagYes = i18n.formatMessage({
  id: 'page.account.setting.experience.tag.yes',
  defaultMessage: 'Yes',
});

export const TagNo = i18n.formatMessage({
  id: 'page.account.setting.experience.tag.no',
  defaultMessage: 'No',
});

export const TagLocationLocal = i18n.formatMessage({
  id: 'page.account.setting.experience.location.local',
  defaultMessage: 'Local',
});

export const TagLocationOversea = i18n.formatMessage({
  id: 'page.account.setting.experience.location.oversea',
  defaultMessage: 'Oversea',
});
