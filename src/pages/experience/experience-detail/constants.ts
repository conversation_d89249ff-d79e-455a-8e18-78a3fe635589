import * as i18n from '@alife/lazada-i18n';
import * as locale from '../locale';
export interface IQuestion {
  questionCode: string;
  questionText: string;
  questionType: string;
  visible?: boolean;
  tips?: string;
  dataSource?: Array<any>;
  linkageAnswerCode?: string;
  parentQuestionCode?: string;
  className?: string;
}
export const bizCodeMap = {
  th: 'th-experience-feedback',
  sg: 'sg-experience-feedback',
  id: 'id-experience-feedback',
  ph: 'ph-experience-feedback',
  vn: 'vn-experience-feedback',
  my: 'my-experience-feedback',
};
/**
 * 问卷数据
 * @questionCode 问题标识key
 * @questionText title 多语言key
 * @options 选项 多语言key列表（可选）
 */

export const ONLINE_STORE: Array<IQuestion> = [
  {
    questionCode: 'E_COMMERCE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.ecommerceTitle',
      defaultMessage: 'Ecommerce experience on other platforms',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
  },
  {
    questionCode: 'E_COMMERCE_PLATFORM',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.ecommercePlatform',
      defaultMessage: 'Operating platform',
    }),
    questionType: 'MULTI_CHOICE',
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'E_COMMERCE',
    dataSource: [
      {
        label: 'Amazon',
        value: 'Amazon',
      },
      {
        label: 'Carousell',
        value: 'Carousell',
      },
      {
        label: 'Facebook',
        value: 'Facebook',
      },
      {
        label: 'Instagram',
        value: 'Instagram',
      },
      {
        label: 'JD',
        value: 'JD',
      },
      {
        label: 'Pinduoduo',
        value: 'Pinduoduo',
      },
      {
        label: 'Qoo10',
        value: 'Qoo10',
      },
      {
        label: 'Shopee',
        value: 'Shopee',
      },
      {
        label: 'Taobao',
        value: 'Taobao',
      },
      {
        label: 'Tiktok',
        value: 'Tiktok',
      },
      {
        label: 'Tmall',
        value: 'Tmall',
      },
      {
        label: 'Others',
        value: 'Others',
      },
    ],
    visible: false,
  },
];

export const OFFLINE_STORE: Array<IQuestion> = [
  {
    questionCode: 'OFFLINE_STORE_RETAIL',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.operations',
      defaultMessage: 'Operation of physical retail store',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
  },
  {
    questionCode: 'OFFLINE_STORE_RETAIL_ADDRESS',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.storeAddress',
      defaultMessage: 'Store address',
    }),
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'OFFLINE_STORE_RETAIL',
    questionType: 'TEXT',
  },
  {
    questionCode: 'OFFLINE_STORE_RETAIL_PICTURE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.offlinePicture',
      defaultMessage: 'Picture of store',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.offlinePictureTips',
      defaultMessage: 'Attach a picture of your store front',
    }),
    questionType: 'FILE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'OFFLINE_STORE_RETAIL',
  },
  {
    questionCode: 'OFFLINE_STORE_RETAIL_VERIFY',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.offlineStoreVerify',
      defaultMessage: 'Store verification document',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.offlineStoreVerifyTips',
      defaultMessage:
        'Provide relevant documents as proof of operation (e.g. proof of ownership of store or rental agreement)',
    }),
    questionType: 'FILE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'OFFLINE_STORE_RETAIL',
  },
  {
    questionCode: 'OFFLINE_STORE_RETAIL_YEARS',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.offlineOpYears',
      defaultMessage: 'Years in operation',
    }),
    questionType: 'SINGLE_CHOICE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'OFFLINE_STORE_RETAIL',
    dataSource: [
      {
        label: locale.lessThan1year,
        value: 'LEES_THAN_1_YEAR',
      },
      {
        label: locale.lessThan3year,
        value: '1_TO_3_YEAR',
      },
      {
        label: locale.lessThan5year,
        value: '3_TO_5_YEAR',
      },
      {
        label: locale.moreThan5year,
        value: 'MORE_THAN_5_YEAR',
      },
    ],
  },
  {
    questionCode: 'OFFLINE_STORE_RETAIL_REVENUE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.offlineRevenue',
      defaultMessage: 'Estimate annual revenue',
    }),
    questionType: 'SINGLE_CHOICE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'OFFLINE_STORE_RETAIL',
    dataSource: [
      {
        label: locale.UP_TO_USD_100_THOUSAND,
        value: 'UP_TO_USD_10_THOUSAND',
      },
      {
        label: locale.UP_TO_USD_50_THOUSAND,
        value: 'UP_TO_USD_50_THOUSAND',
      },
      {
        label: locale.UP_TO_USD_100_THOUSAND,
        value: 'UP_TO_USD_100_THOUSAND',
      },
      {
        label: locale.UP_TO_USD_500_THOUSAND,
        value: 'UP_TO_USD_500_THOUSAND',
      },
      {
        label: locale.MORE_THAN_500_USD_THOUSAND,
        value: 'MORE_THAN_500_THOUSAND',
      },
    ],
  },
];
export const SOCIAL_MEDIA: Array<IQuestion> = [
  {
    questionCode: 'SOCIAL_MEDIA',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.socialMedia',
      defaultMessage: 'Social media business accounts',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
  },
  {
    questionCode: 'SOCIAL_MEDIA_URL',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.socialMediaUrl',
      defaultMessage: 'Social media account(s) URL',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SOCIAL_MEDIA',
  },
  {
    questionCode: 'SOCIAL_MEDIA_FOLLOW',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.socialMediaFollowers',
      defaultMessage: 'Number of followers',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.UP_TO_1_THOUSAND,
        value: 'UP_TO_1_THOUSAND',
      },
      {
        label: locale.UP_TO_10_THOUSAND,
        value: 'UP_TO_10_THOUSAND',
      },
      {
        label: locale.UP_TO_20_THOUSAND,
        value: 'UP_TO_20_THOUSAND',
      },
      {
        label: locale.UP_TO_50_THOUSAND,
        value: 'UP_TO_50_THOUSAND',
      },
      {
        label: locale.UP_TO_100_THOUSAND,
        value: 'UP_TO_100_THOUSAND',
      },
      {
        label: locale.MORE_THAN_100_THOUSAND,
        value: 'MORE_THAN_100_THOUSAND',
      },
    ],
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SOCIAL_MEDIA',
  },
];

export const STORE_MANAGEMENT: Array<IQuestion> = [
  {
    questionCode: 'STORE_MANAGEMENT',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.management',
      defaultMessage: 'Operation of store',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.managementTips',
      defaultMessage:
        'Please state whether you operate your store independently or you have engaged a third party vendor to assist you with operations (e.g. managing of online store, fulfilment of orders etc.)',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: 'Self operate',
        value: 'SELF_OPERATE',
      },
      {
        label: 'Engage shared services',
        value: 'ENGAGE_SHARE_SERVICES',
      },
    ],
  },
  {
    questionCode: 'STORE_MANAGEMENT_NAME',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.serviceName',
      defaultMessage: 'Name of shared services provider',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'ENGAGE_SHARE_SERVICES',
    parentQuestionCode: 'STORE_MANAGEMENT',
  },
  {
    questionCode: 'STORE_MANAGEMENT_EMPLOYEE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.serviceNumber',
      defaultMessage: 'Number of employees managaing your Lazada store',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: '0-5',
        value: 'ZERO_TO_FIVE',
      },
      {
        label: '6-10',
        value: 'SIX_TO_TEN',
      },
      {
        label: '11-50',
        value: 'ELEVEN_TO_FIFTY',
      },
      {
        label: '51-100',
        value: 'FIFTY_ONE_TO_HUNDRED',
      },
      {
        label: 'More than 100',
        value: 'MORE_THAN_HUNDRED',
      },
    ],
  },
];

export const SUPPLY_CHAIN: Array<IQuestion> = [
  {
    questionCode: 'SUPPLY_CHAIN',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.supplyChain',
      defaultMessage: 'Ownership of factory',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.supplyChainTips',
      defaultMessage: 'Please state if you own a factory, which produces the goods that you are selling on Lazada',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
  },
  {
    questionCode: 'SUPPLY_CHAIN_ADDRESS',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.factoryAddress',
      defaultMessage: 'Factory address',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SUPPLY_CHAIN',
  },
  {
    questionCode: 'SUPPLY_CHAIN_PHOTO',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.factoryPicture',
      defaultMessage: 'Photo of factory ',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.factoryPictureTips',
      defaultMessage: 'Attach a picture of the front entrance of your factory ',
    }),
    questionType: 'FILE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SUPPLY_CHAIN',
  },
  {
    questionCode: 'SUPPLY_CHAIN_VERIFY',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.factoryVerify',
      defaultMessage: 'Factory verification document',
    }),
    tips: i18n.formatMessage({
      id: 'page.account.experience.factoryVerifyTips',
      defaultMessage: 'Provide relevant documents as proof of ownership of the factory',
    }),
    questionType: 'FILE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SUPPLY_CHAIN',
  },
  {
    questionCode: 'SUPPLY_CHAIN_SOURCE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.factorySource',
      defaultMessage: 'Source of inventory',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: 'self manufactured',
        value: 'SELF_MANUFACTURED',
      },
      {
        label: 'source from supplier',
        value: 'SOURCE_FROM_SUPPLIER',
      },
    ],
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SUPPLY_CHAIN',
  },
  {
    questionCode: 'SUPPLY_CHAIN_SOURCE_SUPPLIER',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.factoryName',
      defaultMessage: 'Name of supplier',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'SUPPLY_CHAIN',
  },
];
export const BRAND_INFO: Array<IQuestion> = [
  {
    questionCode: 'BRAND',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.brand',
      defaultMessage: 'Brand ownership',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
  },
  {
    questionCode: 'BRAND_LAZ_MALL',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.lazMallSeller',
      defaultMessage: ' Do you want to register as a LazMall seller?',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: locale.Yes,
        value: 'YES',
      },
      {
        label: locale.No,
        value: 'NO',
      },
    ],
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'BRAND',
  },
  {
    questionCode: 'BRAND_NAME',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.brandName',
      defaultMessage: 'Brand name',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'BRAND',
  },
  {
    questionCode: 'BRAND_WEBSITE',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.brandLink',
      defaultMessage: 'Brand Website',
    }),
    questionType: 'TEXT',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'BRAND',
  },
  {
    questionCode: 'BRAND_RELATION',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.brandRelation',
      defaultMessage: 'Relation with the brand',
    }),
    questionType: 'SINGLE_CHOICE',
    dataSource: [
      {
        label: 'Brand Owner',
        value: 'BRAND_OWNER',
      },
      {
        label: 'Exclusive Distributor',
        value: 'BRAND_Exclusive',
      },
      {
        label: 'Non-Exclusive Distributor',
        value: 'BRAND_Non_Exclusive',
      },
      {
        label: 'Retailer',
        value: 'BRAND_Retailer',
      },
    ],
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'BRAND',
  },
  {
    questionCode: 'BRAND_AUTHENCITY',
    questionText: i18n.formatMessage({
      id: 'page.account.experience.brandProof',
      defaultMessage: 'Proof of authencity ',
    }),
    questionType: 'FILE',
    visible: false,
    linkageAnswerCode: 'YES',
    parentQuestionCode: 'BRAND',
  },
];

const PLATFORM = [
  'Amazon',
  'Carousell',
  'Facebook',
  'Instagram',
  'JD',
  'Pinduoduo',
  'Qoo10',
  'Shopee',
  'Taobao',
  'Tiktok',
  'Tmall',
  'Others',
];

export const PLATFORMS = PLATFORM.map(item => {
  const name = item.toUpperCase();
  ONLINE_STORE.push(
    {
      questionCode: `E_COMMERCE_${name}_URL`,
      questionText:
        i18n.formatMessage({
          id: 'page.account.experience.ecommercePlatformUrl',
          defaultMessage: 'Online platform URL for ',
        }) + item,
      questionType: 'TEXT',
      linkageAnswerCode: item,
      parentQuestionCode: 'E_COMMERCE_PLATFORM',
      visible: false,
      tips: i18n.formatMessage({
        id: 'page.account.experience.ecommercePlatformUrlTips',
        defaultMessage: 'Provide the link to the main page of your online store',
      }),
    },
    {
      questionCode: `E_COMMERCE_${name}_VERIFY`,
      questionText:
        i18n.formatMessage({
          id: 'page.account.experience.ecommercePlatformVerify',
          defaultMessage: 'Online platform verification for ',
        }) + item,
      linkageAnswerCode: item,
      tips: i18n.formatMessage({
        id: 'page.account.experience.ecommercePlatformVerifyTips',
        defaultMessage: 'E.g. upload a screeshot of your online store front',
      }),
      parentQuestionCode: 'E_COMMERCE_PLATFORM',
      questionType: 'FILE',
      visible: false,
    },
    {
      questionCode: `E_COMMERCE_${name}_YEARS`,
      questionText:
        i18n.formatMessage({
          id: 'page.account.experience.ecommercePlatformYears',
          defaultMessage: 'Years in operation for ',
        }) + item,
      linkageAnswerCode: item,
      parentQuestionCode: 'E_COMMERCE_PLATFORM',
      questionType: 'SINGLE_CHOICE',
      visible: false,
      dataSource: [
        {
          label: locale.lessThan1year,
          value: 'LEES_THAN_1_YEAR',
        },
        {
          label: locale.lessThan3year,
          value: '1_TO_3_YEAR',
        },
        {
          label: locale.lessThan5year,
          value: '3_TO_5_YEAR',
        },
        {
          label: locale.moreThan5year,
          value: 'MORE_THAN_5_YEAR',
        },
      ],
    },
    {
      questionCode: `E_COMMERCE_${name}_REVENUE`,
      questionText:
        i18n.formatMessage({
          id: 'page.account.experience.ecommercePlatformEstimate',
          defaultMessage: 'Estimate annual revenue for ',
        }) + item,
      questionType: 'SINGLE_CHOICE',
      linkageAnswerCode: item,
      parentQuestionCode: 'E_COMMERCE_PLATFORM',
      visible: false,
      dataSource: [
        {
          label: locale.UP_TO_USD_100_THOUSAND,
          value: 'UP_TO_USD_10_THOUSAND',
        },
        {
          label: locale.UP_TO_USD_50_THOUSAND,
          value: 'UP_TO_USD_50_THOUSAND',
        },
        {
          label: locale.UP_TO_USD_100_THOUSAND,
          value: 'UP_TO_USD_100_THOUSAND',
        },
        {
          label: locale.UP_TO_USD_500_THOUSAND,
          value: 'UP_TO_USD_500_THOUSAND',
        },
        {
          label: locale.MORE_THAN_500_USD_THOUSAND,
          value: 'MORE_THAN_500_THOUSAND',
        },
      ],
      className: 'divider-line',
    },
  );
});

export const formMaps = {
  ONLINE_STORE,
  OFFLINE_STORE,
  SUPPLY_CHAIN,
  SOCIAL_MEDIA,
  STORE_MANAGEMENT,
  BRAND_INFO,
  // 后续新增的类型，已经独立出来是一个单独的页面，不需要配置
  TIKTOK_SHOP: [],
};
