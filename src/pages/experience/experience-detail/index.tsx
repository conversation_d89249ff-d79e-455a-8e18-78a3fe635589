import React from 'react';
import { getExperienceForm, saveExperienceForm } from '../service';
import { Switch, Case, Default } from '@alife/workstation-utils';
import { Loading, Button, Message, Icon, Card } from '@alifd/next';
import { useRequest } from 'ahooks';
import { SubmitAffix, useLzdOss, HelpTip } from '@alife/asc-components';
import { formMaps, IQuestion } from './constants';
import { createForm, onFormMount } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { Field, useForm } from '@formily/react';
import { Form, FormItem, Input, Submit, Radio, Select, Upload } from '@formily/next';
import values from 'lodash/values';
import commonLocale from '../../../constants/common-locale';
import { FromCodeType } from '../page';
import ExperienceContainer from '../components/page-container';
import './index.scss';

enum QUESTION_TYPE {
  MULTI_CHOICE = 'MULTI_CHOICE',
  TEXT = 'TEXT',
  FILE = 'FILE',
  SINGLE_CHOICE = 'SINGLE_CHOICE',
}
const isJsonString = str => {
  try {
    if (typeof JSON.parse(str) == 'object') {
      return true;
    }
  } catch (e) {}
  return false;
};
const transMap = (arr, name) => {
  if (!arr) return {};
  return arr.reduce((fields, item) => {
    const variable = item[name];
    fields[variable] = item['answerText'];
    return fields;
  }, {});
};
const DraggerUpload = props => {
  return (
    <Upload {...props}>
      <Button className="next-upload-card">
        <Icon type="upload" />
        {commonLocale['page.common.btn.upload']}
      </Button>
    </Upload>
  );
};
const Question = (item: IQuestion) => {
  const {
    questionType,
    questionText,
    questionCode,
    dataSource,
    visible,
    linkageAnswerCode,
    parentQuestionCode = '',
    tips = '',
    className = '',
  } = item;
  const form = useForm();
  const findValueAdapt = () => {
    if (form.values) {
      return (
        form.values &&
        form.values[parentQuestionCode] &&
        (values(form.values[parentQuestionCode])?.indexOf(linkageAnswerCode) > -1 ||
          form.values[parentQuestionCode]?.indexOf(linkageAnswerCode) > -1)
      );
    }
    return false;
  };
  return (
    <div className={`question`}>
      <Switch>
        <Case when={questionType === QUESTION_TYPE.TEXT}>
          <Field
            reactions={field => {
              if (!parentQuestionCode || findValueAdapt()) {
                field.visible = true;
              } else {
                field.visible = false;
              }
            }}
            required
            visible={visible}
            name={questionCode}
            title={<HelpTip help={tips}>{questionText}</HelpTip>}
            decorator={[FormItem]}
            component={[
              Input,
              {
                className: className,
                onBlur: e => {
                  const value = e.target.value;
                  form.setValues({
                    [questionCode]: value.trim(),
                  });
                },
              },
            ]}
          />
        </Case>
        <Case when={questionType === QUESTION_TYPE.FILE}>
          <Field
            reactions={field => {
              if (!parentQuestionCode || findValueAdapt()) {
                if (field.value && isJsonString(field.value)) {
                  field.value = JSON.parse(field.value);
                }
                field.visible = true;
              } else {
                field.visible = false;
              }
            }}
            visible={visible}
            required
            name={questionCode}
            title={<HelpTip help={tips}>{questionText}</HelpTip>}
            decorator={[FormItem]}
            component={[
              DraggerUpload,
              {
                ...useLzdOss({
                  accept:
                    'image/png, image/jpg, image/jpeg, image/gif, image/bmp, application/pdf, application/msword , application/vnd.ms-excel,  application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                  restrict: ['width>=300px'],
                  maxFileSize: '20mb',
                }),
              },
            ]}
          />
        </Case>
        <Case when={questionType === QUESTION_TYPE.SINGLE_CHOICE}>
          <Field
            required
            reactions={field => {
              if (!parentQuestionCode || findValueAdapt()) {
                field.visible = true;
              } else {
                field.visible = false;
              }
            }}
            visible={visible}
            name={questionCode}
            title={<HelpTip help={tips}>{questionText}</HelpTip>}
            dataSource={dataSource}
            decorator={[FormItem]}
            component={[Radio.Group, { className: className }]}
          />
        </Case>
        <Case when={questionType === QUESTION_TYPE.MULTI_CHOICE}>
          <Field
            required
            reactions={field => {
              if (!parentQuestionCode || findValueAdapt()) {
                if (field.value && isJsonString(field.value)) {
                  field.value = JSON.parse(field.value);
                }
                field.visible = true;
              } else {
                field.visible = false;
              }
            }}
            visible={visible}
            name={questionCode}
            title={<HelpTip help={tips}>{questionText}</HelpTip>}
            dataSource={dataSource}
            decorator={[FormItem]}
            component={[
              Select,
              {
                mode: 'multiple',
              },
            ]}
          />
        </Case>
        <Default>
          <Field
            required
            visible={visible}
            name={questionCode}
            title={<HelpTip help={tips}>{questionText}</HelpTip>}
            decorator={[FormItem]}
            component={[
              Input,
              {
                trim: true,
              },
            ]}
          />
        </Default>
      </Switch>
    </div>
  );
};

export const ExperiencePage = ({ id }: { id: FromCodeType }) => {
  const { data, error, loading } = useRequest(getExperienceForm);
  const module = data?.data;
  let initialValues = {};
  if (!error && module) {
    initialValues = transMap(module, 'questionCode');
  }
  const { autoFormTrack, setError } = withTrackEffects({
    formKey: `business-experience-${id}`,
  });
  const form = createForm({
    effects: form => {
      autoFormTrack();
      onFormMount(() => {
        form.setValues(initialValues);
      });
    },
  });

  const submit = async values => {
    const businessExperienceFormEntryVOS = formMaps[id]
      .map(item => {
        const { questionCode, questionType, linkageAnswerCode, parentQuestionCode, questionText } = item;
        if (!values[questionCode]) return;
        let params = {
          answerText: values[questionCode],
          questionCode,
          questionType,
          linkageAnswerCode,
          parentQuestionCode,
          questionText,
        };
        if (questionType === 'FILE') {
          if (values[questionCode] && values[questionCode].length > 0) {
            const keyArray = values[questionCode]?.map(item => {
              if (item.key) {
                return item;
              }
              const {
                response: { tokenInfo },
                name,
                downloadURL,
              } = item;
              const { key } = tokenInfo;
              return {
                key,
                name,
                downloadURL,
              };
            });
            params.answerText = keyArray;
          }
        }
        return params;
      })
      .filter(item => item !== undefined);
    const { data, error } = await saveExperienceForm({
      businessExperienceFormEntryVOS: JSON.stringify(businessExperienceFormEntryVOS),
    });
    if (data) {
      Message.success(commonLocale['page.common.tips.success']);
      location.href = '/apps/setting/experience';
    } else {
      setError(error);
      Message.error(commonLocale['page.common.tips.error']);
    }
  };

  return (
    <ExperienceContainer id={id}>
      <Card>
        <Switch>
          <Case when={loading}>
            <Loading style={{ width: '100%', height: 200 }} />
          </Case>
          <Case when={!!error}>something wrong</Case>
          <Default>
            <Form className="experience-form" layout={'vertical'} form={form}>
              {formMaps[id] && formMaps[id].map(item => <Question {...item} />)}
              <SubmitAffix>
                <div
                  style={{ textAlign: 'left', width: '100%', fontSize: '12px' }}
                  dangerouslySetInnerHTML={{ __html: commonLocale['page.account.experience.importantComment'] }}
                ></div>
                <Button
                  type="secondary"
                  onClick={() => {
                    location.href = '/apps/setting/experience';
                  }}
                >
                  {commonLocale['page.common.btn.cancel']}
                </Button>
                <Submit
                  data-spm={`d_click_submit_${id}`}
                  className="aplus-auto-exp"
                  onSubmit={submit}
                  onSubmitSuccess={e => {}}
                  onSubmitFailed={() => {
                    const errors = document.getElementsByClassName('next-formily-item-error-help');
                    if (errors) {
                      errors?.[0].scrollIntoView({
                        block: 'center',
                      });
                    }
                  }}
                >
                  {commonLocale['page.common.btn.submit']}
                </Submit>
              </SubmitAffix>
            </Form>
          </Default>
        </Switch>
      </Card>
    </ExperienceContainer>
  );
};
