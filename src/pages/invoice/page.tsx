import { Card, Loading, Button, Message, Box } from '@alifd/next';
import commonLocale from '../../constants/common-locale';
import { Form, FormItem, Input, Select, Reset, Submit, FormLayout, Radio } from '@formily/next';
import React, { useState } from 'react';
import * as i18n from '@alife/lazada-i18n';
import { createForm } from '@formily/core';
import { axios } from '@alife/workstation-utils';
import { Field, FormConsumer } from '@formily/react';
import { SubmitAffix, PageContainer } from '@alife/asc-components';
import { withTrackEffects } from '@alife/form-track-plugin';
import { useRequest, useMemoizedFn } from 'ahooks';
import SettingPageTitle from '../../components/setting-page-title';

enum GENERATE_TYPES_VALUES {
  AUTO = 'autoincrement_number',
  ORDER = 'order_number',
  MANUALLY = 'show_dialog',
}

const GENERATE_TYPES_OPTIONS = [
  {
    label: i18n.formatMessage({
      id: 'page.account.setting.useAutoincrementNumber',
      defaultMessage: 'Use Autoincrement Number',
    }),
    value: GENERATE_TYPES_VALUES.AUTO,
  },

  {
    label: i18n.formatMessage({ id: 'page.account.setting.useOrderNumber', defaultMessage: 'Use Order Number' }),
    value: GENERATE_TYPES_VALUES.ORDER,
  },

  {
    label: i18n.formatMessage({
      id: 'page.account.setting.provideNumberManually',
      defaultMessage: 'Provide number manually',
    }),
    value: GENERATE_TYPES_VALUES.MANUALLY,
  },
];

const { autoFormTrack, setError } = withTrackEffects({
  formKey: 'update_invoice_number',
});

const form = createForm({
  editable: false,
  effects() {
    autoFormTrack();
  },
});

export default (props = {}) => {
  const formState = form.getState();
  const [submiting, setSubmitting] = useState(false);
  const [editing, setEditing] = useState(formState.editable);

  // debugger
  const { loading, run } = useRequest(
    () =>
      axios
        .get('mtop.lazada.seller.order.query.invoice.number')
        .then(({ data: result }) => {
          if (result?.data?.generationType) {
            form.setInitialValues({ ...result?.data });
          } else {
            form.setInitialValues({
              generationType: GENERATE_TYPES_VALUES.MANUALLY,
            });
          }
        })
        .catch(e => {
          Message.error(e.message);
          form.setInitialValues({
            generationType: GENERATE_TYPES_VALUES.MANUALLY,
          });
        }),
    {},
  );

  const setFormEditing = useMemoizedFn(bool => {
    setEditing(bool);
    form.setState(state => {
      state.editable = bool;
    });
    if (bool === false) {
      run();
    }
  });

  const onSubmit = useMemoizedFn(() => {
    setSubmitting(true);
    // console.log(form.values);
    return axios
      .post('mtop.lazada.seller.order.update.invoice.number', {
        ...form.values,
      })
      .then(() => {
        Message.success(commonLocale['page.common.tips.success']);
        setFormEditing(false);
      })
      .catch(e => {
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        setError(message);
        // console.error(e);
      })
      .then(() => {
        setSubmitting(false);
      });
  });

  const invoiceTip = currentForm => {
    if (currentForm.values.generationType === GENERATE_TYPES_VALUES.MANUALLY) {
      return <></>;
    }

    let demo = '';
    if (currentForm.values.generationType === GENERATE_TYPES_VALUES.ORDER) {
      demo = `${currentForm.values.prefix ?? ''} 123456`;
    }

    if (currentForm.values.generationType === GENERATE_TYPES_VALUES.AUTO) {
      demo = `${currentForm.values.prefix ?? ''} ${currentForm.values.nextNumber ?? ''}`;
    }

    return (
      <Message type="notice" style={{ backgroundColor: '#efefef', border: 'none', display: 'inline-block' }}>
        {i18n.formatMessage(
          {
            defaultMessage: 'Next invoice number will look like this: {demo}',
            id: 'page.invoice.tipWithValue',
          },

          { demo },
        )}
      </Message>
    );
  };

  return (
    <PageContainer>
      <SettingPageTitle title={commonLocale['page.common.nav.invoiceNumber']} />
      <Loading data-spm={props['data-spm'] || 'mrender'} visible={loading} inline={false}>
        <Form form={form} labelCol={6} wrapperCol={10}>
          <Card>
            <FormLayout layout="vertical" wrapperWidth={320} colon={false}>
              <Field
                name={'generationType'}
                title={'Generate Type'}
                required
                decorator={[FormItem]}
                component={[Select, { dataSource: GENERATE_TYPES_OPTIONS }]}
              />

              <Field
                name={'prefix'}
                title={i18n.formatMessage({
                  id: 'page.account.setting.invoicePrefix',
                  defaultMessage: 'Invoice Prefix',
                })}
                validator={{ max: 45 }}
                decorator={[FormItem]}
                component={[Input]}
                reactions={field => {
                  const visible = form.values.generationType !== GENERATE_TYPES_VALUES.MANUALLY;
                  field.visible = visible;
                  field.required = visible;
                }}
              />

              <Field
                name={'nextNumber'}
                title={i18n.formatMessage({
                  id: 'page.account.setting.nextNumberInsertNumbersOnly',
                  defaultMessage: 'Next Number (Insert numbers only)',
                })}
                validator={{ max: 19 }}
                decorator={[FormItem]}
                component={[Input]}
                reactions={field => {
                  const visible = form.values.generationType === GENERATE_TYPES_VALUES.AUTO;
                  field.visible = visible;
                  field.required = visible;
                }}
              />

              <Field
                name={'enableRegisterAddressForInvoice'}
                title={i18n.formatMessage({
                  id: 'page.account.setting.enableRegisterAddressForInvoice',
                  defaultMessage: 'Enable register address for invoice',
                })}
                validator={{ max: 19 }}
                decorator={[FormItem]}
                component={[
                  Radio.Group,
                  {
                    dataSource: [
                      {
                        label: (
                          <>
                            {i18n.formatMessage({
                              id: 'page.account.setting.byAddress',
                              defaultMessage: 'Business Address',
                            })}
                            (
                            <Button component="a" text target="_blank" href="/apps/setting/business">
                              {i18n.formatMessage({
                                id: 'page.account.setting.check',
                                defaultMessage: 'Check',
                              })}
                            </Button>
                            )
                          </>
                        ),
                        value: true,
                      },
                      {
                        label: (
                          <>
                            {i18n.formatMessage({
                              id: 'page.account.setting.byWarehouse',
                              defaultMessage: 'Warehouse Address',
                            })}
                            (
                            <Button component="a" text target="_blank" href="/apps/setting/warehouse_address">
                              {i18n.formatMessage({
                                id: 'page.account.setting.check',
                                defaultMessage: 'Check',
                              })}
                            </Button>
                            )
                          </>
                        ),
                        value: false,
                      },
                    ],
                  },
                ]}
              />
              <FormConsumer>{invoiceTip}</FormConsumer>
            </FormLayout>
          </Card>
          <SubmitAffix style={{ marginTop: 20 }}>
            {editing ? (
              <>
                <Reset data-spm="d_cancel" className="aplus-auto-exp" onClick={() => setFormEditing(false)}>
                  {commonLocale['page.common.btn.cancel']}
                </Reset>
                <Submit data-spm="d_submit" className="aplus-auto-exp" loading={submiting} onSubmit={onSubmit}>
                  {commonLocale['page.common.btn.submit']}
                </Submit>
              </>
            ) : (
              <Button
                data-spm="d_modify"
                className="aplus-auto-exp"
                onClick={() => setFormEditing(true)}
                type="primary"
              >
                {commonLocale['page.common.btn.modify']}
              </Button>
            )}
          </SubmitAffix>
        </Form>
      </Loading>
    </PageContainer>
  );
};
