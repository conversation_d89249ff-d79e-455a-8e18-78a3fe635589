import * as i18n from '@alife/lazada-i18n';
import SettingPageTitle from '../../components/setting-page-title';
import commonLocale from '../../constants/common-locale';
import dadaAdaptor from '../../utils/dada-adaptor';
import React from 'react';
import { Table, Card, Loading, Switch, Message } from '@alifd/next';
import { ASCTools, axios } from '@alife/workstation-utils';
import { Pagination, PageContainer, useFormDialog } from '@alife/asc-components';
import { useMemoizedFn, useRequest, useFusionTable, useMount } from 'ahooks';
import { TARGET_TABLES, getCBFlag, updateHolidayMode, updateRCHolidayMode } from './services';
import { DialogContent } from './edit-dialog';
import './page.scss';

const country = ASCTools.getCountry();
const noop = () => {};
const HolidayTable = ({ tableProps = {}, targetTable, paginationProps = { total: 1 }, afterSetting }) => {
  // Place useFormDialog here will generate 3 DialogContent inside page
  // but we can avoid the call delegate layer by layer on props
  const [EditDialog, { edit }] = useFormDialog({
    content: DialogContent,
    dialogProps: {
      footer: false,
      title: i18n.formatMessage({
        id: 'page.account.setting.updateHolidayMode',
        defaultMessage: 'Update Holiday Mode',
      }),
    },
  });

  const renderDate = useMemoizedFn((value, index, row) => {
    return row.isOpen ? value : <span className="not-set-text">{commonLocale['page.common.tips.notSet']}</span>;
  });
  const onSwitch = useMemoizedFn((v, r) => {
    const updateFunction = ['settingAllWhTable'].includes(targetTable) ? updateHolidayMode : updateRCHolidayMode;
    if (!v) {
      updateFunction({
        holidayMode: v,
        type: targetTable,
        targetTable,
        code: r.code,
        date: `${r.start},${r.end}`,
      })
        .then(() => {
          Message.success(commonLocale['page.common.tips.success']);
          if (typeof afterSetting === 'function') {
            afterSetting();
          }
        })
        .catch(e => {
          Message.error(e.message);
        });
    } else {
      edit({
        ...r,
        targetTable,
      })
        .then(() => {
          Message.success(commonLocale['page.common.tips.success']);
          if (typeof afterSetting === 'function') {
            afterSetting();
          }
        })
        .catch(() => noop);
    }
  });
  const renderAction = useMemoizedFn((value, index, row) => {
    return (
      <Switch
        className="aplus-auto-exp"
        data-spm="d_switch"
        onChange={v => {
          onSwitch(v, row);
        }}
        checked={row.isOpen}
      />
    );
  });

  return (
    <>
      <Table {...tableProps}>
        <Table.Column
          title={i18n.formatMessage({ id: 'page.account.setting.name', defaultMessage: 'Name' })}
          width={200}
          dataIndex="name"
        />
        {targetTable === TARGET_TABLES.HOLIDAY_PER ? (
          <Table.Column
            width={200}
            title={i18n.formatMessage({ id: 'page.account.setting.code', defaultMessage: 'Code' })}
            dataIndex="code"
          />
        ) : null}
        <Table.Column
          title={i18n.formatMessage({ id: 'page.account.setting.date', defaultMessage: 'Date' })}
          dataIndex="dateFormat"
          cell={renderDate}
        />
        <Table.Column
          title={i18n.formatMessage({ id: 'page.account.setting.action', defaultMessage: 'Action' })}
          lock="right"
          cell={renderAction}
        />
      </Table>
      <Pagination hideOnlyOnePage {...paginationProps} />
      <EditDialog />
    </>
  );
};
// mtop.global.merchant.subaccount.multi.address.holiday.init  mtop.lazada.rc.seller.address.holiday.init
export default () => {
  const { data: initData } = useRequest(() =>
    axios
      .get('mtop.lazada.rc.seller.address.holiday.init')
      .then(({ data: res }) => {
        const allNewWarehouse = res?.module?.[0] || [];
        return allNewWarehouse;
      })
      .catch(e => {
        console.error(e);
        return {};
      }),
  );
  // mtop.global.merchant.subaccount.multi.address.new.holiday.all mtop.lazada.rc.seller.all.warehouse.holiday.query
  const { data: holidayAll, loading: holidayAllLoading, run: reloadHolidayAll } = useRequest(() =>
    axios
      .get('mtop.lazada.rc.seller.all.warehouse.holiday.query')
      .then(({ data: res }) => {
        return [res.module];
      })
      .catch(e => {
        console.error(e);
        return {};
      }),
  );

  const { data: closureAll, loading: closureAllLoading, run: reloadClosureAll } = useRequest(() =>
    axios
      .get('mtop.global.merchant.subaccount.multi.address.holiday.all')
      .then(({ data: res }) => res.data)
      .catch(e => {
        console.error(e);
        return {};
      }),
  );

  const { paginationProps, tableProps, run: reloadHolidayPer } = useFusionTable(
    pageInfo =>
      axios
        .get('mtop.lazada.rc.warehouse.holiday.query', {
          params: pageInfo,
        })
        .then(({ data: res }) => ({
          total: res?.totalCount || 0,
          list: res?.modelList || [],
        })),

    {},
  );

  const reloadHolidayPerReal = useMemoizedFn(() => {
    const pageInfo = {
      current: paginationProps.current,
      pageSize: paginationProps.pageSize,
    };

    reloadHolidayPer(pageInfo);
  });

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.holiday']} />

      <Loading visible={closureAllLoading}>
        <Card
          showHeadDivider={false}
          showTitleBullet={false}
          title={i18n.formatMessage({
            id: 'page.account.setting.holidayModeWithStoreClosure',
            defaultMessage: 'Holiday mode with store closure',
          })}
          className="mb-standard"
        >
          <HolidayTable
            targetTable={TARGET_TABLES.CLOSURE_ALL}
            afterSetting={reloadClosureAll}
            tableProps={{
              rowProps: (_, idx) => ({
                ['data-spm']: `closure_${idx}`,
              }),

              dataSource: closureAll?.dataSource,
            }}
          />
        </Card>
      </Loading>

      {initData?.visible ? (
        <Loading visible={holidayAllLoading}>
          <Card
            showHeadDivider={false}
            showTitleBullet={false}
            title={i18n.formatMessage({
              id: 'page.account.setting.holidayModeWithStoreOpen',
              defaultMessage: 'Holiday mode with store open & only fulfilment extension',
            })}
            className="mb-standard"
          >
            <HolidayTable
              targetTable={TARGET_TABLES.HOLIDAY_ALL}
              afterSetting={reloadHolidayAll}
              tableProps={{
                rowProps: (_, idx) => ({
                  ['data-spm']: `all_${idx}`,
                }),

                dataSource: holidayAll,
              }}
            />
          </Card>
        </Loading>
      ) : null}

      {tableProps?.dataSource?.length ? (
        <Card
          showHeadDivider={false}
          showTitleBullet={false}
          title={i18n.formatMessage({
            id: 'page.account.setting.holidayModeForPerWh',
            defaultMessage: 'Holiday Mode for per WH',
          })}
        >
          <HolidayTable
            afterSetting={reloadHolidayPerReal}
            targetTable={TARGET_TABLES.HOLIDAY_PER}
            tableProps={{
              ...tableProps,
              rowProps: (_, idx) => ({
                ['data-spm']: `per_${idx}`,
              }),
            }}
            paginationProps={paginationProps}
          />
        </Card>
      ) : null}
    </PageContainer>
  );
};
