import { axios } from '@alife/workstation-utils';

export enum TARGET_TABLES {
  HOLIDAY_ALL = 'settingAllNewWhTable',
  HOLIDAY_PER = 'settingPerWhTable',
  CLOSURE_ALL = 'settingAllWhTable',
}

export interface IUpdateParams {
  targetTable?: TARGET_TABLES;
  code?: string;
  date?: string;
  holidayMode: boolean;
  type?: TARGET_TABLES;
}

export const updateRCHolidayMode = (params: IUpdateParams) => {
  // targetTable: settingAllNewWhTable/settingPerWhTable
  // holidayMode: false
  // code: SG101BB-WH-10003
  // date: 2021-06-01 00:00:00,2021-07-01 00:00:00
  // Imigration to RC mtop.global.merchant.subaccount.multi.address.holiday.update

  return axios.post('mtop.lazada.rc.holiday.update', params).catch(e => {
    throw new Error(e.data?.data?.error || e.message);
  });
};

export const updateHolidayMode = (params: IUpdateParams) => {
  // targetTable: settingAllNewWhTable/settingPerWhTable
  // holidayMode: false
  // code: SG101BB-WH-10003
  // date: 2021-06-01 00:00:00,2021-07-01 00:00:00
  return axios.post('mtop.global.merchant.subaccount.multi.address.holiday.update', params).catch(e => {
    throw new Error(e.data?.data?.error || e.message);
  });
};

export const getCBFlag = () => {
  return axios
    .get('mtop.lazada.onboard.cb.registration.getJoinGoGlobal')
    .then(result => {
      const data = result?.data?.data || {};
      const cb = data.sellerType === 'cb';
      return cb;
    })
    .catch(e => false);
};
