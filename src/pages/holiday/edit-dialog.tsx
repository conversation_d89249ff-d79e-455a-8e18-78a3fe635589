import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
// import { useRequest } from 'ahooks';

import { Button, Message } from '@alifd/next';
import { createForm } from '@formily/core';
import { Field, FormConsumer } from '@formily/react';
import { Form, FormItem, Input, FormLayout, FormButtonGroup, DatePicker } from '@formily/next';
import moment from 'moment';
import { withTrackEffects } from '@alife/form-track-plugin';
import { useCreation, useMemoizedFn } from 'ahooks';
import { updateHolidayMode, updateRCHolidayMode } from './services';
import commonLocale from '../../constants/common-locale';

const currentDate = moment();
const noop = () => {};

export const DialogContent = ({ record, ok, close }) => {
  // console.log('dialog form context', operateType, record);
  // console.log('dialog form values', field.getValues());

  const [hasDbChecked, setHasDbChecked] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');

  const [form, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'open_holiday_mode',
    });

    return [
      createForm({
        effects() {
          autoFormTrack();
        },
      }),
      setError,
    ];
  }, record?.code);

  const onSubmit = useMemoizedFn(() => {
    const updateFunction = ['settingAllWhTable'].includes(record?.targetTable)
      ? updateHolidayMode: updateRCHolidayMode;
    const date = `${form.values?.period[0]} 00:00:00,${form.values?.period[1]} 00:00:00`;
    updateFunction({
      type: record.targetTable,
      targetTable: record.targetTable,
      code: record.code,
      date,
      holidayMode: true,
    })
      .then(res => {
        ok();
      })
      .catch(e => {
        // debugger;
        setErrorMsg(e.message);
        setTrackerError(e.message);
      });
  });

  // not allow to choose the passed date (include today)
  const disabledDate = useMemoizedFn((date, view) => {
    switch (view) {
      case 'date':
        return date.valueOf() <= currentDate.valueOf();
      case 'year':
        return date.year() < currentDate.year();
      case 'month':
        return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
      default:
        return false;
    }
  });
  const onDoubleCheck = useMemoizedFn(() => {
    form
      .validate()
      .then(() => {
        setHasDbChecked(true);
      })
      .catch(noop);
  });
  const onCancel = () => {
    close();
  };

  return (
    <Form form={form}>
      {hasDbChecked ? (
        <div style={{ width: 500 }}>
          {errorMsg !== '' ? (
            <p className="option-error">
              {commonLocale['page.common.tips.error']}: {errorMsg}
            </p>
          ) : null}
          <p>
            {i18n.formatMessage({
              id: 'page.account.setting.pleaseConfirmThatYouHave',
              defaultMessage: 'Please confirm that you have set up the holiday mode for below date:',
            })}
          </p>
          <p>
            <span className="tip-label">
              {i18n.formatMessage({ id: 'page.account.setting.warehouse', defaultMessage: 'Warehouse' })}:
            </span>{' '}
            {record.name}
          </p>
          <p>
            <span className="tip-label">
              {i18n.formatMessage({ id: 'page.account.setting.date', defaultMessage: 'Date' })}:
            </span>{' '}
            {`${form.values?.period[0]} ~ ${form.values?.period[1]}`}
          </p>
        </div>
      ) : (
        <FormLayout layout="vertical" wrapperWidth={450} colon={false}>
          <Field
            name="period"
            title={i18n.formatMessage({ id: 'page.account.setting.holidayPeriod', defaultMessage: 'Holiday Period' })}
            required
            decorator={[FormItem]}
            component={[DatePicker.RangePicker, { disabledDate }]}
          />
        </FormLayout>
      )}

      <FormButtonGroup className="address-dialog-footer">
        <Button loading={form.submitting} onClick={onCancel}>
          {commonLocale['page.common.btn.cancel']}
        </Button>
        {hasDbChecked ? (
          <>
            <Button
              onClick={() => {
                setErrorMsg('');
                setHasDbChecked(false);
              }}
            >
              {commonLocale['page.common.btn.previous']}
            </Button>
            <Button
              type="primary"
              loading={form.submitting}
              onClick={() => {
                form.submit(onSubmit);
              }}
            >
              {commonLocale['page.common.btn.submit']}
            </Button>
          </>
        ) : (
          <Button type="primary" onClick={onDoubleCheck}>
            {commonLocale['page.common.btn.next']}
          </Button>
        )}
      </FormButtonGroup>
    </Form>
  );
};
