import * as i18n from '@alife/lazada-i18n';

/**
 * switch button 多语言
 */
export const verifyViaLoginEmailAddress = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginEmailAddress',
  defaultMessage: 'Verify via login email address',
});

export const verifyViaLoginPhoneAddress = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginPhoneAddress',
  defaultMessage: 'Verify via login phone address',
});

export const verifyViaLoginWhatsApp = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginWhatsApp',
  defaultMessage: 'Verify via login WhatsApp',
});

export const verifyViaLoginViber = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginViber',
  defaultMessage: 'Verify via login Viber',
});

export const verifyViaLoginZalo = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginZalo',
  defaultMessage: 'Verify via login Zalo',
});

export const verifyViaLoginPhoneWhatsApp = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginWhatsApp',
  defaultMessage: 'Verify via login phone WhatsApp',
});

export const verifyViaLoginPhoneSMS = i18n.formatMessage({
  id: 'page.account.setting.verifyViaLoginSMS',
  defaultMessage: 'Verify via login SMS',
});

/**
 * 当前登录账号多语言
 */
export const yourCurrentLoginPhone = i18n.formatMessage({
  id: 'page.account.setting.yourCurrentLoginPhone',
  defaultMessage: 'Your current login phone',
});

export const yourCurrentLoginEmail = i18n.formatMessage({
  id: 'page.account.setting.yourCurrentLoginEmail',
  defaultMessage: 'Your current login email',
});

/**
 * 输入验证码标题多语言
 */
export const verificationCodeEmail = i18n.formatMessage({
  id: 'page.account.setting.verificationCodeEmail',
  defaultMessage: 'Verification Code in Email',
});

export const verificationCodeSMS = i18n.formatMessage({
  id: 'page.account.setting.verificationCodeSMS',
  defaultMessage: 'Verification Code in SMS',
});

export const verificationCodeWhatsApp = i18n.formatMessage({
  id: 'page.account.setting.verificationCodeWhatsApp',
  defaultMessage: 'Verification Code in WhatsApp',
});

export const verificationCodeViber = i18n.formatMessage({
  id: 'page.account.setting.verificationCodeViber',
  defaultMessage: 'Verification Code in Viber',
});

export const verificationCodeZalo = i18n.formatMessage({
  id: 'page.account.setting.verificationCodeZalo',
  defaultMessage: 'Verification Code in Zalo',
});
