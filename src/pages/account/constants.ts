import * as locale from './locale';

export interface SwitchChannelInfo {
  /** 请求验证码 */
  requestUrl: string;
  /** 渠道类型 */
  deliveryType: EnumOtpChannel;
  /** 当前登录信息多语言 */
  currentLocale: string;
  /** 验证码标题多语言 */
  titleLocale: string;
  /** 验证验证码 */
  checkUrl: string;
}
export type SwitchChannelMap = Record<EnumOtpType, SwitchChannelInfo>;

/**
 * 验证渠道
 */
export enum EnumOtpChannel {
  SMS = 'SMS',
  ZALO = 'ZALO',
  WHATSAPP = 'WHATSAPP',
  VIBER = 'VIBER',
  EMAIL = 'EMAIL',
}

/**
 * 账号信息更改方式
 */
export enum EnumOtpType {
  PHONE = 'phone',
  EMAIL = 'email',
  PASSWORD = 'password',
  VIBER = 'viber',
  WHATSAPP = 'whatsapp',
  ZALO = 'zalo',
}

/**
 * 不同国家对应的可验证渠道
 */
export const CONST_OTP_TYPES = {
  SG: [EnumOtpChannel.SMS],
  MY: [EnumOtpChannel.SMS],
  TH: [EnumOtpChannel.SMS],
  VN: [EnumOtpChannel.SMS, EnumOtpChannel.ZALO],
  ID: [EnumOtpChannel.WHATSAPP, EnumOtpChannel.SMS],
  PH: [EnumOtpChannel.SMS],
};

export const CONST_OTP_TYPE_SHOW = {
  [EnumOtpType.PHONE]: EnumOtpType.PHONE,
  [EnumOtpType.EMAIL]: EnumOtpType.EMAIL,
  [EnumOtpType.WHATSAPP]: EnumOtpType.PHONE,
  [EnumOtpType.PASSWORD]: EnumOtpType.PHONE,
  [EnumOtpType.ZALO]: EnumOtpType.PHONE,
  [EnumOtpType.VIBER]: EnumOtpType.PHONE,
};

export const CONST_SWITCH_BUTTON = {
  [EnumOtpChannel.SMS]: {
    otpType: EnumOtpType.PHONE,
    switchLocale: locale.verifyViaLoginPhoneSMS,
  },
  [EnumOtpChannel.EMAIL]: {
    otpType: EnumOtpType.EMAIL,
    switchLocale: locale.verifyViaLoginEmailAddress,
  },
  [EnumOtpChannel.WHATSAPP]: {
    otpType: EnumOtpType.WHATSAPP,
    switchLocale: locale.verifyViaLoginPhoneWhatsApp,
  },
  [EnumOtpChannel.ZALO]: {
    otpType: EnumOtpType.ZALO,
    switchLocale: locale.verifyViaLoginZalo,
  },
  [EnumOtpChannel.VIBER]: {
    otpType: EnumOtpType.VIBER,
    switchLocale: locale.verifyViaLoginViber,
  },
};

export const CONST_SWITCH_INFO: SwitchChannelMap = {
  [EnumOtpType.PHONE]: {
    requestUrl: 'mtop.global.seller.verificationcode.sendsms',
    checkUrl: 'mtop.global.seller.identification.verifyidentificationotpcode',
    deliveryType: EnumOtpChannel.SMS,
    currentLocale: locale.yourCurrentLoginPhone,
    titleLocale: locale.verificationCodeSMS,
  },
  [EnumOtpType.PASSWORD]: {
    requestUrl: 'mtop.global.seller.verificationcode.sendsms',
    checkUrl: 'mtop.global.seller.identification.verifyidentificationotpcode',
    deliveryType: EnumOtpChannel.SMS,
    currentLocale: locale.yourCurrentLoginPhone,
    titleLocale: locale.verificationCodeSMS,
  },
  [EnumOtpType.EMAIL]: {
    requestUrl: 'mtop.global.seller.verificationCode.sendEmail',
    checkUrl: 'mtop.global.seller.identification.verifyIdentificationEmailCode',
    deliveryType: EnumOtpChannel.EMAIL,
    currentLocale: locale.yourCurrentLoginEmail,
    titleLocale: locale.verificationCodeEmail,
  },
  [EnumOtpType.WHATSAPP]: {
    requestUrl: 'mtop.global.seller.verificationcode.sendsms',
    checkUrl: 'mtop.global.seller.identification.verifyidentificationotpcode',
    deliveryType: EnumOtpChannel.WHATSAPP,
    currentLocale: locale.yourCurrentLoginPhone,
    titleLocale: locale.verificationCodeWhatsApp,
  },
  [EnumOtpType.ZALO]: {
    requestUrl: 'mtop.global.seller.verificationcode.sendsms',
    checkUrl: 'mtop.global.seller.identification.verifyidentificationotpcode',
    deliveryType: EnumOtpChannel.ZALO,
    currentLocale: locale.yourCurrentLoginPhone,
    titleLocale: locale.verifyViaLoginZalo,
  },
  [EnumOtpType.VIBER]: {
    requestUrl: 'mtop.global.seller.verificationcode.sendsms',
    checkUrl: 'mtop.global.seller.identification.verifyidentificationotpcode',
    deliveryType: EnumOtpChannel.VIBER,
    currentLocale: locale.yourCurrentLoginPhone,
    titleLocale: locale.verificationCodeViber,
  },
};

export const CONST_PHONE_PREFIX = {
  ID: '62',
  MY: '60',
  PH: '63',
  SG: '65',
  VN: '84',
  TH: '66',
};
