import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React from 'react';
import { axios, getUmidToken } from '@alife/workstation-utils';
import { PageContainer } from '@alife/asc-components';
import MRender from '../../components/mrender';
import SettingPageTitle from '../../components/setting-page-title';

import './page.scss';

declare global {
  interface Window {
    dadaConfig: any;
    FB: any;
    fbAsyncInit: any;
    fyModule: any;
  }
}

getUmidToken();
function ResultPage() {
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.accountSetting']} />
      <MRender
        pannelKey="account_panel"
        services={{
          render: () => {
            return axios.get('mtop.global.seller.member.getUserInfo').then(res => {
              const data = res.data;
              const { phonePrefixCode, email, phone, userId } = data;

              if (!res?.data) {
                throw new Error(
                  i18n.formatMessage({
                    id: 'page.account.setting.getAccountInfoError',
                    defaultMessage: 'Get Account Info Error',
                  }),
                );
              }

              res.data.data = {
                email: email,
                nationCode: '+' + phonePrefixCode,
                phone: phone,
                userId: userId,
              };

              res.data.data.fields = [
                {
                  label: i18n.formatMessage({
                    id: 'page.account.setting.loginEmailAddress',
                    defaultMessage: 'Login Email address',
                  }),

                  fieldName: 'email',
                  name: 'email',
                  require: true,
                  uiType: 'LoginContactText',
                  value: res.data?.email,
                  tips: '',
                  disabled: false,
                  needOtp: false,
                  history: false,
                },

                {
                  label: i18n.formatMessage({
                    id: 'page.account.setting.loginPhoneNumber',
                    defaultMessage: 'Login Phone Number',
                  }),

                  fieldName: 'phone',
                  name: 'phone',
                  require: true,
                  uiType: 'LoginContactText',
                  prefix: phonePrefixCode ? '+' + phonePrefixCode : '',
                  value: res.data?.phone,
                  tips: '',
                  disabled: false,
                  needOtp: false,
                  history: false,
                },

                {
                  label: i18n.formatMessage({ id: 'page.account.setting.password', defaultMessage: 'Password' }),
                  fieldName: 'password',
                  name: '4',
                  require: true,
                  uiType: 'PasswordText',
                  value: '',
                  tips: i18n.formatMessage({
                    id: 'page.account.setting.passwordNeedsAtLeastCharacters',
                    defaultMessage: 'Password needs at least 8 characters, sincluding 1 letter, 1 number and 1 symbol',
                  }),
                  disabled: false,
                  needOtp: false,
                  history: false,
                  hasPassword: res.data?.hasPassword,
                },
              ];

              return res;
            });
          },
          // no submit  for this page
          submit: () => Promise.resolve(),
          // no precheck for this page
          precheck: () => Promise.resolve(),
        }}
      />
    </PageContainer>
  );
}

export default ResultPage;
