import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React, { useState } from 'react';
import { PageContainer } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import { Table, Card, Input, Box, Icon, Message, Button, Loading } from '@alifd/next';
import { useRequest } from 'ahooks';
import _ from 'lodash';
import SettingPageTitle from '../../components/setting-page-title';
import './page.scss';

const resolveListData = (list = []) =>
  list.map((item: any = {}) => {
    item.children = [{ categoryId: Math.ceil(Math.random() * 10000000) }];
    return item;
  });

function ResultPage() {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openRowKeys, setOpenRowKeys] = useState<string[]>([]);
  const [keyword, setKeyword] = useState('');
  const { run, loading: loadingList } = useRequest(
    keyword =>
      axios
        .get('mtop.lazada.finance.sellerfund.seller.commission.query', {
          data: {
            keyword,
          },
        })
        .then(res => {
          setOpenRowKeys([]);
          return resolveListData(res?.data?.data);
        })
        .catch(err => Message.error(err?.message)),
    {
      throwOnError: true,
      onError: err => {
        console.log(err);
      },
      onSuccess: (data: any) => {
        setDataSource(data);
      },
    },
  );

  const getAsyncChildren = categoryId => {
    return axios
      .get('mtop.lazada.finance.sellerfund.seller.commission.query', {
        data: {
          // keyword: '',
          categoryId,
        },
      })
      .then(res => {
        if (res.data) {
          return resolveListData(res?.data.data);
        }
        throw new Error(res?.data?.msg);
      });
  };
  // function toTree(p, n: any) {
  //   return Object.assign({}, p, {
  //     [n.categoryId]: {
  //       ...n,
  //       children: Array.isArray(n.children) ? n.children.reduce(toTree, {}) : n.children,
  //     },
  //   });
  // }

  // function toArray(tree) {
  //   return Object.values(tree).map((value: any) => {
  //     return Object.assign(value, {
  //       children: value?.children ? toArray(value?.children) : value.children,
  //     });
  //   });
  // }
  // const setChildrenForRecord = (ork: string[], children) => {
  //   const newData = [...dataSource];

  //   const kv = newData.reduce(toTree, {});
  //   const paths = ork.join('.children.');
  //   const item = _.get(kv, paths);
  //   item.children = children.reduce(toTree, {});
  //   console.log(item, toArray(kv));
  //   // return newData;
  //   return toArray(kv);
  // };
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.commission']} />
      <Card data-spm="commission_pannel">
        <Box direction="row" spacing={12} margin={[0, 0, 16, 0]} justify="flex-end">
          <Input
            data-spm="d_search_input"
            className="aplus-auto-exp"
            // hasClear
            style={{ width: 300 }}
            placeholder={i18n.formatMessage({ id: 'page.commission.keyword', defaultMessage: 'Category Name' })}
            onChange={setKeyword}
            onPressEnter={() => run(keyword)}
            value={keyword}
          />
          <Button
            type="secondary"
            onClick={() => {
              setKeyword('');
              run(keyword);
            }}
            className="aplus-auto-exp"
            data-spm="d_reset"
          >
            {i18n.formatMessage({
              id: 'page.common.btn.reset',
              defaultMessage: 'Reset',
            })}
          </Button>
          <Button className="aplus-auto-exp" data-spm="d_search" type="primary" onClick={() => run(keyword)}>
            {i18n.formatMessage({
              id: 'page.common.btn.search',
              defaultMessage: 'Search',
            })}
          </Button>
        </Box>
        <Loading visible={loadingList} style={{ width: '100%' }}>
          <Table
            rowProps={(_, index) => ({
              'data-spm': `tablerow_${index}`,
            })}
            dataSource={dataSource}
            openRowKeys={openRowKeys}
            onRowOpen={(ork: string[], currentRowKey, expanded, currentRecord) => {
              const getOldKeys = kk => kk.filter(cu => cu !== currentRowKey);
              if (expanded) {
                setLoading(true);
                getAsyncChildren(currentRowKey)
                  .then(res => {
                    // if success, open it and show childrens

                    // const newData = setChildrenForRecord(ork, res);
                    currentRecord.children = res;
                    setDataSource(dataSource);
                    // debugger;
                    setOpenRowKeys(ork);
                  })
                  .catch(() => {
                    // if not success, close it
                    setOpenRowKeys(getOldKeys(ork));
                  })
                  .then(() => {
                    setLoading(false);
                  });
              } else {
                setOpenRowKeys(getOldKeys(ork));
              }
            }}
            primaryKey="categoryId"
            isTree
            loading={loading}
          >
            <Table.Column
              title={i18n.formatMessage({ id: 'page.commission.level', defaultMessage: 'Level' })}
              dataIndex="level"
            />

            <Table.Column
              title={i18n.formatMessage({ id: 'page.commission.categoryName', defaultMessage: 'Category Name' })}
              dataIndex="categoryName"
            />

            <Table.Column
              title={i18n.formatMessage({ id: 'page.commission.commission', defaultMessage: 'Commission(%)' })}
              dataIndex="resultingCommission"
            />
          </Table>
        </Loading>
      </Card>
    </PageContainer>
  );
}

export default ResultPage;
