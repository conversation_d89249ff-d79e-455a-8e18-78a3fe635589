.manage-image-page {
  .next-form.next-inline .next-form-item:not(:last-child) {
    margin-right: 12px;
  }
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.mb15 {
  margin-bottom: 15px;
}

.config-list {
  // display: flex;
  // flex-flow: wrap;
  // margin-right: -16px;
}

.config-item {
  display: flex;
  height: 96px;
  // width: 347px;
  background-color: #f9f9f9;
  border-radius: 12px;
  box-sizing: border-box;
  padding: 16px 8px;
  margin-bottom: 16px;

  &:hover {
    // background: rgba($color: #595f6d, $alpha: 0.1);
    box-shadow: 2px 4px 10px 0 rgba(0, 0, 0, 0.12);
    background-color: #eeeff0;
  }

  &.disable:hover {
    box-shadow: none;
    background-color: #f9f9f9;
    cursor: not-allowed;
  }

  &.disable {
    .config-item-icon {
      opacity: 0.5;
    }

    .config-item-info {
      opacity: 0.5;
    }
  }

  &-icon {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  &-desc {
    color: #858b9c;
    font-size: 12px;
    height: 40px;
    line-height: 20px;
    overflow: hidden;
  }
}

// @media screen and (max-width: 1440px) {
//   .config-item {
//     width: 314px;
//   }
// }

.edit-shopname {
}

.profile-header {
  display: flex;
  border-bottom: 1px solid #efefef;
  padding-bottom: 16px;
  margin-bottom: 16px;

  &-avatar {
    width: 80px;
    height: 80px;
    border-radius: 40px;
    overflow: hidden;
    border: 1px solid #efefef;
    margin-right: 16px;
  }

  &-avatarimg {
    height: 100%;
    width: 100%;
  }

  &-id.copy-item {
    color: #858b9c;
    margin-bottom: 5px;
    display: block;
  }

  &-bind {
    .description-item-scope {
      font-size: var(--form-font-size, 16px);
      line-height: 24px;
      align-items: center;
      margin-top: -5px;
      .description-item-label {
        font-weight: 400;
        font-family: Roboto, Helvetica Neue, Helvetica, Tahoma, Arial, PingFang SC, Microsoft YaHei;
        color: #858b9c;
      }
      .description-item-content {
        cursor: pointer;
        font-family: Roboto-Medium;
        font-size: 14px;
        color: #1a71ff;
        align-items: center;
      }
    }
    .share {
      margin-left: 12px;
      img {
        width: 24px;
        height: 24px;
      }
    }
  }
}

.skeleton-item {
  width: 24%;
  box-sizing: border-box;
  padding: 0px 8px;
  margin-bottom: 16px;

  .skeleton {
    border-radius: 12px;
  }
}
