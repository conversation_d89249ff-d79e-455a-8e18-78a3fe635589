import * as i18n from '@alife/lazada-i18n';

import { ASCTools } from '@alife/workstation-utils';
import commonLocale from '../../constants/common-locale';
import { ConfigGroup } from './models';

const currentBU = ASCTools.getBU();

export default [
  new ConfigGroup({
    title: commonLocale['page.common.nav.businessInformation'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i3/O1CN01uo9PFN1gfuK8qQYGH_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.sellerProfile'],
        url: '/apps/setting/seller',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.sellerProfile',
          defaultMessage: 'Description For Seller Profile',
        }),
        key: 'seller',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01oyKjYW29O9pahfhQy_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.businessInformation'],
        url: '/apps/setting/business',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.businessInformation',
          defaultMessage: 'Description For Business Information',
        }),
        key: 'business',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN011VF78A1I9LO0Rut9Z_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.brand'],
        url: currentBU === 'DARAZ' ? '/portal/apps/setting/brand_v2' : '/apps/setting/brand',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.brand',
          defaultMessage: 'Description For Brand',
        }),
        key: 'brand',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i3/O1CN01zhmLC61vMs69yzU6N_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.accountSetting'],
        url: '/apps/setting/account',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.accountSetting',
          defaultMessage: 'Description For Login Account',
        }),
        key: 'account',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i3/O1CN01zhmLC61vMs69yzU6N_!!*************-55-tps-60-60.svg',
        name: 'Laz Go Global',
        desc: 'Laz Go Global Description',
        url: '/apps/gsp/lazgo',
        key: 'lazgoGlobal',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01uHhvg81W6Va4vumRU_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.account.experience.businessExperience'],
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.businessExperience',
          defaultMessage: 'Basic account setting including Email, phone number and password',
        }),
        url: '/apps/setting/experience',
        key: 'experience',
      },
    ],
  }),
  new ConfigGroup({
    title: commonLocale['page.common.nav.finance'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01sMPMUe1kldkFl0khQ_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.bankAccount'],
        url: '/apps/setting/bank',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.bankAccount',
          defaultMessage: 'Description For Bank',
        }),
        key: 'bank',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN010YYh5x1cJge0HbBhZ_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.commission'],
        url: '/apps/setting/commission',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.commission',
          defaultMessage: 'Description For Commission',
        }),
        key: 'commission',
      },
    ],
  }),
  new ConfigGroup({
    title: commonLocale['page.common.nav.logistic'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01WouGND1aXlaOajeoF_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.invoiceNumber'],
        url: '/apps/setting/invoice',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.invoiceNumber',
          defaultMessage: 'Description For Invoice Number',
        }),
        key: 'invoice',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01J2Akmc29GpUfPkXG9_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.warehouse'],
        url: '/apps/setting/warehouse_address',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.warehouse',
          defaultMessage: 'Description For Warehouse Address',
        }),
        key: 'warehouse_address',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN014wEd4V1UhwtJMUi3T_!!*************-55-tps-46-45.svg',
        name: commonLocale['page.common.nav.holiday'],
        url: '/apps/setting/holiday',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.holiday',
          defaultMessage: 'Description For Holiday Mode',
        }),
        key: 'holiday',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN01KDbBhB1FYaMnHEDq9_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.deliveryService'],
        url: '/apps/setting/delivery_service',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.deliveryService',
          defaultMessage: 'Description For Delivery Service',
        }),
        key: 'delivery_service',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01aW29aU21QQs8f424c_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.shippingProvider'],
        url: '/apps/setting/shipping_provider',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.shippingProvider',
          defaultMessage: 'Description For Shipping Provider',
        }),
        key: 'shipping_provider',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN01KDbBhB1FYaMnHEDq9_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.deliveryOptions'],
        url: '/apps/setting/delivery_options',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.deliveryOptions',
          defaultMessage: 'Your available Delivery options',
        }),
        key: 'delivery_options',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01yKBiiu1SfXOee0D4I_!!*************-55-tps-40-40.svg',
        name: commonLocale['page.common.nav.shipmentProvider'],
        url: '/apps/setting/shipment_provider',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.shipmentProvider',
          defaultMessage: 'Your available transferring warehouses',
        }),
        key: 'shipment_provider',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01SnlcTG28INDELqtYS_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.operatingHours'],
        url: '/apps/setting/operating_hours',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.operatingHours',
          defaultMessage: 'Description For Operating Hours',
        }),
        key: 'operating_hours',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01xxSQkz1zH9cKIa1RG_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.storeList'],
        url: '/apps/coupon/store_list?from=store_pickup',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.storeList',
          defaultMessage: 'Description For Store List',
        }),
        key: 'store_list',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN01DLh37r21ymZvXFzCW_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.serviceArea'],
        url: '/apps/setting/service_area',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.serviceArea',
          defaultMessage: 'Description For Service Area',
        }),
        key: 'service_area',
      },
    ],
  }),

  new ConfigGroup({
    title: commonLocale['page.common.nav.userManagement'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01MIyJbD1yNjgwBlq7y_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.manageUsers'],
        url: '/apps/setting/member/list?tab=sub_account',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.manageUsers',
          defaultMessage: 'Description For Login Manage Users',
        }),
        key: 'manage_users',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i4/O1CN01Ulg3BN1nzmpGrarHi_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.manageRoles'],
        url: '/apps/setting/member/list?tab=roles',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.manageRoles',
          defaultMessage: 'Description For Login Manage Roles',
        }),
        key: 'manage_roles',
      },
    ],
  }),

  new ConfigGroup({
    title: commonLocale['page.common.nav.chat'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN01CcEdjr1poSG4ARa2A_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.quickReply'],
        url: '/apps/im/setting/quick?type=quickReply#quickReply',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.quickReply',
          defaultMessage: 'Description For Quick Reply',
        }),
        key: 'quick_reply',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i3/O1CN01c9gBDQ1oZy77p2vrn_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.autoReply'],
        url: '/apps/im/setting/auto?type=autoReply#autoReply',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.autoReply',
          defaultMessage: 'Description For Auto Reply',
        }),
        key: 'auto_reply',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i1/O1CN017kLpTr1uELDkFoNC7_!!*************-55-tps-44-44.svg',
        name: commonLocale['page.common.nav.chatNotification'],
        url: '/apps/im/setting/notification?type=notification#notification',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.chatNotification',
          defaultMessage: 'Description For Chat Notification',
        }),
        key: 'chat_notification',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01l7gpN41YGFCk4AQPt_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.chatKeywordTag'],
        url: '/apps/im/keywordTag',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.chatKeywordTag',
          defaultMessage: 'Description For Chat Keyword Tag',
        }),
        key: 'chat_keyword_tag',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN014So3WJ1NN9P0jlbwW_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.assistantManagement'],
        url: '/apps/im/setting?type=userManagement#userManagement',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.assistantManagement',
          defaultMessage: 'Description For Assistant Management',
        }),
        key: 'assistant_management',
      },
    ],
  }),
  new ConfigGroup({
    title: commonLocale['page.common.nav.system'],
    list: [
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01LvDhym27XevToOEhT_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.notification.settings'],
        url: '/portal/messages/notification',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.notification',
          defaultMessage: 'Receive notification according to your preferences',
        }),
        key: 'notification_setting',
      },
      {
        icon: 'https://img.alicdn.com/imgextra/i2/O1CN01LvDhym27XevToOEhT_!!*************-55-tps-60-60.svg',
        name: commonLocale['page.common.nav.model.settings'],
        url: '/portal/mode/setting',
        desc: i18n.formatMessage({
          id: 'page.account.setting.desc.model',
          defaultMessage: 'Switch between Normal Mode and Simplify Mode',
        }),
        key: 'mode_setting',
      },
    ],
  }),
];
