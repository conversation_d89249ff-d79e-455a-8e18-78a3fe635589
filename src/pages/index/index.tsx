import React from 'react';
import ReactDOM from 'react-dom';
import Page from './page';
import { BrowserRouter as Router, Route } from 'react-router-dom';
import 'uno.css';

export function mount({ container }) {
  ReactDOM.render(
    <Router>
      <Route>
        <Page />
      </Route>
    </Router>,
    container,
  );
}

export function unmount({ container }) {
  ReactDOM.unmountComponentAtNode(container);
}
