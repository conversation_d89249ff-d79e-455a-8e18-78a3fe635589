import { Vmo } from '@vmojs/decorator';

@Vmo()
export class Config {
  constructor(data: any) {}

  @Vmo()
  name: string;

  @Vmo()
  url: string;

  @Vmo()
  icon: string;

  @Vmo()
  desc: string;

  @Vmo()
  key: string;

  @Vmo()
  disable?: boolean;
}

@Vmo()
export class ConfigGroup {
  constructor(data: any) {}

  @Vmo()
  title: string;

  @Vmo(obj => obj.list.map(item => new Config(item)))
  list: Config[];
}

@Vmo()
export class Profile {
  constructor(data: any) {}

  @Vmo()
  storeName: string;

  @Vmo()
  sellerId: string;

  @Vmo(obj => (obj.storeUrl && obj.storeUrl.indexOf('//') === 0 ? `https:${obj.storeUrl}` : obj.storeUrl))
  storeUrl: string;

  @Vmo()
  shopLogo: string;

  @Vmo()
  shortCode: string;

  @Vmo()
  isCrossBorder: boolean;

  @Vmo()
  isLazMall: boolean;

  @Vmo()
  isMulitiAddressSeller: boolean;

  @Vmo()
  isEnableOperationHours: boolean;

  @Vmo()
  isEnableO2oStoreList: boolean;

  @Vmo()
  isWalletActive: boolean;

  @Vmo()
  canJoin: boolean;

  @Vmo()
  hasJoined: boolean;

  // 是否是 MP2.5 卖家
  @Vmo()
  isSimplifyModeSeller: boolean;

  // 是否被圈选中的卖家（打标，可以看到 Mode Settings 入口）
  @Vmo()
  isSimplifyModeWhiteListSeller: boolean;

  // 是否是 MP3 卖家
  @Vmo()
  isSupplyModeSeller: boolean;
}
