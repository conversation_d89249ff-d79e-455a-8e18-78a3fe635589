import { Box, Card, Icon, Message, Typography } from '@alifd/next';
import {
  CopyText,
  Description,
  Image,
  PageContainer,
  Skeleton,
  createIndexDBSWRPlugin,
  useOsc,
} from '@alife/asc-components';
import * as i18n from '@alife/lazada-i18n';
import { ASCTools, axios, getScript } from '@alife/workstation-utils';
import { useMount, useRequest } from 'ahooks';
import classnames from 'classnames';
import React, { useEffect, useState } from 'react';
import SettingPageTitle from '../../components/setting-page-title';
import { useFacebookBindContent } from '../../hooks/bind-facebook-account';
import { fetchSellerTag } from '../../services/rc';
import { AccountStrength } from './account-strength';
import configs from './configs';
import { Config, ConfigGroup, Profile } from './models';
import './page.scss';
declare global {
  interface Window {
    dadaConfig: any;
    FB: any;
    fbAsyncInit: any;
  }
}
const FB_APP_ID = location.host.includes('-staging') ? '****************' : '***************';
const SELLER_ID = window.dadaConfig?.sellerId;
const currentBU = ASCTools.getBU();
const IS_DARAZ_HOST = currentBU === 'DARAZ';

const getProfile = () => {
  return axios.get('mtop.global.merchant.subaccount.otp.userinfo').then(res => {
    return new Profile(res?.data?.data);
  });
};

/**
 * 查询是否开通GoGlobal
 * @returns
 */
export const queryJoinGoGlobal = async (): Promise<{
  canJoin: boolean;
  hasJoined: boolean;
}> => {
  return axios({
    url: 'mtop.lazada.onboard.cb.registration.getJoinGoGlobal',
  })
    .then(res => res.data.data)
    .catch(e => {
      if (currentBU !== 'DARAZ') {
        Message.error(e.message || e.ret?.[0]);
      }
    });
};
export const brandShow = async () => {
  return axios
    .get('mtop.lazada.merchant.onboard.brand.show')
    .then(res => res?.data?.data?.showBrand || false)
    .catch();
};

export const checkBind = async () => {
  return axios
    .get('mtop.lazada.merchant.social.media.facebook.status.get')
    .then(res => res.data.data)
    .catch(e => Promise.resolve({ bind: true }));
};

const renderConfigItem = (configGroup: ConfigGroup, loading: boolean) => {
  return (
    <div data-spm="configs">
      <Typography.H4>{configGroup.title}</Typography.H4>
      {loading ? (
        <Box direction="row" wrap>
          {configGroup.list?.map(() => (
            <div className={classnames('skeleton-item')}>
              <Skeleton className="skeleton" width="100%" height={96} />
            </div>
          ))}
        </Box>
      ) : (
        <div className="config-list grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3  2xl:grid-cols-4">
          {configGroup.list?.map(item => {
            if (item.disable) {
              return null;
            }
            return (
              <a
                data-spm={`d_${item.key}`}
                className={classnames('config-item', 'aplus-auto-exp', { disable: item.disable })}
                href={item.disable ? 'javascript:;' : item.url}
              >
                <img className="config-item-icon" src={item.icon} />
                <div className="config-item-info">
                  <Typography.H5>{item.name}</Typography.H5>
                  <p className="config-item-desc">{item.desc}</p>
                </div>
              </a>
            );
          })}
        </div>
      )}
    </div>
  );
};

const renderHeader = (profile: Profile, bindStatus: any, loading?: boolean) => {
  if (!loading && (!profile || !bindStatus)) {
    return null;
  }
  const [status, setBindStatus] = useState(bindStatus);
  useEffect(() => {
    setBindStatus(bindStatus);
  }, [bindStatus]);
  useMount(() => {
    getScript('https://connect.facebook.com/en_US/sdk.js')
      .then(() => {
        window.fbAsyncInit = function() {
          window.FB.init({
            appId: FB_APP_ID,
            cookie: true,
            xfbml: true,
            version: 'v14.0',
          });
        };
      })
      .catch(e => {
        console.info('facebook sdk loaded failed', e);
      });
  });
  const [FacebookDialog, { onFacebookBind }] = useFacebookBindContent({
    onCancel: async () => {
      const bind = await checkBind();
      setBindStatus(bind);
    },
  });
  const { data } = useOsc('186001', {
    cacheLocalStorage: true,
    defaultValue: {
      showButton: false,
    },
  });
  if (loading) {
    return (
      <div data-spm="profile_header" className="profile-header">
        <div className="profile-header-avatar">
          <Skeleton width="100%" height="100%" className="profile-header-avatarimg" />
        </div>
        <div className="profile-header-desc">
          <Skeleton style={{ width: 160, height: 25, display: 'block', marginBottom: 8 }} />
          <Skeleton
            style={{ width: 160, height: 18, display: 'block', marginBottom: 8 }}
            className="profile-header-id"
          />
          <Skeleton style={{ width: 550, height: 18, display: 'block' }} className="profile-header-id" />
          <Skeleton style={{ width: 550, height: 18, display: 'block' }} className="profile-header-bind" />
        </div>
      </div>
    );
  }
  return (
    <div data-spm="profile_header" className="profile-header">
      <div className="profile-header-avatar">
        <Image
          mode="aspectFill"
          width={80}
          height={80}
          cdnOptimize={true}
          src={profile?.shopLogo}
          className="profile-header-avatarimg"
        />
      </div>
      <div className="profile-header-desc">
        <Typography.H4>
          {profile?.storeName}{' '}
          <a data-spm="d_edit_shopname" href="/site/profile/infoSettings" target="_blank" className="aplus-auto-exp">
            <Icon type="edit" size="xs" className="ml5" />
          </a>
        </Typography.H4>
        <CopyText
          data-spm="d_seller_id"
          className="copy-text-item profile-header-id aplus-auto-exp"
          value={profile?.shortCode}
        >
          {i18n.formatMessage({ id: 'page.account.setting.sellerId', defaultMessage: 'Seller ID' })}:{' '}
          {profile?.shortCode}
        </CopyText>
        <br />
        <CopyText
          data-spm="d_store_url"
          className="copy-text-item profile-header-id aplus-auto-exp"
          value={profile?.storeUrl}
        >
          {i18n.formatMessage({ id: 'page.account.setting.storeUrl', defaultMessage: 'Store URL' })}:{' '}
          {profile?.storeUrl}
        </CopyText>
        {data?.showButton && (
          <Description className="profile-header-bind">
            <Description.Item
              label={i18n.formatMessage({ id: 'page.common.nav.accountBinding', defaultMessage: 'Facebook' })}
              data-spm="d_fb_account"
              className="profile-header-id aplus-auto-exp"
            >
              <span className="content" onClick={() => onFacebookBind({ bindStatus: status })}>
                {status?.bind
                  ? i18n.formatMessage({
                      id: 'page.account.setting.viewNow',
                      defaultMessage: 'View Now',
                    })
                  : i18n.formatMessage({
                      id: 'page.account.setting.bindNow',
                      defaultMessage: 'Bind Facebook Account',
                    })}
              </span>
              <span className="share">
                <img
                  src="https://img.alicdn.com/imgextra/i3/O1CN01L7gduw23g7hkD7ce4_!!*************-2-tps-168-168.png"
                  alt=""
                />
              </span>
            </Description.Item>
          </Description>
        )}
      </div>
      <FacebookDialog />
    </div>
  );
};

const strategies = {
  // brand: (item: Config, profile: Profile): Config => {
  // MP3 卖家不展示该入口
  // if (profile?.isSupplyModeSeller) {
  //   item.disable = true;
  // } else {
  //   item.disable = false;
  // }
  //   return item;
  // },
  commission: (item: Config, profile: Profile): Config => {
    // MP3 卖家不展示该入口
    if (profile?.isSupplyModeSeller) {
      item.disable = true;
    } else {
      item.disable = false;
    }
    return item;
  },
  store_list: (item: Config, profile: Profile): Config => {
    item.disable = !profile.isEnableO2oStoreList;
    return item;
  },
  operating_hours: (item: Config, profile: Profile): Config => {
    item.disable = profile.isCrossBorder || !profile.isEnableOperationHours || IS_DARAZ_HOST;
    return item;
  },
  service_area: (item: Config, profile: Profile): Config => {
    item.disable = !profile.isMulitiAddressSeller;
    return item;
  },

  shipping_provider: (item: Config, profile: Profile, is3PF = false): Config => {
    if (currentBU === 'DARAZ') {
    } else {
      item.disable = profile.isCrossBorder && !is3PF;
    }
    return item;
  },

  delivery_service: (item: Config, profile: Profile): Config => {
    item.disable = profile.isCrossBorder;
    return item;
  },

  shipment_provider: (item: Config, profile: Profile): Config => {
    if (currentBU === 'DARAZ') {
      item.disable = true;
    } else {
      item.disable = !profile.isCrossBorder;
    }
    return item;
  },

  delivery_options: (item: Config, profile: Profile): Config => {
    item.disable = !profile.isCrossBorder;
    return item;
  },

  assistant_management: (item: Config, profile: Profile): Config => {
    item.disable = IS_DARAZ_HOST;
    return item;
  },

  experience: (item: Config, profile: Profile): Config => {
    item.disable = IS_DARAZ_HOST;
    return item;
  },

  bank: (item: Config, profile: Profile): Config => {
    if (profile.isWalletActive) {
      item.url = '/apps/wallet/card/management';
    }
    return item;
  },

  lazgoGlobal: (item: Config, profile: Profile): Config => {
    // MP3 卖家不展示该入口
    if (profile?.isSupplyModeSeller) {
      item.disable = true;
    } else if (!profile.canJoin) {
      item.disable = true;
    } else if (profile.hasJoined) {
      item.url = '/apps/gsp/asean/index';
      item.disable = false;
    } else {
      item.disable = false;
      item.url = '/apps/gsp/lazgo';
    }
    return item;
  },

  mode_setting: (item: Config, profile: Profile): Config => {
    // MP2.5 商家标，有了这个标之后才展示商家工作台版本切换入口
    if (profile?.isSimplifyModeWhiteListSeller && !profile.isSupplyModeSeller) {
      item.disable = false;
    } else {
      item.disable = true;
    }

    if (IS_DARAZ_HOST) {
      item.disable = true;
    }

    return item;
  },
};
const configResolveByProfile = (item: Config, profile: Profile, is3PF = false): Config => {
  if (strategies[item.key]) {
    return strategies[item.key](item, profile, is3PF);
  }
  return item;
};

function ResultPage() {
  const { data, loading } = useRequest(
    () => {
      return Promise.all([
        queryJoinGoGlobal(),
        getProfile(),
        checkBind(),
        brandShow(),
        fetchSellerTag('3PF_whitelist'),
      ]).then(([joinResult, profile, bindStatus, brandShow, is3PF]) => {
        console.log('sellerTag', is3PF);
        const newConfigs: ConfigGroup[] = [];
        // 查询接口是否展示brand入口
        const filterConfigs = configs.map(i => ({
          ...i,
          list: i.list.filter(j => !(j.key === 'brand' && !brandShow)),
        }));
        for (const group of filterConfigs) {
          const tempGroup: ConfigGroup = { title: group.title, list: [] };
          for (const item of group.list) {
            tempGroup.list.push({
              ...configResolveByProfile(
                item,
                {
                  ...profile,
                  ...joinResult,
                },
                is3PF,
              ),
            });
          }
          newConfigs.push(tempGroup);
        }
        return {
          profile,
          newConfigs,
          bindStatus,
        };
      });
    },
    {},
    [
      createIndexDBSWRPlugin({
        getCacheKey: () => `seller-profile-${SELLER_ID}`,
      }),
    ],
  );

  let finalConfigs = data ? data.newConfigs : configs;

  if (currentBU === 'DARAZ') {
    let darazChildParamsTable = {
      experience: {
        disable: true,
      },
      holiday: {
        disable: true,
      },
      operating_hours: {
        disable: true,
      },
    };
    let newFatherArr = [];

    finalConfigs.forEach(child => {
      let newChildArr = [];

      child.list.forEach(i => {
        if (i.key in darazChildParamsTable) {
          i = { ...i, ...darazChildParamsTable[i.key] };
        }
        newChildArr.push(i);
      });

      newFatherArr.push({
        title: child.title,
        list: newChildArr,
      });
    });

    finalConfigs = newFatherArr;
  }

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle />
      <Card>
        {renderHeader(data?.profile, data?.bindStatus, loading)}
        {!IS_DARAZ_HOST && <AccountStrength sellerId={SELLER_ID} />}
        {finalConfigs.map((item: ConfigGroup) => renderConfigItem(item, loading))}
      </Card>
    </PageContainer>
  );
}

export default ResultPage;
