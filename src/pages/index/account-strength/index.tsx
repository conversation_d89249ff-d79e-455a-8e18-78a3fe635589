import React from 'react';
import { Typography } from '@alifd/next';
import { HelpTip, createIndexDBSWRPlugin } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import { useRequest } from 'ahooks';
import { InstrumentPanel } from '../../../components/gauge-panel/instrument-panel';
import commonLocale from '../../../constants/common-locale';
import './index.scss';

interface IStrengthProps {
  sellerId?: string;
}
interface IStrength {
  profileStrength: string;
  taskList: Array<{
    isBusinessExperience: boolean;
    taskCode: string;
    taskLink: string;
    taskName: string;
    taskPriority: number;
    taskStatus: string;
    visible: boolean;
  }>;
  warningMsg: string;
}
const toNum = percent => {
  if (!percent) return 0;
  const num = percent.replace('%', '') / 100;
  return num;
};

const clickItem = item => {
  location.href = item.taskLink;
};

const getStatus = () => {
  return axios({
    url: 'mtop.global.merchant.subaccount.seller.task.status',
    params: {
      onlyIncludeBusinessExperience: false,
    },
  })
    .then(({ data }) => data?.data as IStrength)
    .catch(e => {
      console.error(e);
      return null;
    });
};

export const AccountStrength = (props: IStrengthProps) => {
  const { data } = useRequest(getStatus, {}, [
    createIndexDBSWRPlugin({
      getCacheKey: () => `profile-strength-${props.sellerId}`,
    }),
  ]);

  if (!data) {
    return null;
  }

  const { profileStrength, taskList, warningMsg } = data;
  return (
    <div className="account-strength" data-spm="profile_strength">
      <div className="strength-gauge">
        <Typography.H4>
          <HelpTip
            help={<span dangerouslySetInnerHTML={{ __html: commonLocale['page.account.setting.strengthTips'] }}></span>}
          >
            {commonLocale['page.account.setting.profileStrength']}
          </HelpTip>
        </Typography.H4>
        <InstrumentPanel data={toNum(profileStrength)} />
      </div>
      <div className="todo-tasks">
        <Typography.H4>{commonLocale['page.account.setting.completeTask']}</Typography.H4>
        <div className="tip-tag">{warningMsg}</div>
        <div className="list grid gap-2 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 min-[1920px]:grid-cols-6">
          {taskList &&
            taskList
              .filter(item => item.taskStatus !== 'done')
              .map((item, index) => {
                return (
                  <div
                    className="button aplus-auto-exp"
                    data-spm={`d_${item?.taskCode?.toLowerCase()}_clk`}
                    key={index}
                    onClick={() => clickItem(item)}
                  >
                    {item?.taskCode === 'TIKTOK_SHOP' && (
                      <img
                        className="icon"
                        src="https://img.alicdn.com/imgextra/i3/O1CN01Fi94EJ1glPEzk5bMd_!!*************-55-tps-36-19.svg"
                      />
                    )}
                    <span className="text">{item?.taskName}</span>
                    <span className="add-icon"></span>
                  </div>
                );
              })}
        </div>
      </div>
    </div>
  );
};
