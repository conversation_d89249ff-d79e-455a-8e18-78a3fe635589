.account-strength {
  display: flex;
  flex-direction: row;
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: 1px solid #efefef;

  .strength-gauge {
    flex-grow: 1;
    .bizcharts {
      margin: 0 auto;
    }
  }
  .todo-tasks {
    width: 60%;
    flex-grow: 1;
    .tip-tag {
      display: inline-block;
      line-height: 16px;

      font-size: 14px;
      background-color: rgba(#1a71ff, 0.1);
      padding: 4px 12px;
      border-radius: 12px;
      color: #1a71ff;
      margin-bottom: 16px;
      color: #1a71ff;
      font-weight: 400;
      .span {
        font-family: Roboto-Regular;
        font-size: 14px;
      }
    }
    .list {
      // display: flex;
      // flex-wrap: wrap;
      .button {
        position: relative;
        height: 48px;
        line-height: 18px;
        display: flex;
        // width: 192px;
        // flex: 0 1 192px;
        flex-direction: row;
        background-color: #f8f9fd;
        border-radius: 6px;
        margin-bottom: 8px;
        padding: 0 9px;
        cursor: pointer;
        align-items: center;
        &:hover {
          box-shadow: 2px 4px 10px 0 rgb(0 0 0 / 12%);
          background-color: #eeeff0;
        }
        .text {
          flex: 1;
          font-weight: 400;
          font-size: 14px;
          color: #8c8f9d;
        }
        .add-icon {
          width: 16px;
          height: 16px;
          display: block;
          margin: 16px auto;
          background-image: url('https://img.alicdn.com/imgextra/i2/O1CN01OZQ6FE1axsBzZDsYY_!!6000000003397-55-tps-200-200.svg');
          background-size: 18px 18px;
          background-position: 0 0;
          background-repeat: no-repeat;
        }

        .icon {
          position: absolute;
          top: 0px;
          left: 0px;
        }
      }
    }
  }
}
