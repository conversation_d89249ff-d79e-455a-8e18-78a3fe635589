import { Vmo } from '@vmojs/decorator';

@Vmo()
export class ShippingResult {
  constructor(data: any) {}

  @Vmo()
  warehouseCode: string;

  @Vmo()
  warehouseName: string;

  @Vmo()
  shippingProviderName: string;

  @Vmo()
  solutionCode: string;

  @Vmo()
  deliveryOption: string;

  @Vmo()
  spillShippingProviderName: string;

  @Vmo()
  pickUpType: string;

  @Vmo()
  firstMileType: number;

  @Vmo()
  firstMileTypeName: string;

  @Vmo()
  isBlackPeriod: boolean; // 是否冻结

  @Vmo()
  fmAllocationId: number;

  @Vmo()
  canChangeFmType: boolean;

  @Vmo()
  changeFmTypeBlackOutReason: string;

  @Vmo()
  canChange3pl: boolean;

  @Vmo()
  change3plBlackOutReason: string;

  @Vmo()
  blackOutReason: string; //冻结原因

  @Vmo()
  blackDate: string; // 冻结时间

  @Vmo()
  actionStatus: boolean;

  @Vmo()
  migrationType: string;

  @Vmo()
  dopLink: string;

  @Vmo()
  suggestedEffectiveTime: number;

  @Vmo()
  suggestedFirstMileType: string;

  @Vmo()
  suggestedShippingProviderName: string;

  @Vmo()
  hideRequestBtn: boolean; //控制Actions按钮的显示与隐藏，优先级最高

  @Vmo()
  migrationTypeName: string;
}

@Vmo()
export class HistoryResult {
  constructor(data: any) {}

  @Vmo()
  actionStatus: string;

  @Vmo()
  afterShippingProviderName: string;

  @Vmo()
  beforeShippingProviderName: string;

  @Vmo()
  migrationType: string;

  @Vmo()
  migrationTypeName: string;

  @Vmo()
  pickUpType: string;

  @Vmo()
  reason: string;

  @Vmo()
  reqTimeStamp: string;

  @Vmo()
  warehouseCode: string;

  @Vmo()
  warehouseName: string;

  @Vmo()
  afterFirstMileType: string;

  @Vmo()
  gmtModified: number;

  @Vmo()
  solutionCode: string;

  @Vmo()
  deliveryOption: string;

  @Vmo()
  beforeFirstMileType: string;

  @Vmo()
  sellerPreferenceReasonName: string;

  @Vmo()
  detailReasonCodeName: string;

  @Vmo()
  gmtCreateName: string;

  @Vmo()
  gmtModifiedName: string;

  @Vmo()
  eventStatusName: string;

  @Vmo()
  note: string;
}

@Vmo()
export class ReasonResult {
  constructor(data: any) {}

  @Vmo()
  reasonDesc: string;

  @Vmo()
  reasonKey: string;

  @Vmo()
  reasonValue: string;
}

@Vmo()
export class ReasonList {
  constructor(data: any) {}

  @Vmo()
  detailReasonCode: string;

  @Vmo()
  detailReasonName: string;
}

@Vmo()
export class FilterResult {
  constructor(data: any) {}

  @Vmo()
  label: string;

  @Vmo()
  value: string;
}
