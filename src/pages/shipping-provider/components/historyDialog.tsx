import React, { useState } from 'react';
import { Card, Table, Box, Pagination, Select, Form, Field } from '@alifd/next';
import * as i18n from '@alife/lazada-i18n'; // import * as i18n from '@alife/lazada-i18n';
import { useFusionTable, useRequest } from 'ahooks';
import { statusFilter } from '../constant';
import locale from '../locale';
import { getOperationsHistories, getFilterList } from '../service';

const Column = Table.Column;
const paginate = { pageSize: 20, current: 1 };

export const HistoryContent = () => {
  const { data: selectData } = useRequest(getFilterList);
  const field = Field.useField([]);
  const { paginationProps, search, tableProps } = useFusionTable(getOperationsHistories, { field, ...paginate });
  const { submit } = search;
  return (
    <Box spacing={16}>
      <Form inline style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }} field={field}>
        <Form.Item>
          <Select
            label={locale['warehouseName']}
            name="warehouseCode"
            hasClear
            onChange={submit}
            style={{ width: '400px' }}
            dataSource={selectData?.list}
          />
        </Form.Item>
        <Form.Item>
          <Select
            label="Status Filter"
            name="status"
            hasClear
            onChange={submit}
            style={{ width: '400px' }}
            dataSource={statusFilter}
          />
        </Form.Item>
      </Form>
      <Table
        {...tableProps}
        fixedHeader
        maxBodyHeight="50vh"
        rowProps={(record, index) => ({
          'data-spm': `history_tablerow_${index}`,
        })}
      >
        <Column width={100} title={locale['warehouseCode']} dataIndex="warehouseCode" />
        <Column width={100} title={locale['warehouseName']} dataIndex="warehouseName" />
        <Column width={100} title={locale['solutionCode']} dataIndex="deliveryOption" />
        <Column width={100} title={locale['migrationType']} dataIndex="migrationTypeName" />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.common.sellerConfig.fm.requestHisColumns.originalProviderName',
            defaultMessage: 'Original Shipping Provider',
          })}
          dataIndex="beforeShippingProviderName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.before.fm',
            defaultMessage: 'Origin First Mile Type',
          })}
          dataIndex="beforeFirstMileType"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.shipping.provider',
            defaultMessage: 'Shipping Provider',
          })}
          dataIndex="afterShippingProviderName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.fm',
            defaultMessage: 'First Mile Type',
          })}
          dataIndex="afterFirstMileType"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.request',
            defaultMessage: 'Request',
          })}
          dataIndex="sellerPreferenceReasonName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.request.reason',
            defaultMessage: 'Request Reason',
          })}
          dataIndex="detailReasonCodeName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.create.time',
            defaultMessage: 'Create Time',
          })}
          dataIndex="gmtCreateName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.common.sellerConfig.fm.requestHisColumns.reqTimeStamp',
            defaultMessage: 'Last Updated',
          })}
          dataIndex="gmtModifiedName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.status',
            defaultMessage: 'Status',
          })}
          dataIndex="eventStatusName"
        />
        <Column
          width={100}
          title={i18n.formatMessage({
            id: 'page.sellerProfile.shipping.provider.history.note',
            defaultMessage: 'Note',
          })}
          dataIndex="note"
        />
      </Table>
      <Pagination {...paginationProps} />
    </Box>
  );
};
