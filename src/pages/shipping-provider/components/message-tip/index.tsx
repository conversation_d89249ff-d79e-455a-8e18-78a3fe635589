import React from 'react';
import { Box, Message, Button, Icon, Balloon } from '@alifd/next';
import { SafeHtmlText } from '@alife/asc-components';
import moment from 'moment';
import { When } from '@alife/workstation-utils';

const ToolTip = Balloon.Tooltip;

function MessageTip({
  value,
  suggestedFirstMileType = '',
  suggestedShippingProviderName = '',
  suggestedEffectiveTime,
  locale,
}) {
  const time = moment(suggestedEffectiveTime).format('YYYY-MM-DD');
  return (
    <ToolTip
      trigger={
        <Box direction="row">
          {value}
          <Button text component="a">
            <Icon type="warning" style={{ color: '#FFA003', marginLeft: '8px' }} />
          </Button>
        </Box>
      }
    >
      <Box direction="column">
        <When condition={!!suggestedFirstMileType}>
          <SafeHtmlText
            html={locale.adjustmentTips.FmType?.replace('{provider}', suggestedFirstMileType)?.replace(
              '{dateTime}',
              time,
            )}
          />
        </When>
        <When condition={!!suggestedShippingProviderName}>
          <SafeHtmlText
            style={{ marginTop: '8px' }}
            html={locale.adjustmentTips.threePl
              ?.replace('{threePL}', suggestedShippingProviderName)
              ?.replace('{dateTime}', time)}
          />
        </When>
      </Box>
    </ToolTip>
  );
}

export default MessageTip;
