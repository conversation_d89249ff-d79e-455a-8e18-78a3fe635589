import React, { useState } from 'react';
import { Box, Button } from '@alifd/next';
import { createForm } from '@formily/core';
import { FormConsumer, createSchemaField, connect } from '@formily/react';
import { Form, FormStep, FormItem, Input, FormButtonGroup, Select } from '@formily/next';
import { CheckboxGroup } from '@alife/asc-campaign-components';
import { When } from '@alife/workstation-utils';
import { useCreation } from 'ahooks';
import locale from '../locale';
import { renderAgreement, renderReason, resultPage, resultProps } from './steps';
import commonLocale from '../../../constants/common-locale';
import { saveChanges } from '../service';

const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    CheckboxGroup: connect(CheckboxGroup),
    Select,
    renderAgreement,
    renderReason,
  },
});

export const ShippingContent = props => {
  // dialogType 0 为first mile  1为3pl
  const {
    field,
    refresh,
    record: { firstMileType = 0, shippingProviderName, dialogType = 0 },
  } = props;
  // console.log(props);
  const [success, setSuccess] = useState<resultProps>({
    dialogType: dialogType,
    firstMileType: firstMileType,
    flag: false,
  });
  const [form, formStep] = useCreation(() => [createForm({}), FormStep.createFormStep!()], []);

  return (
    <Box spacing={16} className="aplus-module-auto-exp shipping-provider-dialog-wrap" data-spm="shipping_change">
      <When condition={!success?.flag}>
        <Form form={form}>
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  content: locale['page.sellerProfile.shipping.provider.reason.step2.description'],
                }}
              >
                <SchemaField.String
                  name="step1"
                  x-decorator="FormItem"
                  required
                  x-component="renderReason"
                  x-component-props={{
                    firstMileType: firstMileType,
                    dialogType: dialogType,
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  content: locale['page.sellerProfile.shipping.provider.agreement.confirm'],
                }}
              >
                <SchemaField.String
                  name="step2"
                  x-decorator="FormItem"
                  required
                  x-component="renderAgreement"
                  x-component-props={{
                    firstMileType: firstMileType,
                    dialogType: dialogType,
                    shippingProviderName: shippingProviderName,
                  }}
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
          <FormConsumer>
            {() => (
              <FormButtonGroup align="right">
                <Button
                  className="aplus-auto-exp"
                  type="primary"
                  data-spm="d_next"
                  onClick={() => {
                    formStep.current === 0 ? formStep.next() : formStep.back();
                  }}
                >
                  {formStep.current === 0
                    ? commonLocale['page.common.btn.next']
                    : commonLocale['page.common.btn.previous']}
                </Button>
                <When condition={formStep.current !== 0}>
                  <Button
                    disabled={formStep.allowNext}
                    className="aplus-auto-exp"
                    data-spm="d_submit"
                    type="primary"
                    onClick={() => {
                      const fmAllocationId = field.getValue('fmAllocationId');
                      formStep.submit(async values => {
                        const { step2, step1 } = values;
                        const { notSuccess, module } = await saveChanges({
                          preferenceReason:
                            dialogType === 0
                              ? 'page.common.sellerConfig.fm.3plReason.reason11'
                              : 'page.common.sellerConfig.fm.3plReason.reason12',
                          allowDowngrade: step2?.[0] === '1',
                          detailReasonCode: step1,
                          fmAllocationId,
                        });
                        if (!notSuccess) {
                          setSuccess({ ...module, firstMileType, dialogType, flag: true });
                        }
                      });
                    }}
                  >
                    {commonLocale['page.common.btn.submit']}
                  </Button>
                </When>
              </FormButtonGroup>
            )}
          </FormConsumer>
        </Form>
      </When>
      <When condition={!!success?.flag}>{resultPage({ ...success, refresh: refresh })}</When>
    </Box>
  );
};
