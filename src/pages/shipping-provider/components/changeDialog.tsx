import React, { useState } from 'react';
import { Box, Button } from '@alifd/next';
import { createForm, onFormMount } from '@formily/core';
import { Form, FormStep, FormItem, Input, FormButtonGroup, Select } from '@formily/next';
import { Field } from '@formily/react';

interface IProps {
  record?: any;
  field?: any;
}
export const ChangeDialogContent = ({ record, field }: IProps) => {
  console.log(record, field);
  return (
    <Box>
      <Form>
        <Field name="test" title="title"></Field>
      </Form>
    </Box>
  );
};
