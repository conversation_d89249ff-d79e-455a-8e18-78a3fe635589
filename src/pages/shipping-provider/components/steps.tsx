import React, { useEffect, useState } from 'react';
import { Box, Checkbox, Radio, Typography, Select, Icon, Button } from '@alifd/next';
import locale from '../locale';
import { Result, SafeHtmlText } from '@alife/asc-components';
import { conditions, resultTitle } from '../constant';
import { ReasonList } from '../models';
import { useCreation, useRequest } from 'ahooks';
import { getReasonByType, visibleConfig } from '../service';
import { ASCTools, When } from '@alife/workstation-utils';
import moment from 'moment';

export interface resultProps {
  changeStatus?: number;
  dialogType: number;
  firstMileType: number;
  flag?: boolean;
  changeNote?: string;
  beforePickupType?: string;
  afterPickupType?: string;
  afterShippingProviderName?: string;
  beforeShippingProviderName?: string;
  effectiveTime?: number;
  refresh?: () => void;
}

export const renderReason = props => {
  const { onChange, value, firstMileType, dialogType } = props;
  const key =
    dialogType === 0
      ? 'page.common.sellerConfig.fm.3plReason.reason11'
      : 'page.common.sellerConfig.fm.3plReason.reason12';
  const { data } = useRequest(() => getReasonByType(firstMileType, key));
  const { list = [] } = data || {};
  const cache = localStorage.getItem(`${key}-${window?.dadaConfig?.sellerId || ''}`);
  let result: ReasonList[] = [];
  try {
    result = !!list?.length ? list : (!!cache && JSON.parse(cache)) || [];
  } catch (e) {
    result = list;
    console.log(e);
  }
  // firstMileType dropOff->pickUp   pickUp->dropOff   3pl firstMileType为pickup和dropOff
  const title =
    locale[
      `page.sellerProfile.shipping.provider.popup.${
        dialogType === 0 ? (firstMileType === 0 ? 'fm.pu.to.do' : 'fm.do.to.pu') : '3pl.change.title'
      }`
    ];
  return (
    <Box>
      <Typography.H4>{title}</Typography.H4>
      <Radio.Group itemDirection="ver" value={value} onChange={onChange}>
        {result?.map(el => (
          <Radio value={el?.detailReasonCode}>{el?.detailReasonName}</Radio>
        ))}
      </Radio.Group>
    </Box>
  );
};

export const renderAgreement = props => {
  const { onChange, value, firstMileType, dialogType, shippingProviderName } = props;
  const { data: configs } = useRequest(() => visibleConfig('169005'));
  let result: Array<{
    venture?: string;
    pickUp?: string;
    firstMile?: string;
    dropOff?: string;
    changeProvider?: string;
  }> = [];
  try {
    const cache = localStorage.getItem('shippingProviderControlCache');
    result = (!!cache && JSON.parse(cache)) || configs || [];
  } catch {
    result = configs || [];
  }
  const venture = ASCTools.getCountry();
  const record = result?.find(el => el?.['venture'] === venture);
  // firstMileType type match
  const type = (firstMileType === 0 && record?.pickUp) || (firstMileType === 1 && record?.dropOff);
  // If the type is the same show it
  const show =
    !record || (dialogType === 0 && record?.firstMile && type) || (dialogType === 1 && record?.changeProvider && type);
  useEffect(() => {
    !!result && !show && onChange && onChange('0');
  }, [configs]);

  return (
    <Box>
      <Box>
        <Typography.H4>
          <SafeHtmlText
            html={
              locale[
                `page.sellerProfile.shipping.provider.agreement.${
                  dialogType === 0 ? (firstMileType === 0 ? 'title.first' : 'title.dropOff.first') : '3pl.title.first'
                }`
              ]
            }
          />
        </Typography.H4>
        <When condition={dialogType === 0}>
          <Box>
            <SafeHtmlText
              html={
                locale[
                  `page.sellerProfile.shipping.provider.agreement.${
                    firstMileType === 0 ? 'title.list1' : 'doPu.title.list1'
                  }`
                ]
              }
            />
          </Box>
          <Box>
            <SafeHtmlText
              html={
                locale[
                  `page.sellerProfile.shipping.provider.agreement.${
                    firstMileType === 0 ? 'title.list2' : 'doPu.title.list2'
                  }`
                ]
              }
            />
          </Box>
        </When>
      </Box>
      <Box>
        <When condition={!!show}>
          <Typography.H4>
            <SafeHtmlText
              html={`${
                locale[
                  `page.sellerProfile.shipping.provider.agreement.title.${
                    dialogType === 0
                      ? firstMileType === 0
                        ? 'fm.pu.do'
                        : 'fm.do.pu'
                      : firstMileType === 0
                      ? '3pl.pu'
                      : '3pl.do'
                  }`
                ]
              } ${
                dialogType === 0
                  ? firstMileType === 0
                    ? `(${shippingProviderName}) ${locale['page.sellerProfile.shipping.provider.agreement.title.fm.pu.do.end']}`
                    : `(${shippingProviderName}) ${locale['page.sellerProfile.shipping.provider.agreement.title.fm.do.pu.end']}`
                  : ''
              }`}
            />
          </Typography.H4>
          <Radio.Group itemDirection="ver" value={value} onChange={onChange}>
            <Radio value="1">{locale['page.sellerProfile.shipping.provider.agreement.yes']}</Radio>
            <Radio value="0">{locale['page.sellerProfile.shipping.provider.agreement.no']}</Radio>
          </Radio.Group>
        </When>
        <Typography.Text>
          <SafeHtmlText html={locale['page.sellerProfile.shipping.provider.agreement.click.tip']} />
        </Typography.Text>
      </Box>
    </Box>
  );
};

export const resultPage = (props: resultProps) => {
  const {
    changeStatus = 'success',
    effectiveTime,
    beforeShippingProviderName,
    afterShippingProviderName,
    changeNote,
    beforePickupType,
    afterPickupType,
    refresh,
  } = props;
  const type = changeStatus === 2 ? 'notice' : changeStatus === 0 ? 'error' : 'success';
  const titleMap = resultTitle[type] || {};
  const title = titleMap.title;
  const image =
    type === 'error'
      ? '//img.alicdn.com/imgextra/i2/O1CN01ExhBd11Sjey2QTCZK_!!6000000002283-2-tps-363-321.png'
      : '//img.alicdn.com/imgextra/i3/O1CN016qsalC1YblqKToTuB_!!6000000003078-2-tps-270-198.png';
  const subTitle = () => {
    return (
      <Box align="center" style={{ textAlign: 'center' }}>
        <When
          condition={type === 'success'}
          else={
            <>
              {/* <When condition={titleMap.subTitle}>
                <Box>{titleMap.subTitle}</Box>
              </When> */}
              {changeNote}
            </>
          }
        >
          <Box>{changeNote}</Box>
          <span style={{ color: '#EB5E00' }}>{moment(effectiveTime).format('YYYY/MM/DD')}</span>
        </When>
      </Box>
    );
  };

  const onClick = () => {
    const el = document.querySelector('.next-dialog-close');
    !!refresh && refresh();
    // @ts-ignore
    el && el.click && el.click();
  };

  return (
    <Box>
      <Result imgUrl={image} type={type} title={title} subTitle={subTitle()}>
        <When condition={type === 'success'}>
          <Box direction="row" justify="space-between">
            <Box className="success-result">
              <Typography.H6>{locale['first.mile.type']}:</Typography.H6>
              <Box direction="row" className="change-first-mile">
                <When
                  condition={beforePickupType !== afterPickupType}
                  else={
                    <span>{`${beforePickupType} (${locale['page.sellerProfile.shipping.provider.not.change']})`}</span>
                  }
                >
                  <span>
                    {beforePickupType}{' '}
                    <img src="//img.alicdn.com/imgextra/i1/O1CN01j2c2ir1sDlOcMA4Gk_!!6000000005733-2-tps-48-36.png" />
                  </span>
                  <span>{afterPickupType}</span>
                </When>
              </Box>
            </Box>
            <Box className="success-result">
              <Typography.H6>{locale['shipping.provider.result']}:</Typography.H6>
              {/* <Typography.Text style={{ color: '#EB5E00' }}>{afterShippingProviderName}</Typography.Text> */}
              <Box direction="row" className="change-first-mile">
                <When
                  condition={beforeShippingProviderName !== afterShippingProviderName}
                  else={
                    <span>{`${beforeShippingProviderName} (${locale['page.sellerProfile.shipping.provider.not.change']})`}</span>
                  }
                >
                  <span>
                    {beforeShippingProviderName}{' '}
                    <img src="//img.alicdn.com/imgextra/i1/O1CN01j2c2ir1sDlOcMA4Gk_!!6000000005733-2-tps-48-36.png" />
                  </span>
                  <span>{afterShippingProviderName}</span>
                </When>
              </Box>
            </Box>
          </Box>
        </When>
        <Box align="flex-end" style={{ width: '590px', marginTop: '30px' }}>
          <Button onClick={onClick} type="primary">
            {locale['page.sellerProfile.shipping.provider.result.ok']}
          </Button>
        </Box>
      </Result>
    </Box>
  );
};
