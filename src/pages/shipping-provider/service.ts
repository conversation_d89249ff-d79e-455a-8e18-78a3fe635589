import { Message } from '@alifd/next';
import { axios } from '@alife/workstation-utils';
import { HistoryResult, ReasonResult, ShippingResult, ReasonList, FilterResult } from './models';

export const getShippingProvider = async (): Promise<[
  {
    list: ShippingResult[];
    total: number;
  },
]> => {
  return axios.get('mtop.lazada.seller.fulfillment.shipping.provider.list.get').then(res => {
    return [
      {
        list:
          res.data?.module &&
          (res.data?.module?.shippingDataList?.map(item => {
            return new ShippingResult(item);
          }) as ShippingResult[]),
        total: res.data?.module?.shippingDataList?.length ?? 0,
      },
    ];
  });
};

export const getHistoryList = async (): Promise<[
  {
    list: HistoryResult[];
    total: number;
  },
]> => {
  return axios.get('mtop.lazada.seller.fulfillment.shipping.provider.list.get').then(res => {
    return [
      {
        list:
          res.data.module &&
          (res.data.module.historyDataList.map(item => {
            return new HistoryResult(item);
          }) as HistoryResult[]),
        total: res.data?.module?.historyDataList?.length ?? 0,
      },
    ];
  });
};

export const getReasonList = async (): Promise<{
  list: ReasonResult[];
  total: number;
}> => {
  return axios.get('mtop.lazada.seller.fulfillment.shipping.provider.list.get').then(res => {
    return {
      list:
        res.data.module &&
        (res.data.module.changeReasonList.map(item => {
          return new ReasonResult(item);
        }) as ReasonResult[]),
      total: res.data?.module?.changeReasonList?.length ?? 0,
    };
  });
};

export const updateProvider = async (data): Promise<{ notSuccess: boolean }> => {
  return axios
    .post('mtop.lazada.seller.fulfillment.shipping.provider.list.update', data)
    .then(res => {
      return res.data;
    })
    .catch(err => {
      Message.error(err?.message);
    });
};

export const getReasonByType = async (
  firstMileType: number,
  preferenceReason: 'page.common.sellerConfig.fm.3plReason.reason11' | 'page.common.sellerConfig.fm.3plReason.reason12',
): Promise<{
  list: ReasonList[];
}> => {
  return axios
    .get('mtop.lazada.seller.fulfillment.shipping.provider.reason.get', {
      data: {
        firstMileType: firstMileType,
        preferenceReason: preferenceReason,
      },
    })
    .then(res => {
      const list =
        res.data.module &&
        (res.data.module?.map(item => {
          return new ReasonList(item);
        }) as ReasonList[]);
      try {
        localStorage.setItem(`${preferenceReason}-${window?.dadaConfig?.sellerId || ''}`, JSON.stringify(list));
      } catch (e) {
        console.log(e);
      }
      return {
        list: list,
        total: res.data?.module?.length ?? 0,
      };
    });
};

export const saveChanges = async (data: {
  fmAllocationId: number;
  preferenceReason: string;
  detailReasonCode: string;
  allowDowngrade?: boolean;
}): Promise<{ notSuccess: boolean; module: { changeStatus: number; changeNote: string } }> => {
  return axios
    .post('mtop.lazada.seller.fulfillment.shipping.provider.change', data)
    .then(res => {
      return res.data;
    })
    .catch(err => {
      Message.error(err?.message);
    });
};

export const getOperationsHistories = async (
  data: { warehouseCode?: string; status?: number; pageSize?: number; pageNum?: number; current?: number },
  formData: any,
): Promise<{
  list: HistoryResult[];
  pageNum: number | string;
  pageSize: number | string;
  totalPage: number | string;
  totalRecord: number | string;
  total: number | string;
}> => {
  return axios
    .get('mtop.lazada.seller.fulfillment.shipping.provider.history', {
      data: { ...data, ...formData, pageNum: data?.current },
    })
    .then(res => {
      const module = res?.data?.module || {};
      return {
        list: module.list?.map(item => {
          return new HistoryResult(item);
        }) as HistoryResult[],
        pageNum: module.pageNum,
        pageSize: module.pageSize,
        totalPage: module.totalPage,
        totalRecord: module.totalRecord,
        total: module.totalRecord,
      };
    });
};

export const getNewShippingProviders = async (): Promise<[
  {
    list: ShippingResult[];
    total: number;
  },
]> => {
  return axios.get('mtop.lazada.seller.fulfillment.shipping.provider.get').then(res => {
    return [
      {
        list:
          res.data?.module &&
          (res.data?.module?.map(item => {
            return new ShippingResult(item);
          }) as ShippingResult[]),
        total: res.data?.module?.length ?? 0,
      },
    ];
  });
};

export const getFilterList = async (): Promise<{
  list: FilterResult[];
}> => {
  return axios.get('mtop.lazada.rc.multi.address.names').then(res => {
    return {
      list:
        res?.data?.data &&
        (res.data.data.options.map(item => {
          return new FilterResult(item);
        }) as FilterResult[]),
      total: res?.data?.data?.options?.length ?? 0,
    };
  });
};

export const visibleConfig = (
  resId?: string,
): Promise<Array<{
  venture?: string;
  pickUp?: string;
  firstMile?: string;
  dropOff?: string;
  changeProvider?: string;
}>> => {
  if (!resId) return Promise.resolve([]);
  return axios
    .get('mtop.lazada.merchant.seller.ald.lamp.query', {
      data: {
        resId: resId,
      },
    })
    .then(res => {
      const list = res?.data?.data?.options || [];
      try {
        localStorage.setItem('shippingProviderControlCache', JSON.stringify(list));
      } catch (e) {
        console.log(e);
      }
      return list;
    });
};
