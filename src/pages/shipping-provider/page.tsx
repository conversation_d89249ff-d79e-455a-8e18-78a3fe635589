import * as i18n from '@alife/lazada-i18n'; // import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React, { useRef, useState } from 'react';
import { NavTab, PageContainer, HelpTip, useFormDialog, SafeHtmlText } from '@alife/asc-components';
import { Card, Table, Box, Button, Icon, Tag } from '@alifd/next';
import { When, ASCTools } from '@alife/workstation-utils';
import SettingPageTitle from '../../components/setting-page-title';
import MessageTip from './components/message-tip';
import { Balloon } from '@alifd/next';
import './page.scss';
import { useFusionTable, useMemoizedFn } from 'ahooks';
import { getShippingProvider, getNewShippingProviders } from './service';
import locale from './locale';
import { HistoryContent, ShippingContent } from './components';
import { ShippingResult } from './models';
import { Tooltip } from '@alifd/next/lib/balloon';

const Column = Table.Column;
const ToolTip = Balloon.Tooltip;
const currentBU = ASCTools.getBU();

function ResultPage() {
  const { tableProps, run, search } = useFusionTable(() => getNewShippingProviders().then(([res]) => res), {});
  const [provideTitle, setProviderTitle] = useState('');
  const { reset } = search;

  const [ShippingDialog, { edit }] = useFormDialog({
    content: ShippingContent,
    dialogProps: {
      style: {
        width: 640,
      },
      payload: {
        footerActions: ['ok'],
        refresh: run,
      },
    },
    onOk: () => reset(),
    footer: false,
  });

  const [HistoryDialog, { show: showHistory }] = useFormDialog({
    content: HistoryContent,
  });

  const textWithTips = useMemoizedFn((title, tip) => (
    <ToolTip
      trigger={
        <Box direction="row">
          {title}
          <Button type="secondary" style={{ marginLeft: '8px' }} text component="a">
            <Icon type="help" />
          </Button>
        </Box>
      }
    >
      <SafeHtmlText html={tip} />
    </ToolTip>
  ));

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle
        actions={
          currentBU !== 'DARAZ' && (
            <Button
              text
              className="aplus-auto-exp"
              data-spm="d_shipping_to_myshipment"
              component="a"
              href="/apps/fulfillment/shipment"
            >
              <Tag style={{ marginRight: '4px' }} size="small" color="red">
                {i18n.formatMessage({
                  id: 'page.shipping.provider.title.action.tag',
                  defaultMessage: 'New',
                })}
              </Tag>
              {i18n.formatMessage({
                id: 'page.shipping.provider.title.action',
                defaultMessage: 'Go to My Shipment',
              })}
            </Button>
          )
        }
        title={commonLocale['page.common.nav.shippingProvider']}
      />
      {/* <Button
        text
        onClick={() => {
          setProviderTitle('change shipping provider');
          edit({}).catch(() => {});
        }}
      >
        change shipping provider
      </Button> */}
      <Card>
        <Box spacing={16}>
          <Box direction="row" justify="end" data-spm="toolbar">
            <Button
              data-spm="d_view_history"
              className="aplus-auto-exp"
              type="primary"
              onClick={() => showHistory({}).catch(() => {})}
            >
              {i18n.formatMessage({ id: 'data.view_history.lazada', defaultMessage: 'View History' })}
            </Button>
          </Box>
          <Table
            {...tableProps}
            rowProps={(record, index) => ({
              'data-spm': `tablerow_${index}`,
            })}
          >
            <Column
              width={100}
              title={textWithTips(locale['warehouseCode'], locale['warehouseCodeTip'])}
              dataIndex="warehouseCode"
            />
            <Column width={100} title={locale['warehouseName']} dataIndex="warehouseName" />

            <Column
              width={100}
              title={textWithTips(locale['solutionCode'], locale['solutionCodeTip'])}
              dataIndex="deliveryOption"
            />
            <Column width={100} title={locale['migrationType']} dataIndex="migrationTypeName" />

            <Column
              width={100}
              title={i18n.formatMessage({
                id: 'page.common.sellerConfig.fm.shippingColumns.first.mile.type',
                defaultMessage: 'First Mile Type',
              })}
              dataIndex="firstMileTypeName"
              cell={(v, index, record) => {
                const { suggestedEffectiveTime, suggestedFirstMileType } = record;

                const FMText = (
                  <>
                    {v}
                    <a
                      target="_blank"
                      href={record?.dopLink}
                      data-spm="d_view_location"
                      className="aplus-auto-exp type-icon"
                    >
                      <img src="//img.alicdn.com/imgextra/i1/O1CN01MhdpEI1lwv8OJI8RG_!!6000000004884-2-tps-72-72.png" />
                    </a>
                  </>
                );

                return (
                  <When
                    condition={record?.firstMileType === 1}
                    else={
                      <When condition={!!suggestedFirstMileType && !!suggestedEffectiveTime} else={v}>
                        <MessageTip
                          locale={locale}
                          value={v}
                          suggestedEffectiveTime={suggestedEffectiveTime}
                          suggestedFirstMileType={suggestedFirstMileType}
                        />
                      </When>
                    }
                  >
                    <div className="drop-off-type-location">
                      <Tooltip
                        trigger={
                          <When condition={!!suggestedFirstMileType && !!suggestedEffectiveTime} else={FMText}>
                            <MessageTip
                              locale={locale}
                              value={FMText}
                              suggestedEffectiveTime={suggestedEffectiveTime}
                              suggestedFirstMileType={suggestedFirstMileType}
                            />
                          </When>
                        }
                      >
                        {i18n.formatMessage({
                          id: 'page.common.sellerConfig.fm.shippingColumns.viewLocation',
                          defaultMessage: 'Click to View drop off Location',
                        })}
                      </Tooltip>
                    </div>
                  </When>
                );
              }}
            />
            <Column
              width={100}
              title={i18n.formatMessage({
                id: 'page.common.sellerConfig.fm.shippingColumns.shippingProviderName',
                defaultMessage: 'Shipping Provider',
              })}
              dataIndex="shippingProviderName"
              cell={(v, index, record) => {
                const { suggestedEffectiveTime, suggestedShippingProviderName } = record;
                return (
                  <When condition={!!suggestedShippingProviderName && !!suggestedEffectiveTime} else={v}>
                    <MessageTip
                      locale={locale}
                      value={v}
                      suggestedEffectiveTime={suggestedEffectiveTime}
                      suggestedShippingProviderName={suggestedShippingProviderName}
                    />
                  </When>
                );
              }}
            />
            <Column
              width={100}
              title={textWithTips(
                i18n.formatMessage({
                  id: 'page.common.sellerConfig.fm.shippingColumns.spillShippingProviderName',
                  defaultMessage: 'List of Additional 3PL Providers',
                }),
                i18n.formatMessage({
                  id: 'page.common.sellerConfig.fm.shippingColumns.spillShippingProviderName.tips',
                  defaultMessage:
                    'List of Additional 3PL Providers is Additional 3PL from Multiple Pickup Project, Those First Mile 3PLs are Also Do Pickup From you Warehouse',
                }),
              )}
              dataIndex="spillShippingProviderName"
            />
            <Column
              width={150}
              lock="right"
              title={i18n.formatMessage({ id: 'page.account.setting.actions', defaultMessage: 'Actions' })}
              cell={(value, index, record: ShippingResult) => {
                // if (record.hideRequestBtn) return;
                // 是否可修改first mile type 及不能修改原因
                const canChangeFmType = record?.canChangeFmType;
                const changeFmTypeBlackOutReason = record?.changeFmTypeBlackOutReason;
                // 是否可修改3pl 及不能修改原因
                const canChange3pl = record?.canChange3pl;
                const change3plBlackOutReason = record?.change3plBlackOutReason;
                // 是否大促封网不可修改及其原因 和封网时间段
                const isBlackPeriod = record?.isBlackPeriod;
                const blackOutReason = record?.blackOutReason;
                const blackDate = record?.blackDate;
                // fm shp按钮 disabled
                const fmDisable = !canChangeFmType || !!isBlackPeriod;
                const shpDisable = !canChange3pl || !!isBlackPeriod;
                // type 0 为first mile  1为3pl
                const commonBtn = (disabled, title, type) => (
                  <Button
                    text
                    disabled={disabled}
                    data-more={`${record?.warehouseCode}-${record?.firstMileType}-${record?.solutionCode}`}
                    data-spm={`d_shipping_provider_${type}`}
                    className="aplus-auto-exp"
                    onClick={() => {
                      setProviderTitle(title);
                      edit({ ...record, dialogType: type }).catch(() => {});
                    }}
                  >
                    {title}
                  </Button>
                );

                const fmButton = commonBtn(
                  fmDisable,
                  locale['page.sellerProfile.shipping.provider.type.first.mile'],
                  0,
                );
                const plButton = commonBtn(shpDisable, locale['page.sellerProfile.shipping.provider.type.provider'], 1);

                return (
                  <Box className="aplus-module-auto-exp" data-spm={`${record?.warehouseCode}-${index}`}>
                    {currentBU !== 'DARAZ' && (
                      <When condition={fmDisable} else={fmButton}>
                        <div className="shipping-provider-actions">
                          {fmButton}
                          <Tooltip trigger={<Icon style={{}} type="prompt" />}>
                            <SafeHtmlText
                              html={!!isBlackPeriod ? `${blackOutReason} ${blackDate}` : changeFmTypeBlackOutReason}
                            />
                          </Tooltip>
                        </div>
                      </When>
                    )}
                    {currentBU !== 'DARAZ' && (
                      <When condition={shpDisable} else={plButton}>
                        <div className="shipping-provider-actions">
                          {plButton}
                          <Tooltip trigger={<Icon type="prompt" />}>
                            <SafeHtmlText
                              html={!!isBlackPeriod ? `${blackOutReason} ${blackDate}` : change3plBlackOutReason}
                            />
                          </Tooltip>
                        </div>
                      </When>
                    )}
                  </Box>
                );
              }}
            />
          </Table>
        </Box>
      </Card>
      <ShippingDialog title={provideTitle} />
      {/* locale['page.sellerProfile.shipping.provider.dialog.3pl.title'] */}
      <HistoryDialog
        title={i18n.formatMessage({
          id: 'page.sellerProfile.shipping.rhTitle',
          defaultMessage: 'Request History',
        })}
      />
    </PageContainer>
  );
}

export default ResultPage;
