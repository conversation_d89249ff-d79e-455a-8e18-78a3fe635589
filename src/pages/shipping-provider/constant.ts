import { getCountry } from '@alife/workstation-utils/lib/pure-functions/asc-tools';
import locale from './locale';
import commonLocale from '../../constants/common-locale';

export const conditions = [
  {
    label: locale['page.sellerProfile.shipping.provider.step0.faq.label1'],
    value: '1',
  },
  {
    label: locale['page.sellerProfile.shipping.provider.step0.faq.label2'],
    value: '2',
  },
  {
    label: locale['page.sellerProfile.shipping.provider.step0.faq.label3'],
    value: '3',
  },
  {
    label: locale['page.sellerProfile.shipping.provider.step0.faq.label4'],
    value: '4',
  },
  {
    label: locale['page.sellerProfile.shipping.provider.step0.faq.label5'],
    value: '5',
  },
];

export const resultTitle = {
  success: {
    title: locale['page.sellerProfile.shipping.provider.result.success.title'],
    content: locale['page.sellerProfile.shipping.provider.result.success.subtitle'],
  },
  error: {
    title: locale['page.sellerProfile.shipping.provider.result.error.title'],
    content: locale['page.sellerProfile.shipping.provider.result.error.subtitle'],
  },
  notice: {
    title: locale['page.sellerProfile.shipping.provider.result.notice.title'],
    subTitle: locale['page.sellerProfile.shipping.provider.result.notice.subtile'],
    content: locale['page.sellerProfile.shipping.provider.result.notice.content'],
  },
};

export const statusFilter = [
  {
    label: commonLocale['page.common.tips.pending'],
    value: 0,
  },
  {
    label: commonLocale['page.common.tips.pre.effective'],
    value: 1,
  },
  {
    label: commonLocale['page.common.tips.effective'],
    value: 2,
  },
  {
    label: commonLocale['page.common.tips.rejected'],
    value: 3,
  },
  {
    label: commonLocale['page.common.tips.error'],
    value: 4,
  },
  {
    label: commonLocale['page.common.btn.cancel'],
    value: 5,
  },
];

export const unsatisfy3plEnable = getCountry() === 'SG';
export const unsatisfy3plReason = 'page.common.sellerConfig.fm.3plReason.reason5';
export const unsatisfy3plSurvey = '/xiniu/survey/nh8pX7rEM';
