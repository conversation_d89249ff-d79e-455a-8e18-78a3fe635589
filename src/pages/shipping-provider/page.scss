.manage-image-page {
  .next-form.next-inline .next-form-item:not(:last-child) {
    margin-right: 12px;
  }
  .drop-off-type-location {
    .type-icon {
      margin-left: 5px;
      img {
        width: 16px;
        height: 16px;
      }
    }
  }

  .shipping-provider-actions {
    text-align: center;
    margin: 5px 0;
    i {
      margin-left: 5px;
    }
    i::before {
      font-size: 14px;
    }
  }
}

.shipping-provider-dialog-wrap {
  .lzd-result-scope {
    min-height: 200px;
  }
  .success-result {
    background-color: #d8d8d8;
    padding: 16px 0 20px 40px;
    margin-right: 2px;
    width: 288px;
    .change-first-mile {
      span {
        color: #eb5e00;
        img {
          width: 16px;
          height: 12px;
          margin: 0 5px;
        }
      }
      i {
        margin: 0 8px;
      }
      i::before {
        font-size: 14px;
      }
    }
  }
}

.mr5 {
  margin-right: 5px;
}

.mr10 {
  margin-right: 10px;
}

.mb15 {
  margin-bottom: 15px;
}

ul {
  list-style: none !important;
}

.reason-richtext {
  line-height: 1.5;
}
