import * as i18n from '@alife/lazada-i18n';

export default {
  'page.sellerProfile.shipping.provider.reason.status.frozen': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.status.frozen',
    defaultMessage: 'Change Frozen',
  }),
  'page.sellerProfile.shipping.provider.frozen.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.frozen.title',
    defaultMessage: 'Times:',
  }),
  'page.sellerProfile.shipping.provider.reason.status.change': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.status.change',
    defaultMessage: 'Request to Change',
  }),
  'page.sellerProfile.shipping.provider.reason.status.progress': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.status.progress',
    defaultMessage: 'Request in progress',
  }),
  'page.sellerProfile.shipping.provider.step2.agreement.content': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step2.agreement.content',
    defaultMessage:
      'I have read and understand the criteria for the change request and have selected the best reason...',
  }),
  'page.sellerProfile.shipping.provider.step0.content': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.content',
    defaultMessage:
      'As part of our effort to shorten the processing time of your \n requests and improve your experience, some of these change \n requests will be automated.',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.title',
    defaultMessage: 'What do you need to do?',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.label1': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.label1',
    defaultMessage: 'You warehouse Address is accurate and updated',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.label2': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.label2',
    defaultMessage: 'Update email address; update will be sent to main account only',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.label3': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.label3',
    defaultMessage: 'Make sure you select the most approprite reason for the change',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.label4': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.label4',
    defaultMessage: 'Understand and confirm that you meet the relevant criteria',
  }),
  'page.sellerProfile.shipping.provider.step0.faq.label5': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.faq.label5',
    defaultMessage: 'If you do not meet the criteria and your request is rejected…',
  }),
  'page.sellerProfile.shipping.provider.step0.btn.ok': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step0.btn.ok',
    defaultMessage: 'I understand and would like to proceeds',
  }),
  'page.sellerProfile.shipping.provider.step1.btn.ok': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step1.btn.ok',
    defaultMessage: 'Next',
  }),
  'page.sellerProfile.shipping.provider.step2.btn.ok': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step2.btn.ok',
    defaultMessage: 'Submit',
  }),
  'page.sellerProfile.shipping.provider.step3.btn.ok': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step3.btn.ok',
    defaultMessage: 'Close',
  }),
  'page.sellerProfile.shipping.provider.btn.cancel': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.btn.cancel',
    defaultMessage: 'Cancel',
  }),
  'page.sellerProfile.shipping.provider.btn.back': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.btn.back',
    defaultMessage: 'Back',
  }),
  'page.sellerProfile.shipping.provider.step3.content': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.step3.content',
    defaultMessage: '',
  }),
  'page.sellerProfile.shipping.provider.reason.step1.label': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step1.label',
    defaultMessage: 'Step 1',
  }),
  'page.sellerProfile.shipping.provider.reason.step2.label': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step2.label',
    defaultMessage: 'Step 2',
  }),
  'page.sellerProfile.shipping.provider.reason.step3.label': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step3.label',
    defaultMessage: 'Step 3',
  }),
  'page.sellerProfile.shipping.provider.reason.step4.label': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step4.label',
    defaultMessage: 'Step 4',
  }),
  'page.sellerProfile.shipping.provider.reason.step1.description': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step1.description',
    defaultMessage: 'Change Requirements',
  }),
  'page.sellerProfile.shipping.provider.reason.step2.description': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step2.description',
    defaultMessage: 'Reason to Change',
  }),
  'page.sellerProfile.shipping.provider.reason.step3.description': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step3.description',
    defaultMessage: 'Acknowledgement',
  }),
  'page.sellerProfile.shipping.provider.reason.step4.description': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.reason.step4.description',
    defaultMessage: 'Finished',
  }),
  'page.sellerProfile.shipping.provider.dialog.3pl.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.dialog.3pl.title',
    defaultMessage: 'Request to Change',
  }),
  'page.sellerProfile.shipping.provider.agreement.confirm': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.confirm',
    defaultMessage: 'Agreement Confirmation',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.first': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.first',
    defaultMessage: 'The following are the criteria that will be considered for pick-up service eligibility',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.dropOff.first': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.dropOff.first',
    defaultMessage: 'The following are the criteria that will be considered for drop-off service eligibility',
  }),
  'page.sellerProfile.shipping.provider.agreement.3pl.title.first': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.3pl.title.first',
    defaultMessage:
      'Lazada will allocate the best shipping provider partner for you based on your volume and warehouse location',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.list1': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.list1',
    defaultMessage:
      '1.Package profiles that can be accepted by drop-off points are maximum 20 kg weight and 70 cm length / width / height',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.list2': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.list2',
    defaultMessage: '2.Warehouse location is within coverage of the drop-off point (2kg) on Lazada Logistics',
  }),
  'page.sellerProfile.shipping.provider.agreement.doPu.title.list1': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.doPu.title.list1',
    defaultMessage: 'Minimum average parcels per day depends on 3PL that assigned to the seller',
  }),
  'page.sellerProfile.shipping.provider.agreement.doPu.title.list2': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.doPu.title.list2',
    defaultMessage:
      'Warehouse location is within the coverage of courier partners for pickup service on Lazada Logistics',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.fm.do.pu': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.fm.do.pu',
    defaultMessage: 'If current 3PL can not provide the drop-off service, will you allow to change your current 3PL ',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.fm.do.pu.end': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.fm.do.pu.end',
    defaultMessage: ' to other 3PL to move to pick-up',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.fm.pu.do': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.fm.pu.do',
    defaultMessage: 'If current 3PL can not provide the pick-up service will you allow to change your current 3PL ',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.fm.pu.do.end': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.fm.pu.do.end',
    defaultMessage: ' to other 3PL to move to drop-off',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.3pl.pu': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.3pl.pu',
    defaultMessage:
      'lf there is no other 3PL can provide pickup service will you allow to change your current first mile type pick-up to change 3PL',
  }),
  'page.sellerProfile.shipping.provider.agreement.title.3pl.do': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.title.3pl.do',
    defaultMessage:
      'If there is no other 3PL can provide dropoff service Will you allow to change your current first mile type drop-off to change 3PL',
  }),
  'page.sellerProfile.shipping.provider.agreement.yes': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.yes',
    defaultMessage: 'Yes',
  }),
  'page.sellerProfile.shipping.provider.agreement.no': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.no',
    defaultMessage: 'No',
  }),
  'page.sellerProfile.shipping.provider.agreement.click.tip': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.agreement.click.tip',
    defaultMessage: 'Click Submit means you have read and agree to the agreement',
  }),
  'page.sellerProfile.shipping.provider.result.success.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.success.title',
    defaultMessage: 'Success',
  }),
  'page.sellerProfile.shipping.provider.result.success.subtitle': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.success.subtitle',
    defaultMessage: 'Your request has been received and will be applied on',
  }),
  'page.sellerProfile.shipping.provider.result.error.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.error.title',
    defaultMessage: 'Sorry',
  }),
  'page.sellerProfile.shipping.provider.result.error.subtitle': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.error.subtitle',
    defaultMessage:
      'Based on your location there are currently no appropriate 3PL that is able to provide pick up service',
  }),
  'page.sellerProfile.shipping.provider.result.notice.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.notice.title',
    defaultMessage: 'Processing',
  }),
  'page.sellerProfile.shipping.provider.result.notice.subtitle': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.notice.subtitle',
    defaultMessage: 'We already received your request!',
  }),
  'page.sellerProfile.shipping.provider.result.notice.content': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.notice.content',
    defaultMessage:
      'Lazada will process your request within 5 working days you can check the request status in your request history',
  }),
  'page.sellerProfile.shipping.provider.type.first.mile': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.type.first.mile',
    defaultMessage: 'Change First mile type',
  }),
  'page.sellerProfile.shipping.provider.type.provider': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.type.provider',
    defaultMessage: 'Shipping provider',
  }),
  'page.sellerProfile.shipping.provider.type.drop.off': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.type.drop.off',
    defaultMessage: 'DropOff',
  }),
  'page.sellerProfile.shipping.provider.type.pick.up': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.type.pick.up',
    defaultMessage: 'PickUp',
  }),
  'page.sellerProfile.shipping.provider.result.ok': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.result.ok',
    defaultMessage: 'OK',
  }),
  'page.sellerProfile.shipping.provider.popup.fm.do.to.pu': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.popup.fm.do.to.pu',
    defaultMessage: 'Why you want to change from Drop-off to Pick-up',
  }),
  'page.sellerProfile.shipping.provider.popup.fm.pu.to.do': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.popup.fm.pu.to.do',
    defaultMessage: 'Why you want to change from Pick-up to Drop-off',
  }),
  'page.sellerProfile.shipping.provider.popup.3pl.change.title': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.popup.3pl.pick.up',
    defaultMessage: 'Why do you want to change 3PL?',
  }),
  'page.sellerProfile.shipping.provider.not.change': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.not.change',
    defaultMessage: 'Not Change',
  }),
  warehouseCode: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.warehouseCode',
    defaultMessage: 'Warehouse ID',
  }),
  warehouseCodeTip: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.warehouseCode.tips',
    defaultMessage: 'Warehouse include address and information, Click HEREgo to Warehouse Page',
  }),
  warehouseName: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.warehouse.name',
    defaultMessage: 'Warehouse Name',
  }),
  solutionCode: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.solutionCode',
    defaultMessage: 'Logistics Service',
  }),
  solutionCodeTip: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.solutionCode.tip',
    defaultMessage: 'Logistics Service include Delivery Option, Click HERE go to Delivery Service Page',
  }),
  migrationType: i18n.formatMessage({
    id: 'page.common.sellerConfig.fm.shippingColumns.migrationType',
    defaultMessage: 'Package Profile',
  }),
  'first.mile.type': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.history.fm',
    defaultMessage: 'First Mile Type',
  }),
  'shipping.provider.result': i18n.formatMessage({
    id: 'page.sellerProfile.shipping.provider.shipping.provider.result',
    defaultMessage: 'Shipping Provider',
  }),
  adjustmentTips: {
    threePl: i18n.formatMessage({
      id: 'page.fulfillment.shipping.provider.tips.3pl',
      defaultMessage:
        'Your Shipping Provider will adjust to {threePL},Effective from {dateTime},Click HERE go to Check',
    }),
    FmType: i18n.formatMessage({
      id: 'page.fulfillment.shipping.provider..tips.fm.type',
      defaultMessage: 'Your FM type will adjust to {provider},Effective from {dateTime},Click HERE go to Check',
    }),
  },
};
