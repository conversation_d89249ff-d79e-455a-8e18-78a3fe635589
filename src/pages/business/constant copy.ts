export const BIZ_INFO_GROUPS = {
  // SG: [
  //   {
  //     title: 'Seller Basic',
  //     fieldIds: ['0'],
  //   },
  //   {
  //     title: 'Business Information',
  //     fieldNames: [
  //       'individual_identification_type',
  //       'name_company',
  //       'business_owner_name',
  //       'individual_document_business_info',
  //       'individual_document_business_info_2',
  //     ],
  //     fieldIds: ['277', '3', '274', '64', '276'],
  //   },
  //   {
  //     title: 'Tax Information',
  //     fieldNames: ['individual_vat_registered', 'individual_vat_number', 'vat_registered', 'vat_number'],
  //     fieldIds: ['89', '88', '38', '37'],
  //   },
  //   {
  //     title: 'Address Information',
  //     fieldNames: ['address1', 'individual_address1', 'individual_country', 'individual_location_level_1'],
  //     fieldIds: ['5', '56', '59', '166'],
  //   },
  // ],
  SG: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_citizen_identification_number',
        'document_business_info',
        'individual_document_business_info',
        'individual_document_business_info_2',
        'business_reg_number',
        'business_owner_name',
        'individual_birthday',
        'individual_nationality',
        'country_of_incorporation',
        'date_of_incorporation',
        'directors_nric',
        'directors_nric_1',
        'ubos_nric',
        'ubos_nric_1',
        'ubos_nric_2',
        'ubos_nric_3',
        'document_for_address_verification',
        'individual_document_for_address_verification',
      ],
      fieldIds: [
        '275',
        '277',
        '3',
        '119',
        '13',
        '64',
        '276',
        '12',
        '274',
        '321',
        '864',
        '865',
        '866',
        '323',
        '324',
        '325',
        '326',
        '327',
        '328',
        '331',
        '332',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: ['vat_registered', 'individual_vat_registered', 'vat_number', 'individual_vat_number'],
      fieldIds: ['38', '89', '37', '88'],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'company_province',
      ],
      fieldIds: ['5', '56', '8', '59', '167', '166', '169', '168', '171', '170', '288'],
    },
  ],
  PH: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_name_company',
        'individual_citizen_identification_number',
        'document_business_info',
        'individual_document_business_info',
        'individual_document_business_info_2',
        'business_reg_number',
        'individual_business_reg_number',
        'dti_sec_document',
        'individual_dti_sec_document',
        'document_business_authority_certificate',
        'document_business_Partnership',
        'business_owner_name',
        'person_in_charge',
        'individual_person_in_charge',
        'seller_first_name',
        'individual_seller_first_name',
        'seller_last_name',
        'individual_seller_last_name',
        'individual_Date_of_Birth',
        'individual_Place_of_Birth',
        'individual_permanent_address',
      ],
      fieldIds: [
        '707',
        '709',
        '3',
        '447',
        '512',
        '13',
        '457',
        '708',
        '12',
        '456',
        '18001',
        '16001',
        '711',
        '712',
        '706',
        '11',
        '455',
        '509',
        '508',
        '511',
        '510',
        '557',
        '713',
        '714',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: [
        'vat_registered',
        'individual_vat_registered',
        'vat_number',
        'individual_vat_number',
        'tax_exemption_group_flag',
        'individual_tax_exemption_group_flag',
        'tax_exemption_proof',
        'individual_tax_exemption_proof',
        'estimate_annual_sales_exceed_flag',
        'individual_estimate_annual_sales_exceed_flag',
        'estimate_annual_sales_proof',
        'individual_estimate_annual_sales_proof',
      ],
      fieldIds: ['38', '482', '37', '481', '102', '98', '103', '99', '104', '100', '105', '101'],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'postcode',
        'individual_postcode',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'country_region',
        'individual_country_region',
        'company_province',
      ],
      fieldIds: ['5', '449', '8', '452', '9', '453', '572', '571', '574', '573', '576', '575', '578', '577', '717'],
    },
  ],
  MY: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_name_company',
        'company_type',
        'citizen_identification_number',
        'individual_citizen_identification_number',
        'document_business_info',
        'individual_document_business_info',
        'individual_document_business_info_2',
        'business_reg_number',
        'individual_business_reg_number',
        'business_identification_number',
        'business_owner_name',
        'person_in_charge',
        'individual_person_in_charge',
        'seller_first_name',
        'seller_last_name',
        'msic',
        'individual_nationality',
      ],
      fieldIds: [
        '620',
        '622',
        '3',
        '442',
        '541',
        '860',
        '508',
        '13',
        '452',
        '621',
        '12',
        '451',
        '872',
        '619',
        '11',
        '450',
        '504',
        '506',
        '876',
        '867',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: [
        'sst_registered_flag',
        'individual_sst_registered_flag',
        'sst_registration_number',
        'individual_sst_registration_number',
        'sst_registration_document',
        'individual_sst_registration_document',
        'ttx_registered_flag',
        'individual_ttx_registered_flag',
        'ttx_registration_number',
        'individual_ttx_registration_number',
        'ttx_registration_document',
        'individual_ttx_registration_document',
        'tin_identification_flag',
        'individual_tin_identification_flag',
        'tin_identification_number',
        'individual_tin_identification_number',
        'document_tax_info',
        'individual_document_tax_info',
      ],
      fieldIds: [
        '873',
        '880',
        '874',
        '881',
        '875',
        '882',
        '877',
        '883',
        '878',
        '884',
        '879',
        '885',
        '886',
        '888',
        '887',
        '889',
        '626',
        '625',
      ],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'country_region',
        'individual_country_region',
        'company_province',
      ],
      fieldIds: ['5', '444', '8', '447', '570', '569', '572', '571', '574', '573', '576', '575', '632'],
    },
  ],
  VN: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_name_company',
        'business_type',
        'citizen_identification_number',
        'individual_citizen_identification_number',
        'document_business_info',
        'individual_document_business_info',
        'individual_document_business_info_2',
        'business_reg_number',
        'individual_business_reg_number',
        'business_owner_name',
        'store_owner',
        'seller_first_name',
        'individual_seller_first_name',
        'citizen_id_number_document_1',
        'citizen_id_number_document_2',
        'legal_letter',
        'individual_legal_letter',
        'resident_status',
        'individual_resident_status',
        'mp3_supply_chain_type',
        'individual_mp3_supply_chain_type',
        'mp3_supply_chain_description',
        'individual_mp3_supply_chain_description',
      ],
      fieldIds: [
        '632',
        '634',
        '3',
        '441',
        '539',
        '18001',
        '540',
        '13',
        '451',
        '633',
        '12',
        '450',
        '628',
        '16001',
        '502',
        '503',
        '20001',
        '22001',
        '26001',
        '24001',
        '28001',
        '30001',
        '860',
        '862',
        '861',
        '863',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: [
        'vat_registered',
        'individual_vat_registered',
        'vat_number',
        'individual_vat_number',
        'tax_code',
        'individual_Personal Tax Code',
        'document_tax_info',
        'individual_document_tax_info',
      ],
      fieldIds: ['38', '476', '37', '475', '518', '555', '526', '525'],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'postcode',
        'individual_postcode',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'country_region',
        'individual_country_region',
        'company_province',
      ],
      fieldIds: ['5', '443', '8', '446', '9', '447', '559', '558', '561', '560', '563', '562', '577', '576', '639'],
    },
  ],
  ID: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_name_company',
        'cms_company_type',
        'individual_cms_company_type',
        'Identification_Number',
        'individual_id_number',
        'individual_Id_number2',
        'document_business_info',
        'individual_document_business_info',
        'individual_document_business_info_2',
        'business_reg_number',
        'individual_business_reg_number',
        'business_owner_name',
        'person_in_charge',
        'individual_person_in_charge',
        'individual_nationality',
        'promo_code',
        'individual_promo_code',
        'mp3_supply_chain_type',
        'individual_mp3_supply_chain_type',
        'mp3_supply_chain_description',
        'individual_mp3_supply_chain_description',
      ],
      fieldIds: [
        '235',
        '234',
        '3',
        '56',
        '4',
        '57',
        '6001',
        '159',
        '160',
        '13',
        '66',
        '237',
        '12',
        '65',
        '236',
        '11',
        '64',
        '856',
        '158',
        '157',
        '860',
        '862',
        '861',
        '863',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: ['vat_registered', 'individual_vat_registered', 'vat_number', 'individual_vat_number'],
      fieldIds: ['38', '91', '37', '90'],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'postcode',
        'individual_postcode',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'location_level_4',
        'individual_location_level_4',
        'country_region',
        'individual_country_region',
        'company_province',
      ],
      fieldIds: [
        '5',
        '58',
        '8',
        '61',
        '9',
        '62',
        '189',
        '188',
        '191',
        '190',
        '193',
        '192',
        '289',
        '288',
        '195',
        '194',
        '244',
      ],
    },
  ],
  TH: [
    {
      title: 'Seller Basic',
      fieldIds: ['0'],
    },
    {
      title: 'Business Information',
      fieldNames: [
        'identification_type',
        'individual_identification_type',
        'name_company',
        'individual_name_company',
        'individual_citizen_identification_number',
        'document_business_info',
        'individual_document_business_info',
        'business_reg_number',
        'individual_business_reg_number',
        'business_owner_name',
        'person_in_charge',
        'individual_person_in_charge',
        'individual_Date_of_Birth',
        'authorized_person_id_copy_1',
        'individual_adult_document_1',
        'individual_adult_document_2',
      ],
      fieldIds: [
        '592',
        '591',
        '3',
        '443',
        '508',
        '13',
        '453',
        '12',
        '452',
        '593',
        '11',
        '451',
        '602',
        '858',
        '10001',
        '12001',
      ],
    },
    {
      title: 'Tax Information',
      fieldNames: [
        'vat_registered',
        'individual_vat_registered',
        'vat_number',
        'individual_vat_number',
        'document_vat_info',
        'individual_document_vat_info',
        'vat_invoicing_address',
        'individual_vat_invoicing_address',
        'vat_branch_id',
        'individual_vat_branch_id',
      ],
      fieldIds: ['38', '478', '37', '477', '606', '859', '607', '860', '608', '861'],
    },
    {
      title: 'Address Information',
      fieldNames: [
        'address1',
        'individual_address1',
        'country',
        'individual_country',
        'location_level_1',
        'individual_location_level_1',
        'location_level_2',
        'individual_location_level_2',
        'location_level_3',
        'individual_location_level_3',
        'company_province',
      ],
      fieldIds: ['5', '445', '8', '448', '546', '545', '548', '547', '550', '549', '609'],
    },
  ],
};
