import commonLocale from '../../constants/common-locale';
import React from 'react';
import { axios } from '@alife/workstation-utils';
import { PageContainer } from '@alife/asc-components';
import MRender, { IFieldItem } from '../../components/mrender';
import SettingPageTitle from '../../components/setting-page-title';
import { ASCTools } from '@alife/workstation-utils';
import { Form } from '@formily/core';
import { BIZ_INFO_GROUPS } from './constant';
import BusinessProgress from '../../components/business-progerss';

import './page.scss';
import ItemStatus from '../../components/business-progerss/item-status';

const country = ASCTools.getCountry();

function ResultPage() {
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.businessInformation']} />
      <BusinessProgress />
      <ItemStatus
        label="Business Information"
        icon="https://gw.alicdn.com/imgextra/i2/O1CN01JVR52P1GtwmGHLmEx_!!*************-55-tps-36-36.svg"
      />
      <MRender
        pannelKey="license_panel"
        grouping={BIZ_INFO_GROUPS[country]}
        services={{
          render: () =>
            axios.get('mtop.global.merchant.subaccount.profile.render.lazada', {
              params: {
                groupId: 2,
              },
            }),
          precheck: data => {
            return axios.post('mtop.global.merchant.subaccount.profile.update.precheck.lazada', {
              fieldMap: JSON.stringify(data),
              groupId: 2,
            });
          },
          submit: (data, fieldList?: IFieldItem[]) => {
            const { otpCode, ...rest } = data;
            const params = fieldList?.reduce((result, item) => {
              if (item.uiType === 'Upload') {
                return { ...result, [item.name]: rest[item.name] ?? { deleted: true } };
              } else {
                return { ...result, [item.name]: rest[item.name] };
              }
            }, {});
            return axios.post('mtop.global.merchant.subaccount.profile.update.lazada', {
              fieldMap: JSON.stringify(params),
              otpCode,
              groupId: 2,
            });
          },
        }}
        externalAction={(form: Form<any>) => {
          const individualVat = form.values['476'];
          const corporateVat = form.values['38'];
          if (country === 'VN') {
            // clearValueAfterMatch
            // VN用户需要强制填写vat，存量用户没有vat，则需要清空。
            // individual_vat_registered ---- 476
            // corporate_vat_registered ---- 38
            individualVat === '0' && form.setValues({ ['476']: '' });
            corporateVat === '0' && form.setValues({ ['38']: '' });
          }
        }}
        needTermsAndConditions={
          <div
            dangerouslySetInnerHTML={{
              __html: `${commonLocale['page.common.edit.termsAndConditions']} ${commonLocale['page.common.edit.termsAndConditions.link.ms']}, ${commonLocale['page.common.edit.termsAndConditions.link.privacy']} ${commonLocale['page.common.edit.termsAndConditions.and']} ${commonLocale['page.common.edit.termsAndConditions.link.agreement']}.`,
            }}
          />
        }
      />
    </PageContainer>
  );
}

export default ResultPage;
