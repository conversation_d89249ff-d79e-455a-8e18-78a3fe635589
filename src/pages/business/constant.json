{"SG": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["vat_registered", "name_company", "individual_vat_registered", "document_business_info", "individual_document_business_info", "individual_document_business_info_2", "identification_type", "individual_identification_type", "business_reg_number"], "fieldIds": ["38", "3", "89", "13", "64", "276", "275", "277", "12"]}, {"title": "Address Information", "fieldNames": ["address1", "location_level_1", "individual_location_level_1", "individual_address1", "company_province"], "fieldIds": ["5", "167", "166", "56", "288"]}], "PH": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["identification_type", "individual_identification_type", "business_reg_number", "individual_name_company", "individual_business_reg_number", "individual_document_business_info", "individual_document_business_info_2"], "fieldIds": ["707", "709", "12", "447", "456", "457", "708"]}, {"title": "Tax Information", "fieldNames": ["vat_number", "vat_registered"], "fieldIds": ["37", "38"]}, {"title": "Address Information", "fieldNames": ["address1", "country", "postcode", "ewallet_bank_statement", "operation_country", "individual_location_level_1", "location_level_1", "individual_location_level_2", "location_level_2", "individual_country_region", "country_region", "individual_address1", "individual_country", "individual_postcode", "individual_permanent_address", "company_province"], "fieldIds": ["5", "8", "9", "761", "800", "571", "572", "573", "574", "577", "578", "449", "452", "453", "714", "717"]}], "MY": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["name_company", "business_reg_number", "document_business_info", "citizen_identification_number", "individual_name_company", "individual_business_reg_number", "individual_document_business_info", "identification_type", "individual_document_business_info_2", "individual_identification_type", "individual_citizen_identification_number"], "fieldIds": ["3", "12", "13", "860", "442", "451", "452", "620", "621", "622", "508"]}, {"title": "Tax Information", "fieldNames": ["sst_registration_number", "individual_sst_registration_number", "sst_registered_flag", "sst_registration_document", "ttx_registration_document", "individual_sst_registered_flag", "individual_sst_registration_document", "individual_ttx_registration_document", "tin_identification_flag", "tin_identification_number", "individual_tin_identification_flag", "individual_tin_identification_number", "individual_document_tax_info", "document_tax_info", "individual_ttx_registered_flag", "ttx_registered_flag"], "fieldIds": ["874", "881", "873", "875", "879", "880", "882", "885", "886", "887", "888", "889", "625", "626", "883", "877"]}, {"title": "Address Information", "fieldNames": ["address1", "individual_address1", "location_level_1", "individual_location_level_1", "location_level_3", "individual_location_level_3"], "fieldIds": ["5", "444", "570", "569", "574", "573"]}], "VN": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["name_company", "business_type", "business_reg_number", "document_business_info", "individual_name_company", "individual_business_reg_number", "individual_document_business_info", "individual_document_business_info_2", "individual_identification_type", "individual_vat_number"], "fieldIds": ["3", "539", "12", "13", "441", "450", "451", "633", "634", "475"]}, {"title": "Tax Information", "fieldNames": ["vat_number", "vat_registered", "individual_vat_registered", "individual_document_tax_info", "document_tax_info"], "fieldIds": ["37", "38", "476", "525", "526"]}, {"title": "Address Information", "fieldNames": ["ewallet_bank_statement", "address1", "operation_country", "country", "postcode", "individual_location_level_1", "location_level_1", "individual_country_region", "country_region", "individual_address1", "individual_country", "individual_postcode", "company_province"], "fieldIds": ["682", "5", "800", "8", "9", "558", "559", "576", "577", "443", "446", "447", "639"]}], "ID": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["name_company", "business_reg_number", "document_business_info", "individual_id_number", "individual_Id_number2", "individual_name_company", "individual_business_reg_number", "individual_document_business_info", "individual_identification_type", "identification_type", "individual_document_business_info_2"], "fieldIds": ["3", "12", "13", "159", "160", "56", "65", "66", "234", "235", "237"]}, {"title": "Tax Information", "fieldNames": ["vat_number", "vat_registered", "individual_vat_number", "individual_vat_registered"], "fieldIds": ["37", "38", "90", "91"]}, {"title": "Address Information", "fieldNames": ["address1", "ewallet_bank_statement", "country", "operation_country", "postcode", "individual_location_level_1", "location_level_1", "individual_location_level_2", "location_level_2", "individual_country_region", "country_region", "individual_address1", "individual_country", "individual_postcode", "company_province"], "fieldIds": ["5", "294", "8", "800", "9", "188", "189", "190", "191", "194", "195", "58", "61", "62", "244"]}], "TH": [{"title": "Seller Basic", "fieldIds": ["0"]}, {"title": "Business Information", "fieldNames": ["name_company", "business_reg_number", "document_business_info", "individual_name_company", "individual_identification_type", "individual_business_reg_number", "individual_document_business_info"], "fieldIds": ["3", "12", "13", "443", "591", "452", "453"]}, {"title": "Tax Information", "fieldNames": ["individual_document_vat_info", "vat_number", "vat_registered", "document_vat_info", "individual_vat_number", "individual_vat_registered"], "fieldIds": ["859", "37", "38", "606", "477", "478"]}, {"title": "Address Information", "fieldNames": ["address1", "country", "individual_location_level_1", "location_level_1", "individual_location_level_3", "location_level_3", "individual_vat_invoicing_address", "individual_address1", "individual_country", "vat_invoicing_address", "company_province", "ewallet_bank_statement", "operation_country"], "fieldIds": ["5", "8", "545", "546", "549", "550", "860", "445", "448", "607", "609", "657", "800"]}]}