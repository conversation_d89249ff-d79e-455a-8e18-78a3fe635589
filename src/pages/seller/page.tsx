import commonLocale from '../../constants/common-locale';
import React from 'react';
import { axios } from '@alife/workstation-utils';
import { PageContainer } from '@alife/asc-components';
import MRender from '../../components/mrender';
import SettingPageTitle from '../../components/setting-page-title';
import 'uno.css';
import './page.scss';

// 这里枚举other_contact_phone/email的filedNames
// 页面右下角提交按钮时，不携带这些字段的值
// 手动去除是因为不需要传这些字段，且如果传递会导致存储到后端的数据不正确
const FILTERED_FIELDS_MAPS = [
  '862',
  '863',
  '864',
  '865',
  '856',
  '857',
  '858',
  '859',
  '868',
  '869',
  '870',
  '871',
  '864',
  '865',
  '866',
  '867',
  '864',
  '865',
  '867',
  '868',
  '856',
  '857',
  '858',
  '859',
];
function ResultPage() {
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.sellerProfile']} />

      <MRender
        pannelKey="seller_panel"
        services={{
          render: () =>
            axios.get('mtop.global.merchant.subaccount.profile.render.lazada', {
              params: {
                groupId: 1,
              },
            }),
          precheck: data => {
            return axios.post('mtop.global.merchant.subaccount.profile.update.precheck.lazada', {
              fieldMap: JSON.stringify(data),
              groupId: 1,
            });
          },
          submit: data => {
            const { otpCode, ...rest } = data;
            const fieldsMap = {};
            Object.keys(rest)
              ?.filter(item => !FILTERED_FIELDS_MAPS.includes(item))
              ?.map(item => {
                fieldsMap[item] = rest[item];
              });
            return axios.post('mtop.global.merchant.subaccount.profile.update.lazada', {
              fieldMap: JSON.stringify(fieldsMap),
              otpCode,
              groupId: 1,
            });
          },
        }}
      />
    </PageContainer>
  );
}

export default ResultPage;
