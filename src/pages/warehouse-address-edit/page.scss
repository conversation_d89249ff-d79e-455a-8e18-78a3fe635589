.warehouse-address-edit-page {
  .warehouse-address-select {
    .amap-copyright {
      display: none !important;
    }
    .amap-logo {
      display: none !important;
    }
    position: relative;
    .info-panel {
      overflow-y: auto;
      // position: absolute;
      margin: 10px 0 0 4px;
      padding: 10px;
      box-shadow: 2px 2px 16px 0 rgba(0, 0, 0, 0.23);
      background-color: #ffffff;
      z-index: 1;
      border-radius: 8px;
      .next-formily-layout {
        min-height: 400px;
        width: 100%;
      }
      .field-sub-label {
        color: #e64a4a;
        margin-right: 4px;
        display: inline-block;
        font-family: SimSun, sans-serif;
      }
      // .next-search-simple.next-normal .next-search-left {
      //   border-color: transparent !important;
      // }
      .next-formily-item-control-content-component {
        border-radius: 8px;
      }
      .next-formily-item {
        margin-bottom: 5px;
      }
      .next-formily-item-feedback-layout-loose {
        margin-bottom: 5px;
      }
      .district-field-name {
        .next-input.next-medium {
          height: 44px;
        }
        .next-input.next-medium .next-input-text-field {
          height: 40px;
          em {
            white-space: normal;
            line-height: 20px;
          }
        }
      }
    }
    .threepf-advertise-content {
      display: inline-block;
      width: 100%;
      a {
        cursor: pointer;
      }
      img {
        border-radius: 16px;
      }
    }
  }

  .asc-seller-amap-wrap {
    height: 35vw;
    padding: 0px;
    margin: 0px;
    width: 100%;
    position: relative;

    .controls-bar {
      z-index: 1;
      position: absolute;
      right: 30px;
      bottom: 30px;
      text-align: center;
      .location {
        background-color: #ffffff;
        display: inline-block;
        img {
          width: 35px;
          height: 35px;
        }
      }
      i {
        background-color: #ffffff;
        padding: 5px;
        margin: 2px;
      }
    }

    .custom-content-marker {
      text-align: center;
      .marker-content {
        padding: 8px 20px;
        min-width: 80px;
        white-space: nowrap;
        background-color: #1a71ff;
        border-radius: 15px;
        font-size: 12px;
        color: #ffffff;
        .marker-title {
          text-align: left;
          font-size: 14px;
        }
        .confirm-button {
          height: 18px;
          margin-top: 8px;
          text-align: right;
          .map-button {
            padding: 5px;
            background-color: #ffffff;
            color: #1a71ff;
            border-radius: 8px;
          }
        }
      }
      .line {
        display: inline-block;
        height: 30px;
        width: 4px;
        background-color: #1a71ff;
      }
    }
  }
}

.warehouse-address-floating-tip-wrap {
  position: relative;
  .warehouse-address-floating-tip {
    position: absolute;
    right: 0;
    i {
      cursor: pointer;
    }
    i::before {
      font-size: 14px;
    }
  }
}
