import React, { useRef, useState } from 'react';
import commonLocale from '../../constants/common-locale';
import { PageContainer, SafeHtmlText } from '@alife/asc-components';
import Locale from '../../constants/common-locale';
import * as i18n from '@alife/lazada-i18n';
import useUrlState from '@ahooksjs/use-url-state';
import { ASCTools, When } from '@alife/workstation-utils';
import { Card, Loading, Message, Grid, Box } from '@alifd/next';
import localeWarehouse from '../../constants/warehouse';
import SettingPageTitle from '../../components/setting-page-title';
import { useSellerMap } from '@alife/asc-logistics-components';
import { MapControls } from './components/controls';
import AddressForm from './add-address';
import getDescription from './components/description';
import { MarkerProps } from './interfaces';
import { FormProps } from '@formily/next';
import { confirmClick } from '../../utils/map-tool';
import { setPosition, markerContent } from './utils';
import {
  diffDistrict,
  getPostChain,
  setAttribute,
  setDistrictField,
  showDescription,
  longLatGeoCode,
} from './district-utils';
import { getCurrentUserType, getRcodeByLngLat, getUserInfo } from './service';

import './page.scss';
import { useRequest } from 'ahooks';
import { getCBData } from '../../services/cb';

const Row = Grid.Row;
const Col = Grid.Col;
const country = ASCTools.getCountry();

/**
 * Write to Form
 * @param form
 * @param code
 * @param addr
 */
function writeAddress(
  form,
  poi,
  chain,
  addr?: string,
  createMarker?: any,
  pos?: Array<number>,
  diffDis?: boolean | string,
) {
  if (!form || !form['fields']) {
    return;
  }
  const setValue = () => {
    // 缓存当前行政区信息并设置行政区
    setDistrictField(form, getDescription(localeWarehouse['district.select.tip']), chain);

    // 设置地图poi符号和设置地址及其描述
    if (!!poi) {
      !!pos && createMarker({ title: poi, content: addr, pos, showConfirm: false });
      setAttribute({ obj: form, attr: 'pinLocation', v: poi });
      ['MY', 'TH', 'VN'].includes(country || '') && setAttribute({ obj: form, attr: 'address1', v: poi });
      showDescription(form['fields'], getDescription(localeWarehouse['input.floor.unit']));
    }
  };

  return () => {
    setValue();
  };
}

function ResultPage() {
  const currentBu = ASCTools.getBU();
  const { data: CB, loading } = useRequest(currentBu === 'DARAZ' ? getUserInfo : getCBData);
    const { data: is3PFUser } = useRequest(getCurrentUserType);
  const [params] = useUrlState<{ addressId: string; groupId: string; code?: string }>();
  let form: FormProps | null = null;
  const marker = useRef<any>(null);

  const setForm = v => {
    form = v;
  };

  const mapClickInfo = pos => {
    // SG不支持地图点选
    if (country === 'SG') return;
    if (['MY', 'TH', 'VN'].includes(country || '')) {
      setAttribute({ obj: form, attr: 'address1', v: '' });
    }
    setAttribute({ obj: form, attr: 'pinLocation', v: '' });
    setAttribute({ obj: form, attr: 'district', v: '' });
    showInfoOnMap({ lngLat: pos });
  };

  const CMarker = ({ title = '', content = '', pos = undefined, showConfirm = true }: MarkerProps) => {
    const center = map?.getCenter();
    if (!pos) {
      pos = [center?.lng, center?.lat];
    }
    marker.current && removeMarker(marker.current);
    marker.current = createMarker(
      markerContent({ title, content, showConfirm, btnText: Locale['page.common.btn.confirm'] }),
      pos,
    );
  };

  const initMarker = () =>
    country !== 'SG' &&
    CMarker({
      title: i18n.formatMessage({
        id: 'page.warehouse.address.edit.map.drag',
        defaultMessage: 'Click On Map To Pin',
      }),
      showConfirm: false,
    });

  const [Element, { zoomIn, zoomOut, map, createMarker, removeMarker, getLocation: mapGetLocation }] = useSellerMap({
    mapClick: mapClickInfo,
    className: 'asc-seller-amap-wrap',
    mapMounted: initMarker,
  });

  const showInfoOnMap = (
    { lngLat: pos = [], placeId, uuid }: { lngLat: Array<number>; placeId?: string; uuid?: string },
    fromAddress?: boolean,
  ) => {
    const fields = (form && form['fields']) || {};
    // Get Rcode by LngLat position
    getRcodeByLngLat({ lngLat: pos, placeId, uuid })
      ?.then(result => {
        const data = result?.data?.data;
        if (!data) {
          Message.warning(localeWarehouse['address.not.found']);
          return;
        }
        const chain = getPostChain(data);
        const addr = data?.address;
        const poi = data?.addressTitle;

        setPosition(
          map,
          CMarker,
          data.longitude || pos?.[0],
          data.latitude || pos?.[1],
          longLatGeoCode,
          poi,
          addr,
          !fromAddress,
        );

        if (form) {
          if (fromAddress) {
            setDistrictField(form, getDescription(localeWarehouse['district.select.tip']), chain);
            if (!!poi) {
              setAttribute({ obj: form, attr: 'pinLocation', v: poi });
              ['MY', 'TH', 'VN'].includes(country || '') && setAttribute({ obj: form, attr: 'address1', v: poi });
              showDescription(fields, getDescription(localeWarehouse['input.floor.unit']));
            }
            return;
          }
          // Invoke when confirm on map
          !!poi && confirmClick(writeAddress(form, poi, chain, addr, CMarker, pos));
        }
      })
      .catch(e => console.log(e));
  };

  const geoLocation = () => {
    mapGetLocation()
      .then(res => {
        if (Array.isArray(res)) {
          showInfoOnMap({ lngLat: res });
        }
      })
      .catch(e => console.error(e));
  };

  // Return address and cb don't need map
  const isCB = CB === false ? false : true;
  const needShowMap = !isCB && params?.groupId !== '11';

  return (
    <PageContainer className="warehouse-address-edit-page">
      <SettingPageTitle title={commonLocale['page.common.nav.warehouse']} />
      <Card>
        <Loading style={{ width: '100%', minHeight: '200px' }} visible={loading}>
          <Box className="warehouse-address-select" spacing={[16, 0]}>
            <Row gutter={10}>
              <Col span={needShowMap ? 7 : 12}>
                <div className="info-panel">
                  <AddressForm
                    map={map}
                    createMarker={CMarker}
                    params={params}
                    showInfoOnMap={showInfoOnMap}
                    setForm={setForm}
                    isCB={CB}
                    is3PFUser={is3PFUser}
                  />
                </div>
              </Col>
              <Col hidden={!needShowMap}>
                <Element>
                  <MapControls searchLocation={geoLocation} zoomIn={zoomIn} zoomOut={zoomOut} />
                </Element>
              </Col>
            </Row>
            <Row>
              <When condition={is3PFUser}>
                <Card
                  title={i18n.formatMessage({
                    id: 'page.warehouse.address.edit.3pf.title',
                    defaultMessage: 'Recommendation 3pf Warehouse',
                  })}
                >
                  <SafeHtmlText
                    className="threepf-advertise-content"
                    html={i18n.formatMessage({
                      id: 'page.warehouse.address.edit.3pf.advertise',
                      defaultMessage: `<a href="https://servicemarket.lazada-seller.cn/?id=1226#/lazglobal" target="_blank" rel="noopener noreferrer">
        <img width="100%" height="auto" src="https://img.alicdn.com/imgextra/i4/O1CN018RpUPb1XV4jbUUncx_!!6000000002928-2-tps-2559-646.png" alt="3PF广告" />
        </a>`,
                    })}
                  />
                  .
                </Card>
              </When>
            </Row>
          </Box>
        </Loading>
      </Card>
    </PageContainer>
  );
}

export default ResultPage;
