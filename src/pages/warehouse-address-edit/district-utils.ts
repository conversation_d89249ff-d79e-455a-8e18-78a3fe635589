import { ASCTools } from '@alife/workstation-utils';
import { getLocationAddress } from './service';
import { dataSource } from './components/select';
import { ReactNode } from 'react';
export let DistrictCache = {};

// Cache district code when district field is not exist
export const cacheDistrictCode: { current: undefined | string } = {
  current: '',
};

// Cache geoCode
export const longLatGeoCode: { current: Array<number> | null } = {
  current: null,
};

/**
 * set District
 * @param arr
 */
export function setDistrict(arr) {
  arr = arr || [];
  const firstEl = arr[0];
  if (Object.prototype.toString.call(firstEl) === '[object Object]') {
    const district = arr.find(ele => ele?.name === 'district');
    const value = district?.value;
    const codes = value?.split('-');
    setDistrict(codes);
    return;
  }
  DistrictCache = {};
  arr?.map((el, index) => {
    DistrictCache[`locationLevel${index + 1}`] = el;
  });
}

/**
 * Diff district if is deferent
 * @param arr
 */
export function diffDistrict(arr) {
  if (!Object.keys(DistrictCache)?.length) {
    return 'add';
  }
  arr = arr || [];
  let flag = false;
  // if (!Object.keys(DistrictCache).length) flag = true;
  Object.keys(DistrictCache).forEach((el, index) => {
    if (DistrictCache[el] !== arr[index]) flag = true;
  });
  return flag;
}

/**
 * Get label and value chain from array or object
 * @param data
 */
export const getChain = (data): { value: string; label: string } => {
  let chain = '';
  let chainValue = '';
  const isArr = Array.isArray(data);
  const levels = isArr ? data : Object.keys(data);
  levels?.forEach((el, index) => {
    const element = isArr ? el : data[el];
    const last = index + 1 === levels?.length;
    chain += (element?.name || element?.label) + (last ? '' : '/');
    chainValue += (element?.locationId || element?.value) + (last ? '' : '-');
  });
  return {
    value: chainValue,
    label: chain,
  };
};

/**
 * Get label and value chain
 * @param data
 */
export const getPostChain = data => {
  const {
    countryLevelId = '',
    countryLevelName = '',
    locationLevel1Label = '',
    locationLevel2Label = '',
    locationLevel3Label = '',
    locationLevel4Label = '',
    locationLevel5Label = '',
    locationLevel1 = '',
    locationLevel2 = '',
    locationLevel3 = '',
    locationLevel4 = '',
    locationLevel5 = '',
  } = data;
  const labelArr = [
    countryLevelName,
    locationLevel1Label,
    locationLevel2Label,
    locationLevel3Label,
    locationLevel4Label,
    locationLevel5Label,
  ].filter(el => !!el);
  const valueArr = [
    countryLevelId,
    locationLevel1,
    locationLevel2,
    locationLevel3,
    locationLevel4,
    locationLevel5,
  ].filter(el => !!el);
  return {
    label: labelArr.join('/'),
    value: valueArr.join('-'),
    levelArr: valueArr,
  };
};

/**
 * Format data for district
 * @param data
 */
export const formatDistrict = data => {
  const isArr = Array.isArray(data);
  if (!isArr) return [];
  return data.map(el => {
    return {
      label: el?.nameLocal,
      value: el?.locationId,
      isLeaf: !el?.hasChildren,
      ...el,
    };
  });
};

/**
 * Add district dataSource
 * @param addressTree
 * @param formatData
 * @param pos
 * @param isLeave if is leave
 */
export const addChildren = (addressTree, formatData, pos) => {
  let position = pos?.split('-');
  position?.shift();

  let len: number | string = position?.length - 1;
  let data = addressTree?.current || [];
  position?.map((el, index) => {
    if (index === len && !!data?.[el]) data[el].children = formatData;
    data = data?.[el]?.children;
  });
};

/**
 * Update current data
 * @param addressTree
 * @param pos
 */
export const updateChild = (addressTree, pos) => {
  let position = pos?.split('-');
  position?.shift();

  const data = addressTree?.current || [];
  const len = position?.length - 1;

  let child = {};
  position?.map((el, index) => {
    child = index === 0 ? data[el] : (child['children'] && child['children'][el]) || [];
    if (len === index && !!child) child['isLeaf'] = true;
  });
  addressTree.current = data;
  return addressTree;
};

/**
 * Format resource for component
 * @param data
 */
export function formatSources(data) {
  if (!Array.isArray(data)) return [];
  return data?.map(el => {
    return {
      label: el?.address || el?.nameLocal,
      value: el?.address || el?.nameLocal,
      title: el?.addressTitle,
      lngLat: [el?.geographyCoordinateDTO?.longitude, el?.geographyCoordinateDTO?.latitude],
      placeId: el?.placeId,
      uuid: el?.uuid,
    };
  });
}

/**
 * Set form fields attribute
 * @param obj fields
 * @param attr attribute
 * @param v value
 */
export function setAttribute({
  obj,
  attr,
  v,
  label,
  valueChain,
  from,
}: {
  obj: any;
  attr: string;
  v?: string;
  label?: string | ReactNode;
  valueChain?: string;
  from?: 'value' | 'click' | 'init';
}) {
  const fields = obj && obj['fields'];
  // if Field is disabled cant set value
  if (!fields || !attr || !fields[attr] || (!!fields[attr]?.disabled && from !== 'init')) return;

  // Set form item title
  !!label && fields[attr] && fields[attr]['setTitle'] && fields[attr]['setTitle'](label);

  const setValue = (itemValue?: string) => {
    if (!fields[attr]) return;
    const value = valueChain?.split?.('-')?.slice(-1)?.[0];
    obj?.setValues({ [attr]: itemValue || value, valueID: valueChain });
  };

  if (['district', 'countryRegion'].includes(attr) && !!valueChain) {
    // cache district first
    if (!!valueChain) cacheDistrictCode.current = valueChain;
    // if from click select already have dataSource, don't need query again
    if (from === 'click') {
      setValue();
      return;
    }
    // query dataSource by current districts RCode
    return getCurrentDataSource(fields, valueChain, attr, itemValue => {
      setValue(itemValue);
    });
  }
  // When there is no value, clear description
  if (attr === 'pinLocation' && !v) {
    showDescription(fields, '');
  }
  v !== undefined && obj?.setValues({ [attr]: v });
}

/**
 * Show expand data by value chain
 */
export const getCurrentDataSource = (fields = {}, valueChain, name, callBack) => {
  let timeID: null | number = null;
  // Waiting for dataSource Ready
  if (!dataSource.current?.length) {
    if (timeID) {
      clearTimeout(timeID);
      timeID = null;
    }
    timeID = setTimeout(() => getCurrentDataSource(fields, valueChain, name, callBack), 1000);
    return;
  }

  const valueArr = valueChain?.split('-')?.filter(el => !!el);
  // district need delete country code to get region code
  name === 'district' && valueArr?.shift();
  let tempData: Array<{ label?: string; value?: string; children?: Array<Object> }> = dataSource.current?.slice(0);
  let pos = '0';

  const setData = async () => {
    if (!!valueArr?.length) {
      const el = valueArr?.shift();
      const index = tempData?.findIndex(ele => el === ele?.['locationId']);
      if (index !== -1) {
        pos += `-${index}`;
      } else {
        return 'none';
      }
      return getDistrictData(fields, el, dataSource, pos).then(res => {
        if (!valueArr?.length || !res?.length) {
          !!callBack && callBack(el);
          return;
        }
        if (Array.isArray(res)) {
          tempData = res;
          return setData();
        }
      });
    }
  };
  return setData();
};

/**
 * Set component props
 * @param fields
 * @param value
 * @param attr
 */
export function setFieldProps(fields, value, attr = 'pinLocation') {
  fields[attr] && fields[attr]['setComponentProps'] && fields[attr]['setComponentProps'](value);
}

/**
 * Get district dataSource
 * @param fields
 * @param locationId
 */
export function getDistrictData(fields, locationId, addressTree, pos?: string, name = '') {
  return getLocationAddress(locationId).then(async result => {
    const data = result?.data?.data || [];
    if (!data?.length && fields[name] && fields[name]['setValue']) {
      return [];
    }
    const formatData = formatDistrict(data);
    if (!addressTree?.current?.length) {
      addressTree.current = formatData;
    } else {
      addChildren(addressTree, formatData, pos);
    }
    !!formatData?.length && setFieldProps(fields, { dataSource: addressTree.current }, 'district');
    return formatData;
  });
}

export const showDescription = (fields, des, attr = 'address1') => {
  if (!!fields[attr] && !!fields[attr]['setDescription']) {
    fields[attr]['setDescription'](des);
  }

  // return if have on desc
  if (!des) return;
  // get focus
  const input = document.getElementById('warehouse-address-edit-map');
  if (input) {
    input?.focus();
    setTimeout(() => {
      input['setSelectionRange'] && input['setSelectionRange'](0, 0);
    }, 10);
  }
};

export const setDistrictField = (form, description, chain) => {
  if (!chain.levelArr) {
    return;
  }
  // setDistrict(chain.levelArr);
  // ID 没有五级地址的话 提示选择五级
  if (ASCTools.getCountry() === 'ID' && chain?.levelArr?.length < 5) {
    showDescription(form['fields'], description, 'district');
  }
  setAttribute({ obj: form, attr: 'district', v: chain.label, valueChain: chain.value });
};

export const districtSelect = (form, v, data, extra, countryCodeName) => {
  const path = extra?.selectedPath;
  const { locationId } = countryCodeName;
  !!locationId && path.unshift(countryCodeName);
  const chain = getChain(path);
  // 保存行政区
  // setDistrict(chain.value?.split('-'));
  setAttribute({ obj: form, attr: 'district', v: chain.label, valueChain: chain.value, from: 'click' });
};
