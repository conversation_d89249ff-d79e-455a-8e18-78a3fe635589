import { MinMaxResult, LevelsResult, LongLatResult } from './interfaces';
import { ASCTools } from '@alife/workstation-utils';
import SelectItem, { dataSource } from './components/select';
import { Search, CascaderSelect } from '@alifd/next';
import { postCodeChange } from './postcode-utils';
import { mapFieldProps } from './interfaces';
import localeWarehouse from '../../constants/warehouse';
import { districtSelect, getDistrictData, setFieldProps } from './district-utils';
import { debounce } from '../../utils/tools';
import noop from 'lodash-es/noop';
import { Checkbox } from '@formily/next';

export const venturePhone = (): { maxLength: number; minLength: number; code?: number } => {
  const country = ASCTools.getCountry();
  switch (country) {
    case 'ID':
      return {
        maxLength: 13,
        minLength: 9,
        code: 62,
      };
    case 'MY':
      return {
        maxLength: 10,
        minLength: 9,
        code: 60,
      };
    case 'PH':
      return {
        maxLength: 10,
        minLength: 9,
        code: 63,
      };
    case 'SG':
      return {
        maxLength: 8,
        minLength: 8,
        code: 65,
      };
    case 'TH':
      return {
        maxLength: 9,
        minLength: 8,
        code: 66,
      };
    case 'VN':
      return {
        maxLength: 11,
        minLength: 8,
        code: 84,
      };
    case 'PK':
      return {
        maxLength: 20,
        minLength: 1,
        code: 92,
      };
    case 'BD':
      return {
        maxLength: 20,
        minLength: 1,
        code: 880,
      };
    case 'LK':
      return {
        maxLength: 20,
        minLength: 1,
        code: 94,
      };
    case 'NP':
      return {
        maxLength: 20,
        minLength: 1,
        code: 977,
      };
    case 'MM':
      return {
        maxLength: 20,
        minLength: 1,
        code: 95,
      };
    default:
      return {
        maxLength: 11,
        minLength: 1,
        code: 86,
      };
  }
};

export const venturePostcode = () => {
  const country = ASCTools.getCountry();
  switch (country) {
    case 'ID':
      return {
        maxLength: 5,
        minLength: 5,
        code: 62,
        reg: new RegExp('^0.*'),
      };
    case 'MY':
      return {
        maxLength: 5,
        minLength: 5,
        code: 60,
      };
    case 'PH':
      return {
        maxLength: 4,
        minLength: 4,
        code: 63,
      };
    case 'SG':
      return {
        maxLength: 6,
        minLength: 6,
        code: 65,
      };
    case 'TH':
      return {
        maxLength: 5,
        minLength: 5,
        code: 66,
        reg: new RegExp('^0.*'),
      };
    case 'VN':
      return {
        maxLength: 6,
        minLength: 6,
        code: 84,
      };
    case 'PK':
      return {
        maxLength: 20,
        minLength: 1,
        code: 92,
      };
    case 'BD':
      return {
        maxLength: 20,
        minLength: 1,
        code: 880,
      };
    case 'LK':
      return {
        maxLength: 20,
        minLength: 1,
        code: 94,
      };
    case 'NP':
      return {
        maxLength: 20,
        minLength: 1,
        code: 977,
      };
    case 'MM':
      return {
        maxLength: 20,
        minLength: 1,
        code: 95,
      };
    default:
      return {
        maxLength: 11,
        minLength: 1,
        code: 86,
      };
  }
};

const countryPhoneValidate = venturePhone();
const countryPostcodeValidate = venturePostcode();

export const getLongLat = (elements): LongLatResult => {
  const addr = elements?.find(el => el && el['name'] === 'address1');
  const LngLat = (addr && addr['extData']) || {};
  const value = addr && addr['value'];
  const latitude = LngLat['latitude'];
  const longitude = LngLat['longitude'];
  const placeId = addr?.placeId;
  const uuid = addr?.uuid;
  return { latitude, longitude, value, placeId, uuid };
};

export const setPosition = (
  map,
  createMarker,
  longitude,
  latitude,
  longLatGeoCode,
  title,
  content,
  showConfirm?: boolean,
) => {
  if (createMarker && longitude && latitude) {
    const center = [longitude, latitude];
    longLatGeoCode.current = center;
    createMarker({ title, content, pos: center, showConfirm });
    map?.setCenter(center);
  }
};

export const setAddress = (elements): string => {
  const addrId = elements?.find(el => el && el['name'] === 'addressId');
  let addressID = addrId?.value;
  return addressID;
};

export const setLevels = (rCodeArr: Array<string>): LevelsResult => {
  const levels = {};
  rCodeArr?.map((el, index) => {
    if (!!el) {
      index === 0 ? (levels['country'] = el) : (levels[`locationLevel${index}`] = el);
    }
  });
  return levels;
};

/**
 * Field verify
 * @param required
 * @param v
 */
export const verifyFiled = (
  required: boolean,
  v: string,
  callback?: () => boolean | { type: string; message: string },
  locale?: any,
) => {
  if (!!required && !v)
    return {
      type: 'error',
      message: locale?.['required'] || 'required',
    };
  if (!required && !v) return true;
  return !!callback ? callback() : true;
};

export const phoneValidate = (
  required,
  v,
  localeWarehouse,
  isCB: boolean,
  countryPhoneValidate: { maxLength: number; minLength: number; code?: number },
) => {
  return verifyFiled(
    required,
    v,
    () => {
      if (
        v?.length > (!isCB ? countryPhoneValidate?.maxLength : 20) ||
        v?.length < (!isCB ? countryPhoneValidate?.minLength : 1)
      )
        return {
          type: 'error',
          message: localeWarehouse['phone.too.long'] || 'Phone is not right',
        };
      const number = /^[0-9]*$/;
      const numberReg = number.test(v);

      // Start +
      let addReg = false;
      if (v.startsWith('+')) {
        addReg = number.test(v.replace('+', ''));
      }
      // tel reg
      // const tel = /^0\d{2,3}-?\d{7,8}$/;
      if (!(numberReg || addReg))
        return {
          type: 'error',
          message: localeWarehouse['phone.verify'] || 'Verify Error',
        };
      return true;
    },
    localeWarehouse,
  );
};

export const emailValidate = (required, v, localeWarehouse) => {
  return verifyFiled(required, v, () => {
    const mailReg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,6})+$/;
    if (!mailReg.test(v))
      return {
        type: 'error',
        message: localeWarehouse['email.verify'] || 'Uncorrect',
      };
    return true;
  });
};

export const postCodeValidate = (required, v, countryPostcodeValidate, isCB, localeWarehouse) => {
  return verifyFiled(required, v, () => {
    const reg = /^[0-9]*$/;
    // Can't start with 0 validate
    if (!!countryPostcodeValidate?.reg && !isCB) {
      if (countryPostcodeValidate?.reg?.test(v))
        return {
          type: 'error',
          message: localeWarehouse['can.not.start.with.zero'] || 'Can not start with zero',
        };
    }
    // Length validate
    if ((v?.length < countryPostcodeValidate?.minLength || v?.length > countryPostcodeValidate?.maxLength) && !isCB)
      return {
        type: 'error',
        message: localeWarehouse['post.code.length'] || 'Post code is not right',
      };
    return reg.test(v);
  });
};

export const commonValidate = (required, v, name, localeWarehouse) => {
  return verifyFiled(required, v, () => {
    if (name === 'address1') {
      let message = '';
      if (/^[\d\s!@#$%^&*()_+\-=[\]{};':"\\|,.<>\/?]*$/.test(v)) {
        message = localeWarehouse['address.charactor.tip'] || 'Address must contain charactor';
      }
      if (!/(?=.*[0-9])/.test(v) && !message) {
        message = localeWarehouse['address.contain.number'];
      }
      if (v?.length > 350 && !message) message = localeWarehouse['address.too.long'] || 'Address is too long';
      if (v?.length < 10 && !message) {
        message = localeWarehouse['address.too.short'] || 'Address is too short';
      }
      if (!!message) {
        return {
          type: 'error',
          message,
        };
      }
    }
    return true;
  });
};

export const getPostCodeMaxMinLength = (countryPostcodeValidate, isCB: boolean): MinMaxResult => {
  const minMax: { maxLength?: number; minLength?: number } = {};

  if (!isCB) {
    minMax.maxLength = countryPostcodeValidate?.maxLength || 50;
    minMax.minLength = countryPostcodeValidate?.minLength || 1;
  } else {
    minMax.maxLength = 50;
    minMax.minLength = 1;
  }

  return minMax;
};

export const getCommonMaxMinLength = (name, placeholder): MinMaxResult => {
  const minMax: { maxLength?: number; minLength?: number; placeholder?: string } = {};

  if (name === 'address1') {
    minMax.placeholder = placeholder;
    minMax.maxLength = 352;
    minMax.minLength = 10;
  } else {
    minMax.maxLength = 100;
    minMax.minLength = 1;
  }

  return minMax;
};

export const districtSelectName = (dataSource, isCB, name, transferName) => {
  return dataSource?.map(el => {
    if (['district', 'countryRegion'].includes(el?.name)) {
      el.name = !!isCB ? transferName : name;
    }
    return el;
  });
};

/**
 * AMap Marker Content from RichText
 * @param param0
 * @returns
 */
export const markerContent = ({ title, content, showConfirm, btnText }) => {
  return `<div class="custom-content-marker">
  <div class="marker-content">
    <div class="marker-title">
    ${title}
    </div>
    ${content}
    ${
      !!showConfirm
        ? `<div class="confirm-button"><a id="warehouse-map-click-confirm" class="aplus-auto-exp map-button" data-spm="d_map_confirm">
      ${btnText}
      </a>
      </div>`
        : ''
    }
  </div>
  <div class="line"></div>
</div>
`;
};

/**
 * Get Field and Component Props by Field Name
 * @param param
 * @returns
 */
export const FieldProps = ({
  name = '',
  title,
  disabled,
  required,
  form,
  item,
  map,
  createMarker = noop,
  countryCodeName,
  addressChange,
  isCB,
  visible,
}: mapFieldProps) => {
  const fields = form.fields;
  if (!form) {
    return {};
  }

  const common = {
    name,
    title,
    disabled,
    componentProps: {
      style: { width: '100%' },
    },
    visible,
  };
  switch (name) {
    case 'phone':
      return {
        ...common,
        validator: v => phoneValidate(required, v, localeWarehouse, isCB, countryPhoneValidate),
        componentProps: {
          addonTextBefore: isCB ? '' : `+${countryPhoneValidate?.code}`,
          maxLength: !isCB ? countryPhoneValidate?.maxLength : 21,
        },
      };
    case 'email':
      return {
        ...common,
        validator: v => emailValidate(required, v, localeWarehouse),
        componentProps: {
          maxLength: 100,
        },
      };
    case 'pinLocation':
      return {
        ...common,
        component: Search,
        validator: v => verifyFiled(required, v),
        componentProps: {
          shape: 'simple',
          hasIcon: false,
          id: 'warehouse-address-edit-map',
          searchText: null,
          placeholder: localeWarehouse['search.tip'],
          onChange: debounce(addressChange, 200),
          onVisibleChange: (v, type) => {
            setFieldProps(fields, { visible: false });
          },
        },
      };
    case 'district':
      return {
        ...common,
        component: CascaderSelect,
        validator: v => verifyFiled(required, v),
        componentProps: {
          className: 'district-field-name',
          loadData: data => {
            const locationId = data && data['locationId'];
            const pos = data && data['pos'];
            return getDistrictData(fields, locationId, dataSource, pos);
          },
          onChange: (v, data, extra) => debounce(districtSelect(form, v, data, extra, countryCodeName), 800),
        },
      };
    case 'postcode':
      return {
        ...common,
        validator: v => postCodeValidate(required, v, countryPostcodeValidate, isCB, localeWarehouse),
        componentProps: {
          onChange: v => postCodeChange(form, map, createMarker, v, isCB),
          ...getPostCodeMaxMinLength(countryPostcodeValidate, isCB),
        },
      };
    case 'countryRegion':
      return {
        ...common,
        validator: v => verifyFiled(required, v),
        component: SelectItem,
        componentProps: {
          name,
          value: item?.value,
          item: item,
          regionForm: form,
        },
      };
    case 'address1':
      return {
        ...common,
        validator: v =>
          verifyFiled(required, v, () => {
            if (/<[^>]*>/g.test(v)) {
              return {
                type: 'error',
                message: localeWarehouse['address.xss.error'] || 'Cannot contain rich text',
              };
            }
            return true;
          }),
        componentProps: {
          ...getCommonMaxMinLength(name, localeWarehouse['search.placeholder']),
        },
      };
    case 'expressFblWarehouse':
      return {
        ...common,
        component: Checkbox,
        componentProps: {
          name,
          item,
          label: item?.label,
          regionForm: form,
          value: parseCheckboxValue(item?.value),
        },
      };
    default:
      return {
        ...common,
        validator: v => commonValidate(required, v, name, localeWarehouse),
        componentProps: {
          ...getCommonMaxMinLength(name, localeWarehouse['search.placeholder']),
        },
      };
  }
};

export const parseCheckboxValue = value => {
  if (!value) return false;
  return value === 'true' ? true : false;
};
