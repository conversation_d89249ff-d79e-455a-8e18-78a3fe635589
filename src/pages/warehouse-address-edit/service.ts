import { axios, ASCTools } from '@alife/workstation-utils';
import { SubmitParams } from './interfaces';
import { SafeHtmlText } from '@alife/asc-components';

const venture = ASCTools.getCountry() || 'SG';

const getWarehouse = ({ addressId, groupId = '7' }) => {
  return axios.get('mtop.lazada.rc.multi.address.edit', {
    data: {
      addressId: addressId,
      groupId: groupId,
    },
  });
};

const getWarehouseByCode = ({ code = '', groupId = '7' }) => {
  return axios
    .get('mtop.lazada.rc.multi.address.edit.by.warehouse.code', {
      data: {
        warehouseCode: code,
        groupId: groupId,
      },
    })
    .then(res => res?.data?.data || []);
};

const addWarehouse = () => {
  return axios.get('mtop.lazada.rc.multi.address.add', {
    data: {
      groupId: '7',
    },
  });
};

const saveWarehouse = (v: SubmitParams) => {
  const isReturn = v?.groupId === '11';
  const api = isReturn ? 'mtop.lazada.rc.return.address.save' : 'mtop.lazada.rc.multi.address.save';
  if (v?.address1) {
    v.address1 = SafeHtmlText.cleanHtml(v.address1 || '');
  }

  return axios
    .post(api, {
      ...v,
    })
    .then(res => {
      if (res['status'] !== 200) {
        return {
          notSuccess: true,
          message: res['message'],
        };
      }
      const dataType = { notSuccess: false };
      res?.data?.data?.map(el => {
        dataType[el?.eventType] = el;
      });
      return dataType;
    });
};

const getRcodeByLngLat = ({ lngLat, placeId, uuid }: { lngLat: Array<number> | number, placeId?: string, uuid?: string}) => {
  if (!Array.isArray(lngLat) && !(placeId && uuid)) return;
  return axios.get('mtop.lazada.rc.multi.address.query.by.coordinate', {
    data: {
      longitude: lngLat[0],
      latitude: lngLat[1],
      placeId,
      uuid
    },
  });
};

const getLngLatByAddress = address => {
  return axios.get('mtop.lazada.rc.multi.address.query.geo.by.address', {
    data: {
      address: address,
    },
  });
};

const searchAddress = address => {
  return axios.get('mtop.lazada.rc.multi.address.auto.complete', {
    data: {
      address: address,
    },
  });
};
// {"_timezone":-8,"spm":"a1zawg.14013315.0.0.61794edfLxzBlH","activeKey":"0","groupId":"7","secondTab":"4","patchName":"countryRegion","code":"TH","group":7}
const getCountryAddress = countryCode => {
  return axios
    .get('mtop.lazada.rc.multi.address.warehouse.location.country', {
      data: {
        countryCode: countryCode,
      },
    })
    .then(res => res?.data?.data);
};
// province {"_timezone":-8,"spm":"a1zawg.14013315.0.0.61794edfLxzBlH","activeKey":"0","groupId":"7","secondTab":"4","patchName":"locationLevel1","code":"TH","group":7}

const getLocationAddress = locationId => {
  return axios.get('mtop.lazada.rc.multi.address.warehouse.location', {
    data: {
      locationId: locationId,
    },
  });
};
// {"_timezone":-8,"spm":"a1zawg.14013315.0.0.61794edfLxzBlH","activeKey":"0","groupId":"7","secondTab":"4","patchName":"locationLevel2","maxLevel":3,"code":"R92277","group":7}

const getAddressByPostCode = postcode => {
  return axios.get('mtop.lazada.rc.multi.address.query.by.postcode', {
    data: {
      postcode: postcode,
    },
  });
};

const getAddressRCode = list => {
  return axios.get('mtop.lazada.rc.multi.address.query.location.area', {
    data: {
      locationIdList: list,
    },
  });
};

const getCountryList = () => {
  return axios.get('mtop.lazada.rc.warehouse.seller.country.list').then(res => {
    const modules = res?.data?.data || [];
    return modules?.map(el => {
      el.label = el.nameLocal;
      el.value = el.locationId;
      return el;
    });
  });
};

const getAddressCheckResult = params => {
  return axios({
    url: 'mtop.lazada.rc.multi.address.check',
    method: 'POST',
    data: params,
    headers: {
      'eagleeye-userdata': 'dpath_env=fm_non_default_area',
    },
  });
};
const getCheckDrtmResult = params => {
  return axios({
    url: 'mtop.lazada.rc.return.address.drtw.switch.check',
    method: 'POST',
    data: params,
  }).then(res => res?.data?.module || {});
};

const getUserInfo = () => {
  return axios
    .get('mtop.global.merchant.subaccount.otp.userinfo')
    .then(result => {
      return result.data.data.isCrossBorder;
    })
    .catch(e => false);
};

export interface I3PFWarehouseResult {
  countryLevelId: string;
  addressDetail: string;
  areaIdLevel1: string;
  areaIdLevel2: string;
  areaIdLevel3: string;
  areaIdLevel4: string;
  areaIdLevel5: string;
  contactName: string;
  contactNumber: string;
  email: string;
  postalCode: string;
  warehouseCode: string;
  warehouseName: string;
  warehousePartner: string;
}

const get3PFWarehouse: (params: { warehouseInfo: string }) => Promise<I3PFWarehouseResult> = ({ warehouseInfo }) => {
  return axios({
    url: 'mtop.lazada.rc.3pf.warehouse.query',
    method: 'POST',
    data: { warehouseInfo },
  }).then(res => res?.data?.data || {});
}

const getCurrentUserType = () => {
  return axios({
    url: 'mtop.lazada.rc.3pf.warehouse.canUse3pfWarehouse',
    method: 'POST',
    // data: params,
  }).then(res => res?.data?.data);
}

export {
  getWarehouse,
  getWarehouseByCode,
  addWarehouse,
  saveWarehouse,
  searchAddress,
  getCountryAddress,
  getLocationAddress,
  getAddressByPostCode,
  getRcodeByLngLat,
  getLngLatByAddress,
  getAddressRCode,
  getCountryList,
  getAddressCheckResult,
  getUserInfo,
  getCheckDrtmResult,
  get3PFWarehouse,
  getCurrentUserType
};
