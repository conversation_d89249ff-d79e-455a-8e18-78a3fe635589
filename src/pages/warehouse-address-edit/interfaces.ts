import { ReactNode } from 'react';

export interface IProps {
  geoLocation?: () => void;
  setForm: (v) => void;
  showInfoOnMap: (pos, v?: boolean) => void;
  createMarker: (v: MarkerProps) => void;
  map?: any;
  isCB?: boolean;
  is3PFUser?: boolean;
  params?: { addressId?: string; groupId?: string; code?: string };
}

export interface MinMaxResult {
  maxLength?: number;
  minLength?: number;
  placeholder?: string;
}

export interface LevelsResult {
  country?: string;
  locationLevel1?: string;
  locationLevel2?: string;
  locationLevel3?: string;
  locationLevel4?: string;
  locationLevel5?: string;
}

export interface ValidateParams {
  required: boolean;
  v: any;
  localeWarehouse?: any;
  isCB?: boolean;
  countryPhoneValidate?: { maxLength: number; minLength: number; code?: number };
  callback?: () => boolean | { type: string; message: string };
  locale?: any;
}

export interface LongLatResult {
  latitude: string;
  longitude: string;
  value: string;
  placeId: string;
  uuid: string;
}

export interface ComponentProps {
  city: string;
  cityCode: string;
  district: string;
  province: string;
  street: string;
  township: string;
  streetNumber: string;
  adcode: string;
}

export interface SubmitParams {
  groupId?: string;
  longitude: number;
  latitude: number;
  modifyPlatform?: string;
  address1?: string;
  isCB?: boolean;
  phone?: string;
  email?: string;
  pinLocation?: string;
  district?: string;
  postcode?: string;
  countryRegion?: string;
}

export type MarkerProps = {
  title?: string;
  content?: string;
  pos?: Array<number>;
  showConfirm?: boolean;
};

export interface mapFieldProps {
  item?: {
    value?: string;
    uiType?: string;
    label?: string;
  };
  isCB: boolean;
  name: string;
  title: ReactNode;
  disabled: boolean;
  required: boolean;
  form: any;
  createMarker: (v: MarkerProps) => void;
  map: any;
  addressChange?: (v, type, item, flag) => void;
  districtSelect?: (v, data, extra) => void;
  countryCodeName?: { locationId: string; name: string };
  visible?: boolean;
}
