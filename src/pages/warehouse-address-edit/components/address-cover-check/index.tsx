import React from 'react';
import { Dialog, Typography, Box } from '@alifd/next';
import commonLocale from '../../../../constants/common-locale';
import { SafeHtmlText } from '@alife/asc-components';
import noop from 'lodash-es/noop';
export default function AddressCoverCheck(callback = noop) {
  Dialog.show({
    onOk: () => {
      callback();
    },
    title: <Typography.H4>{commonLocale['page.account.setting.addressOutOfService']}</Typography.H4>,
    okProps: { children: commonLocale['page.account.setting.please.save.address'], className: 'aplus-auto-exp' },
    cancelProps: { children: commonLocale['page.common.btn.cancel'], className: 'aplus-auto-exp' },
    content: (
      <Box direction="column" spacing={[8, 0]}>
        <SafeHtmlText html={commonLocale['page.account.setting.baseOnYourNewWarehouse']} />
        <SafeHtmlText html={commonLocale['page.account.setting.pleaseConfirmIfYouStill']} />
      </Box>
    ),
    style: { width: 500 },
  });
}
