import React from 'react';
import { Box, Icon } from '@alifd/next';
import noop from 'lodash-es/noop';

function MapControls({ searchLocation = noop, zoomIn = noop, zoomOut = noop }) {
  return (
    <Box direction="column" className="controls-bar">
      <a className="location aplus-auto-exp" data-spm="d_warehouse_edit_location" onClick={() => searchLocation()}>
        <img src="//img.alicdn.com/imgextra/i4/O1CN01Hw0dKo1SD8qDkpxEy_!!6000000002212-2-tps-147-165.png" />
      </a>
      <Icon className="aplus-auto-exp" data-spm="d_warehouse_edit_add" type="add" onClick={() => zoomIn()} />
      <Icon className="aplus-auto-exp" data-spm="d_warehouse_edit_minusa" type="minus" onClick={() => zoomOut()} />
    </Box>
  );
}

export { MapControls };
