.warehouse-address-search-popup {
  box-sizing: border-box;
  border: 1px solid #cbced5;
  border-radius: 8px;
  overflow: auto;
  box-shadow: none;
  background: #ffffff;
  padding: 10px 12px;
  max-height: 200px;
  .render-sub-label {
    cursor: pointer;
    font-size: 14px;
    margin-top: 4px;
    .content {
      margin-left: 16px;
      color: #a0a2ad;
    }
  }
  .label-title {
    color: #000000;
    display: flex;
    img {
      width: 16px;
      height: 16px;
      -webkit-filter: grayscale(100%);
      -moz-filter: grayscale(100%);
      -ms-filter: grayscale(100%);
      -o-filter: grayscale(100%);
      filter: grayscale(100%);
      filter: gray;
    }
  }
}
