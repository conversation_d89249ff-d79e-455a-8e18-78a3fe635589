import React from 'react';
import { Box } from '@alifd/next';
import noop from 'lodash-es/noop';
import './index.scss';

export default function popupContent({ sources, addressChange = noop }) {
  return (
    <Box className="warehouse-address-search-popup">
      {sources?.map(el => {
        return (
          <Box
            className="render-sub-label"
            onClick={() => {
              !!el?.value && addressChange(el?.value, 'click', el);
            }}
          >
            <Box className="label-title" direction="row">
              {/* biome-ignore lint/a11y/useAltText: <explanation> */}
              <img src="//img.alicdn.com/imgextra/i4/O1CN01Hw0dKo1SD8qDkpxEy_!!6000000002212-2-tps-147-165.png" />
              {el?.title}
            </Box>
            <Box className="content">{el?.label}</Box>
          </Box>
        );
      })}
    </Box>
  );
}
