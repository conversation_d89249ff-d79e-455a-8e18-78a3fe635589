import React from 'react';
import { Dialog } from '@alifd/next';

export default function Confirm(data, redirectHref) {
  Dialog.show({
    onOk: () => {
      if (!!window?.location?.href) window.location.href = `${window.location.origin}/${redirectHref}`;
    },
    closeable: false,
    onCancel: () => {
      const cancelActions = data['showConfirm']?.cancelActions || [];
      const cancelHref = cancelActions[0]?.href;
      if (!!window?.location?.href) window.location.href = `${window.location.origin}/${cancelHref}`;
    },
    title: data['showConfirm']?.title,
  });
}
