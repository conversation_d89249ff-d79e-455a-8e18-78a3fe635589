import React, { useCallback, useEffect, useState } from 'react';
import { Box, Select, CascaderSelect } from '@alifd/next';
import { getCountryList } from '../../service';
import { debounce } from '../../../../utils/tools';
import { getChain, getDistrictData, setAttribute, formatDistrict, cacheDistrictCode } from '../../district-utils';

function closePopup() {
  const el = document.querySelector('.info-panel');
  // @ts-ignore
  el && el.click();
}

/**
 *
 * @param data
 */
export let dataSource: { current: Array<{ label?: string; value?: string; children?: Array<Object> }> } = {
  current: [],
};

interface IProps {
  value?: string;
  onChange: (v) => void;
  style?: object;
  regionForm?: { fields: object };
  name?: string;
  item?: { value?: string };
  disabled?: boolean;
}
function SelectItem({ onChange, value = '', style = {}, disabled, name = 'countryRegion', regionForm, item }: IProps) {
  const [selectValue, setValue] = useState('');
  const setRegionValue = useCallback(() => {
    if (!!item?.value && typeof item?.value === 'string') {
      const result = setAttribute({ obj: regionForm, attr: name, valueChain: item.value });
      cacheDistrictCode.current = item.value;

      if (!!result && result instanceof Promise) {
        result?.then(res => {
          if (res === 'none') setValue('');
        });
      }
    }
  }, []);

  useEffect(() => {
    getCountryList().then(res => {
      dataSource.current = formatDistrict(res || []);
      setRegionValue();
      onChange(value);
    });
  }, []);

  useEffect(() => {
    !!value && setValue(value);
  }, [value]);

  const districtSelect = (v, data, extra) => {
    const path = extra?.selectedPath;
    const chain = getChain(path);
    // 保存行政区
    // setDistrict(chain.value?.split('-'));
    setAttribute({ obj: regionForm, attr: name, v: chain.label, valueChain: chain.value, from: 'click' });
  };

  return (
    <Box>
      <CascaderSelect
        style={style}
        value={selectValue}
        disabled={disabled}
        defaultValue={selectValue}
        className="district-field-name"
        loadData={async data => {
          const locationId = data && data['locationId'];
          const pos = data && data['pos'];
          const result = await getDistrictData(regionForm?.fields, locationId, dataSource, pos, name);

          if (!result?.length) {
            setValue(locationId);
            setAttribute({
              obj: regionForm,
              attr: name,
              v: data?.['nameLocal'],
              valueChain: locationId,
              from: 'click',
            });
            closePopup();
          }
          return Promise.resolve(result);
        }}
        onChange={debounce(districtSelect, 800)}
        dataSource={dataSource?.current}
      />
    </Box>
  );
}

export default SelectItem;
