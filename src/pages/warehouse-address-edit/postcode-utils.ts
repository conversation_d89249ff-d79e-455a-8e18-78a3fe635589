import { ASCTools } from '@alife/workstation-utils';
import localeWarehouse from '../../constants/warehouse';
import getDescription from './components/description';
import { getPostChain, setAttribute, showDescription, cacheDistrictCode, longLatGeoCode } from './district-utils';
import { setPosition } from './utils';
import { getAddressByPostCode } from './service';

const country = ASCTools.getCountry();

export const postCodeChange = (form, map, createMarker, v, isCB) => {
  const fields = form.fields;
  if (country !== 'SG' || !!isCB) return;
  getAddressByPostCode(v).then(res => {
    const data = res?.data?.data;
    if (!data) return;
    const addr = data?.geographyAddressTreeDTO;
    // set district value
    if (!!addr) {
      const chain = getPostChain(addr);
      cacheDistrictCode.current = chain.value;
      setAttribute({ obj: form, attr: 'district', v: chain.label, valueChain: chain.value });
    }
    // setMap position
    if (!!data?.geographyCoordinateDTO) {
      setPosition(
        map,
        createMarker,
        data?.geographyCoordinateDTO.longitude,
        data?.geographyCoordinateDTO.latitude,
        longLatGeoCode,
        addr.addressTitle,
        addr?.address,
        false,
      );
    }

    setAttribute({ obj: form, attr: 'pinLocation', v: addr?.addressTitle });
    setAttribute({ obj: form, attr: 'address1', v: addr?.addressTitle });
    showDescription(fields, getDescription(localeWarehouse['input.floor.unit']));
  });
};
