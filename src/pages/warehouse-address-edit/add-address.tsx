import * as i18n from '@alife/lazada-i18n';
import React, { useEffect, useState } from 'react';
import { Message, Loading, Button, Dialog } from '@alifd/next';
import FormFiled from './Field';
import { createForm } from '@formily/core';
import { useMemoizedFn, useRequest } from 'ahooks';
import localeWarehouse from '../../constants/warehouse';
import AddressCoverCheck from './components/address-cover-check';
import renderFieldLabel from './components/field-label';
import { withTrackEffects } from '@alife/form-track-plugin';
import { dataSource } from './components/select';
import showConfirm from './components/confirm';
import popupContent from './components/popup-content';
import commonLocale from '../../constants/common-locale';
import { ASCTools, When } from '@alife/workstation-utils';
import useUrlState from '@ahooksjs/use-url-state';
import type { IProps } from './interfaces';
import {
  setAddress,
  getLongLat,
  setPosition,
  setLevels,
  districtSelectName,
  FieldProps,
  parseCheckboxValue } from
'./utils';
import {
  getPostChain,
  getDistrictData,
  setFieldProps,
  setAttribute,
  formatSources,
  cacheDistrictCode,
  longLatGeoCode } from
'./district-utils';
import { defaultForm, SortElements } from '../../utils/map-tool';
import { getWarehouseByCode, saveWarehouse, searchAddress, getCountryAddress, getAddressCheckResult, getCheckDrtmResult, get3PFWarehouse, type I3PFWarehouseResult, getCurrentUserType } from './service';
import { Form, Reset, Submit, FormLayout, FormButtonGroup, FormItem, Checkbox, Input } from '@formily/next';
import { Field } from '@formily/react';
import { SafeHtmlText } from '@alife/asc-components';

interface FormEleemntType {
  name?: string;
  value?: string;
  label?: string;
  subLabel?: string;
  required?: boolean;
  visible?: boolean;
  disabled?: boolean;
}

/**
 * save address id
 */
let addressID: string | undefined = '';
/**
 * cache country code and name
 */
const countryCodeName = { locationId: '', name: '' };

const { autoFormTrack, setError } = withTrackEffects({
  formKey: 'setting-warehouse-edit'
});

const country = ASCTools.getCountry();

const QuickFillDialog = ({ isDialogVisible, setIsDialogVisible, handle3PFWarehouseQuery }) => {
  const [warehouseName, setWarehouseName] = useState("");
  return (
    <Dialog
      key={isDialogVisible}
      title="Quick fill-in enter your warehouse partner"
      visible={isDialogVisible}
      onClose={() => setIsDialogVisible(false)}
      width={500}
      footer={false}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <span>{i18n.formatMessage({
            id: 'logistic-warehouse-edit.quick-fill-in', defaultMessage: 'Warehouse Name / Code'
          })}</span>
          <Input style={{ flex: 1 }} value={warehouseName} onChange={value => setWarehouseName(value)} />
        </div>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button type="primary" onClick={() => handle3PFWarehouseQuery(warehouseName)}>Submit</Button>
        </div>
      </div>
    </Dialog>
  )
}

function AddAddress({ createMarker, setForm, showInfoOnMap, map, isCB, is3PFUser }: IProps) {
  const { data, run: getDrtmCheckRun } = useRequest(getCheckDrtmResult, {manual: true});
  const [disabled, setDisabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [elements, setElements] = useState<FormEleemntType[]>([]);
  const [params] = useUrlState<{addressId: string;groupId: string;code?: string;}>();
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [form, setFormType] = useState(() => {
    return createForm({
      effects() {
        autoFormTrack();
      }
    });
  });
  setForm(form);

  const runDrtm = (levels) => {
    params.groupId === '11' && getDrtmCheckRun({
      ...params,
      country: ASCTools.getCountry(),
      ...levels
    });
  };

  const updateForm = (dataSource, _form = form) => {
    // setDistrict(elements);
    let allDisable = true;
    // TODO: Return warehouse only have district name cant different isCB
    setElements(districtSelectName(dataSource, isCB, 'district', 'countryRegion'));
    dataSource.map((el) => {
      const { visible, disabled: itemDisabled, value, name, subLabel, label, uiType = '' } = el || {};
      if (!!visible || ['district', 'countryRegion'].includes(el?.name)) {
        if (!itemDisabled) allDisable = false;
        if (name === 'district') {
          const tips = getPostChain(el?.extData);
          cacheDistrictCode.current = value;
          if (!tips?.label) {
            return;
          }
        }
        setAttribute({
          obj: _form,
          attr: name,
          v: uiType === 'CheckboxGroup' ? parseCheckboxValue(value) : value,
          label: renderFieldLabel(name, subLabel, label),
          valueChain: value,
          from: 'init'
        });
      }
    });
    allDisable && setDisabled(allDisable);
  };

  const addressChange = (v, type, item, flag = true) => {
    // When selected
    if (country === 'SG') return;
    if (item) {
      const { placeId, uuid, lngLat } = item;
      setFieldProps(form.fields, { visible: false });
      Array.isArray(item.lngLat) && showInfoOnMap({ lngLat, placeId, uuid}, true);
      return;
    }
    if (!v) {
      setFieldProps(form.fields, { visible: false });
      return;
    }
    // When search address
    searchAddress(v).then((res) => {
      const data = res?.data?.data;
      const sources = !data || !data?.length ? [{ title: `${localeWarehouse['no.result']}` }] : formatSources(data);
      setFieldProps(form.fields, { popupContent: popupContent({ sources, addressChange }), visible: !!flag });
    });
  };

  const setWarehouse = (formItems) => {
    let configItems = formItems;
    // init warehouse
    return getWarehouseByCode({ ...params }).
    then((data) => {
      // Use default data
      if (!data?.length) {
        configItems = defaultForm;
        if (country === 'SG') configItems = SortElements(defaultForm);
        setLoading(false);
        updateForm(configItems);
        return;
      }
      configItems = data[0]?.elements || [];
      runDrtm({ ...(configItems.find((i) => ['district', 'countryRegion'].includes(i?.name))?.extData || {}) });
      if (!configItems?.length) return;
      if (country === 'SG') configItems = SortElements(configItems);
      updateForm(configItems);

      // Get addressId
      addressID = setAddress(configItems);
      // CB dont need geocode
      if (isCB) return;
      // Set position by address
      const { latitude, longitude, value: v } = getLongLat(configItems);
      setTimeout(() => {
        !!v && addressChange(v, '', '', false);
        setPosition(map, createMarker, longitude, latitude, longLatGeoCode, '', v, false);
      }, 800);
    }).
    catch((e) => {
      console.error(e);
      if (country === 'SG') configItems = SortElements(defaultForm);
      updateForm(configItems);
    }).
    finally(() => {
      setLoading(false);
    });
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    let elements: Array<{
      name?: string;
      value?: string;
      label?: string;
      required?: boolean;
      visible?: boolean;
      disabled?: boolean;
      subLabel?: string;
    }> = [];
    // if map exist and not cb seller will query for sub region of country
    if (isCB !== undefined) {
      if (!isCB) {
        // init first select district
        getCountryAddress(country).then((res) => {
          const locationId = res.locationId;
          countryCodeName.locationId = locationId;
          countryCodeName.name = res?.nameLocal;
          // Render field first
          setWarehouse(elements).then(() => {
            // need to get level 2 array first
            !!locationId && getDistrictData(form.fields, locationId, dataSource);
          });
        });
      } else {
        setWarehouse(elements).then(() => {
          setLoading(false);
          // TODO: set dataSource
        });
      }
    }
  }, [map, isCB]);

  const dealWithRedirect = useMemoizedFn((data) => {
    const redirectHref = data['redirect']?.href;
    if (!!data['success'] && !!data['redirect'] && !data['showConfirm']) {
      Message.success(data['success']?.content);
      if (!!data['redirect'] && !!window?.location?.href) {
        window.location.href = `${window.location.origin}/${redirectHref}`;
      }
    } else if (!!data['showConfirm']) {
      showConfirm(data, redirectHref);
    }
  });

  const save = (submitParams) => {
    saveWarehouse(submitParams).
    catch((e) => {
      const msg = typeof e === 'object' ? e?.data?.data?.error || e?.message : e;
      setError(msg);
      Message.error(msg);
    }).
    then((data) => {
      if (!data) return;
      if (!!data?.notSuccess) {
        Message.error(data?.['message']);
        return;
      }
      setDisabled(true);
      dealWithRedirect(data);
    });
  };

  const onSubmit = (v) => {
    const pageType = params?.groupId === '7' ? 'forward' : 'return';
    // Get address chain code if have no field district use origin
    const codeChain = v?.['valueID'] || cacheDistrictCode.current;
    // elements?.find(el => el && el['name'] === 'district')?.value;
    const rCodeArr = codeChain?.split?.('-');
    const levels = setLevels(rCodeArr);

    const center = longLatGeoCode.current;
    // Return address and cb don't need geoCode
    if (!center && !isCB && params?.groupId !== '11' && country !== 'SG') {
      Message.warning(localeWarehouse['no.geo.code.tip']);
      return;
    }
    // Return address
    const par = {
      enableDrtw: !!data?.value,
      ...v,
      ...params,
      // to do  no geoCode can't submit
      longitude: center?.[0],
      latitude: center?.[1],
      ...levels,
      modifyPlatform: 'pc',
      isCB: isCB,
      expressFblWarehouse: v.expressFblWarehouse?.toString()
    };

    // set address by select
    !!isCB && pageType === 'return' ? par.countryRegion = codeChain : par.district = codeChain;
    // set addressId if isn't exist
    if (!!addressID) par.addressId = addressID;

    if (params?.groupId === '11') {
      save(par);
      return;
    }

    getAddressCheckResult(par).then((res) => {
      const isUncovered = res.data.data;
      if (isUncovered === true) {
        AddressCoverCheck(() => save(par));
      } else {
        save(par);
      }
    });
  };

  const handle3PFWarehouseQuery = async (warehouseName?: string) => {
    if (!warehouseName) {
      Message.error('Please enter a warehouse name');
      return;
    }

    setLoading(true);
    try {
      const result = await get3PFWarehouse({ warehouseInfo: warehouseName });
      const finalResult = transform3PFWarehouseResult(result);
      const newForm = createForm({
        effects() {
          autoFormTrack();
        }
      });
      setFormType(newForm);
      const visibleInitialValues = finalResult.reduce((acc, field) => {
        // 自闭了，暂时先这样实现
        newForm.setFieldState(field.name, { disabled: true });
        acc[field.name] = field.value;
        return acc;
      }, {});

      for (const key in result) {
        if (Object.prototype.hasOwnProperty.call(result, key) && key.startsWith("areaIdLevel")) {
          const level = key.replace("areaIdLevel", "");
          visibleInitialValues[`locationLevel${level}`] = result[key] || "";
        }
      }
      newForm.setInitialValues(visibleInitialValues, "overwrite");

      updateForm(finalResult, newForm);
      setIsDialogVisible(false);
      setDisabled(false);
    } catch (error) {
      Message.error('Failed to get warehouse information');
    } finally {
      setLoading(false);
    }
  }

  const transform3PFWarehouseResult = (result: I3PFWarehouseResult) => {
    const { countryLevelId, areaIdLevel1, areaIdLevel2, areaIdLevel3, areaIdLevel4, areaIdLevel5, addressDetail } = result;
    const areaInfo = {
      countryLevelId,
      areaIdLevel1,
      areaIdLevel2,
      areaIdLevel3,
      areaIdLevel4,
      areaIdLevel5,
      addressDetail
    };

    const fieldConfig = [
      { name: "contactName", label: "Contact Name", value: result.contactName, extData: {}, visible: true },
      { name: "phone", label: "Contact Number", value: result.contactNumber, extData: {}, visible: true },
      { name: "email", label: "Email", value: result.email, extData: {}, visible: true },
      { name: "postcode", label: "Postal Code", value: result.postalCode, extData: {}, visible: true },
      { name: "threePFWarehouseCode", label: "Warehouse Code", value: result.warehouseCode, extData: {}, visible: false },
      { name: "name", label: "Warehouse Name", value: result.warehouseName, extData: {}, visible: true },
      { name: "countryRegion", label: "Country region", value: result.countryLevelId || "", extData: areaInfo, visible: true },
      { name: "address1", label: "Address Detail", value: result.addressDetail || "", extData: {}, visible: false },
    ];

    return fieldConfig.map(field => ({
      disabled: false,
      extData: field.extData,
      label: field.label,
      labelPosition: "left",
      name: field.name,
      required: false,
      subLabel: "",
      uiType: "Input",
      value: field.value,
      visible: field.visible
    }));
  }

  return (
    <Loading style={{ width: '100%' }} visible={loading}>
      <When condition={disabled}>
        <Message type='warning' title={<SafeHtmlText html={i18n.formatMessage({id: "page.account.setting.warehouseInformationCannotEditAt", defaultMessage: "Warehouse information cannot edit at the moment due to campaign preparation / system maintainence. We will enable it as soon as the event finish. Sorry for the inconvenience."})} />} />
      </When>
      <When condition={!!is3PFUser}>
        <Button text onClick={() => setIsDialogVisible(true)}>Quick fill-in enter your warehouse partner</Button>
      </When>
      <Form form={form}>
        <FormLayout layout="vertical" colon={false}>
          {elements?.map((el) => {
            const { name = '', label = '', required = false, subLabel = '' } = el || {};
            const dealWithProps = FieldProps({
              name,
              title: renderFieldLabel(name, subLabel, label),
              disabled: !!el.disabled,
              required,
              form,
              addressChange,
              item: el,
              map,
              createMarker,
              countryCodeName,
              isCB: !!isCB,
              visible: el.visible
            });
            return <FormFiled {...dealWithProps} key={name} />;
          })}
          {!!data?.visible && <Field name='enableDrtw' decorator={[FormItem]} component={[Checkbox, { checked: !!data?.value, visible: !!data?.visible, disabled: !!data?.disabled, label: i18n.formatMessage({ id: "page.account.setting.directReturnToSellerWarehouse", defaultMessage: "Direct return to Seller Warehouse" }) }]} />}
        </FormLayout>
        <FormButtonGroup align="right" className="address-dialog-footer">
          <Reset
            onClick={() => {
              if (!!window?.location && !disabled) window.location.href = '/apps/setting/warehouse_address';
            }}
            disabled={disabled}
            className="aplus-auto-exp"
            data-spm="d_reset">

            {commonLocale['page.common.btn.cancel']}
          </Reset>
          <Submit disabled={disabled} className="aplus-auto-exp" data-spm="d_submit" onSubmit={onSubmit}>
            {commonLocale['page.common.btn.submit']}
          </Submit>
        </FormButtonGroup>
      </Form>
      <QuickFillDialog isDialogVisible={isDialogVisible} setIsDialogVisible={setIsDialogVisible} handle3PFWarehouseQuery={handle3PFWarehouseQuery} />
    </Loading>
  );

}

export default AddAddress;