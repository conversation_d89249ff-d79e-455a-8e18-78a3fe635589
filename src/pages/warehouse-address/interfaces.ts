export interface IGetAddressInfoParams {
  addressId: number;
  groupId?: number;
}

export interface IGetReturnAddressParams {
  force?: boolean;
}

export interface requestParams {
  pageIndex?: number;
  pageSize?: number;
  bindingType?: string;
  requestType?: string;
  warehouseCode?: string;
  status?: string;
  current?: number;
}

export interface unbindParams {
  groupId?: string;
  groupMemberId?: string;
  groupOwnerSellerId?: string;
  groupOwnerWarehouseCode?: string;
  groupMemberSellerId?: string;
  groupMemberWarehouseCode?: string;
}

export enum GROUP_ID {
  RETURN = 11,
  WAREHOUSE = 7,
}

export interface agreeReject {
  requestId: string;
  requestDetailId: string;
  rejectReason?: string;
}

export interface IUpdateParams {
  targetTable?: string;
  code?: string;
  date?: string;
  holidayMode: boolean;
  type?: string;
}
