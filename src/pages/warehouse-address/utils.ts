import locale from '../../constants/warehouse';
import { Dialog, Input, Message } from '@alifd/next';
import { agreeInvitation, rejectInvitation, unbind, ownerLeave } from './services';
import { setCache } from '../../utils/tools';

export const okClick = (record, type) => {
  let {
    groupMemberSellerId,
    groupOwner,
    warehouseCode,
    warehouseGroupRequestId: requestId,
    warehouseGroupRequestDetailId: requestDetailId,
    refresh,
  } = record || {};
  switch (type) {
    case 'remove':
    case 'leave':
    case 'groupLeave':
      const { sellerId, groupId, id, warehouseGroupMemberInfoDTO } = groupOwner || {};
      groupMemberSellerId = !!groupMemberSellerId
        ? groupMemberSellerId
        : warehouseGroupMemberInfoDTO?.groupMemberSellerId;
      const Server = type === 'groupLeave' ? ownerLeave : unbind;
      Server({
        groupId,
        groupMemberId: id,
        groupOwnerSellerId: sellerId,
        groupMemberSellerId: groupMemberSellerId,
        groupMemberWarehouseCode: warehouseCode,
        groupOwnerWarehouseCode: warehouseGroupMemberInfoDTO?.warehouseCode,
      })
        .then(res => {
          if (!!res) {
            refresh && refresh();
            Message.success(type === 'remove' ? locale.removeGroupSuccess : locale.leaveGroupSuccess);
          }
        })
        .catch(e => {
          const key = e?.data?.data?.errorCode?.key;
          if (key === 'LZD_RC_WAREHOUSE_GROUP_CAN_NOT_LEAVE') Message.error(locale.cantDisband);
        });
      return;
    case 'history':
      agreeInvitation({ requestId, requestDetailId }).then(res => {
        if (!!res) {
          refresh && refresh();
          Message.success(locale.historySuccess);
        }
      });
      return;
    default:
      return '';
  }
};

export const cancelClick = (record, type) => {
  const { warehouseGroupRequestId: requestId, warehouseGroupRequestDetailId: requestDetailId, refresh } = record || {};
  switch (type) {
    case 'history':
      Dialog.confirm({
        title: 'Reject Reason',
        content: 'dsfe',
        locale: {
          ok: 'Comfirm',
          cancel: 'cancel',
        },
        onOk: () => {
          rejectInvitation({ requestId, requestDetailId }).then(res => {
            if (!!res) {
              refresh && refresh();
              Message.notice('You have rejected the invitation');
            }
          });
        },
      });
      return;
    default:
      return;
  }
};

export const getConfirmTitle = type => {
  switch (type) {
    case 'remove':
      return locale.removeConfirmTitle;
    case 'leave':
      return locale.leaveConfirmTitle;
    case 'history':
      return locale.invitationConfirmTitle;
    default:
      return '';
  }
};

export const getDisable = (type, sellerId, ownerId) => {
  const currentSellerid = window?.dadaConfig?.sellerId;
  switch (type) {
    case 'remove':
      return currentSellerid != ownerId;
    case 'leave':
      return false;
    case 'history':
      return false;
    default:
      return false;
  }
};

export const getReceiveType = type => {
  return type === 2
    ? '//img.alicdn.com/imgextra/i4/O1CN01Vjwu4k23OifjkS0nA_!!*************-55-tps-80-24.svg'
    : '//img.alicdn.com/imgextra/i4/O1CN01H4KOe725S2mJVq5gO_!!*************-55-tps-72-24.svg';
};

export const getBindType = type => {
  return type === 0
    ? '//img.alicdn.com/imgextra/i1/O1CN01HVeumy21f5atVMnUk_!!*************-55-tps-81-24.svg'
    : '//img.alicdn.com/imgextra/i3/O1CN012BMHPV1jtb3XREoAw_!!*************-55-tps-81-24.svg';
};

export const tableRows = [
  { name: locale.roleAccount, key: 'currentMember' },
  { name: locale.sellerID, key: 'groupMemberSellerShortCode' },
  { name: locale.warehouseCode, key: 'warehouseCode' },
  { name: locale.warehouseName, key: 'warehouseName' },
  { name: locale.warehouseAddress, key: 'detailAddress' },
  { name: locale.action, key: 'action' },
];

export const BindingType = [
  {
    label: locale.all,
    value: '-1',
  },
  {
    label: locale.binding,
    value: '0',
  },
  {
    label: locale.unBinding,
    value: '1',
  },
];

export const RequestType = [
  {
    label: locale.all,
    value: '-1',
  },
  {
    label: locale.sender,
    value: '1',
  },
  {
    label: locale.receiver,
    value: '2',
  },
];

export const RequestStatus = [
  {
    label: locale.all,
    value: '-1',
  },
  {
    label: locale.inProcess,
    value: '1',
  },
  {
    label: locale.approved,
    value: '2',
  },
  {
    label: locale.rejected,
    value: '3',
  },
  {
    label: locale.cancelled,
    value: '4',
  },
];

export const adeptDTO = record => {
  const { warehouseGroupMemberDTOList, warehouseGroupInfoDto = {} } = record || {};
  // Group Owner always first one
  let groupOwner = warehouseGroupMemberDTOList[0] || {};
  warehouseGroupMemberDTOList.shift();

  return {
    ...record,
    ...warehouseGroupInfoDto,
    groupOwner,
    warehouseGroupMemberDTOList,
    ...groupOwner,
  };
};

export const getDate = v => {
  if (typeof v !== 'number') return v;
  try {
    const date = new Date(v);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const m = date.getMinutes();
    const s = date.getSeconds();
    return `${year}-${month}-${day} ${hours}:${m}:${s}`;
  } catch {
    return v;
  }
};
