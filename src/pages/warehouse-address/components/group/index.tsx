import React, { useState } from 'react';
import History from './request-history';
import GroupTable from './group';
import commonLocale from '../../../../constants/common-locale';
import { useFormDialog } from '@alife/asc-components';
import locale from '../../../../constants/warehouse';
import content from '../confirm';
import { getConfirmTitle } from '../../utils';

import './index.scss';

function Group() {
  const [title, setTtile] = useState<'leave' | 'remove' | 'history'>('remove');

  const [ConfirmDialog, { edit }] = useFormDialog({
    content: content,
    dialogProps: {
      v2: true,
    },
    hasMessage: false,
    footer: false,
  });

  return (
    <>
      <ConfirmDialog title={getConfirmTitle(title)} />
      <GroupTable locale={{ ...commonLocale, ...locale }} edit={edit} setTtile={setTtile} />
      <History locale={{ ...commonLocale, ...locale }} edit={edit} setTtile={setTtile} />
    </>
  );
}

export default Group;
