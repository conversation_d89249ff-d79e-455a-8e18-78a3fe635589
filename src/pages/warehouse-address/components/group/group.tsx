import React from 'react';
import { When } from '@alife/workstation-utils';
import { Card, Button, Icon, Box, Balloon, Pagination, Table, Typography } from '@alifd/next';
import { useFusionTable, useMemoizedFn } from 'ahooks';
import ButtonTip from '../button-tip';
import { setCache } from '../../../../utils/tools';
import { getGroup } from '../../services';
import { tableRows, okClick } from '../../utils';

const nood = (v?: any) => {};
const Tooltip = Balloon.Tooltip;
interface Iprops {
  locale: any;
  edit: (record) => void;
  setTtile: (title) => void;
}

function Group({ locale, setTtile = nood, edit = nood }: Iprops) {
  const { tableProps, loading, run, refresh } = useFusionTable(
    pageInfo =>
      getGroup(pageInfo)
        .catch(e => {
          console.error(e);
        })
        .then(res => {
          return {
            total: res?.['total'] || 0,
            list: res?.['list'] || [],
          };
        }),
    {
      defaultPageSize: 20,
    },
  );

  const subCellClick = (v, record) => {
    if (!!v) {
      setTtile('remove');
    } else {
      setTtile('leave');
    }
    edit({ ...record, type: !!v ? 'remove' : 'leave' });
  };

  const renderExtra = (
    <ButtonTip
      buttonType="primary"
      tips={locale?.createGroupTip}
      okText={locale['page.common.btn.ok']}
      iconType="add"
      text={locale?.create}
      dataSpm="d_create_group"
      url="/portal/apps/setting/warehouse_request?action=create"
    />
  );

  const renderSubCell = useMemoizedFn((record, key, currentMember) => {
    const value = record?.[key];
    const { groupOwner = {} } = record || {};
    const { sellerId } = groupOwner;
    const currentSellerId = window?.dadaConfig?.sellerId;
    const isRemove = sellerId == currentSellerId;
    const isLeaove = sellerId != currentSellerId && currentMember;

    const Wrap = Typography.Text;

    switch (key) {
      case 'currentMember':
        return locale?.groupMember;
      case 'groupMemberSellerShortCode':
        return (
          <Box direction="row">
            {value}
            <When condition={!!currentMember}>
              <Button text>
                <Icon style={{ marginLeft: '16px' }} type="account" />
              </Button>
            </When>
          </Box>
        );
      case 'action':
        return (
          <Box margin={[0, 16]} direction="row">
            <When condition={!!isRemove || !!isLeaove}>
              <Button
                style={{ width: 'fit-content' }}
                text
                className="aplus-auto-exp"
                data-spm={`d_${isRemove ? 'group_remove' : 'member_remove'}`}
                onClick={() => subCellClick(!!isRemove, { ...record, refresh })}
              >
                {!isRemove ? locale?.leaveGroup : locale?.removeMember}
              </Button>
            </When>
          </Box>
        );
      default:
        return <Wrap>{value}</Wrap>;
    }
  });

  const renderCell = useMemoizedFn((key, v, index, record) => {
    const { warehouseGroupMemberInfoDTO, warehouseGroupMemberDTOList, groupOwnerSellerId, groupId } = record;
    const currentSellerId = window?.dadaConfig?.sellerId;
    const { detailAddress, groupMemberSellerId, groupMemberSellerShortCode, warehouseName } =
      warehouseGroupMemberInfoDTO || {};

    switch (key) {
      case 'currentMember':
        return <Typography.H5>{locale?.accountRole}</Typography.H5>;
      case 'action':
        return (
          <When condition={currentSellerId === groupOwnerSellerId}>
            <Button
              component="a"
              className="aplus-auto-exp"
              onClick={() => {
                setCache('multipleWarehouseAddress', {
                  sellerId: groupMemberSellerId,
                  sellerShortCode: groupMemberSellerShortCode,
                  detailAddress,
                  warehouseName,
                  groupId,
                  code: warehouseGroupMemberInfoDTO?.warehouseCode,
                });
              }}
              style={{ marginRight: '16px', fontWeight: 700 }}
              href="/portal/apps/setting/warehouse_request?action=add"
              data-spm="d_add_members"
              text
            >
              {locale?.addMembers}
            </Button>
            <When condition={!warehouseGroupMemberDTOList?.length}>
              <Tooltip
                triggerType="click"
                trigger={
                  <Button
                    component="a"
                    style={{ fontWeight: 700 }}
                    onClick={() => {
                      okClick({ ...record, refresh }, 'groupLeave');
                    }}
                    className="aplus-auto-exp"
                    data-spm="d_owner_leave"
                    text
                  >
                    {locale?.disband}
                  </Button>
                }
              >
                {locale?.disbandTip}
              </Tooltip>
            </When>
          </When>
        );
      default:
        return <Typography.H5>{warehouseGroupMemberInfoDTO[key]}</Typography.H5>;
    }
  });

  return (
    <Card
      title={locale?.title}
      showHeadDivider={false}
      showTitleBullet={false}
      style={{ margin: '16px 0' }}
      extra={renderExtra}
    >
      <Table
        className="warehouse-address-group-table"
        rowProps={(_, index) => ({
          'data-spm': `warehouse_group_${index}`,
        })}
        {...tableProps}
        fixedHeader
        expandedRowRender={(record, index) => {
          const { warehouseGroupMemberDTOList, groupOwner } = record;
          return warehouseGroupMemberDTOList?.map(el => {
            const { warehouseGroupMemberInfoDTO = {}, currentMember } = el || {};
            return (
              <Box className="table-row" direction="row">
                {tableRows.map(item => (
                  <Box key={item.key} className="box-column">
                    {renderSubCell({ ...warehouseGroupMemberInfoDTO, groupOwner }, item.key, currentMember)}
                  </Box>
                ))}
              </Box>
            );
          });
        }}
        hasBorder={false}
      >
        {tableRows.map(item => (
          <Table.Column
            title={item.name}
            dataIndex={item.key}
            key={item.key}
            cell={(v, index, record) => renderCell(item.key, v, index, record)}
          />
        ))}
      </Table>
      {/* <Pagination style={{ marginLeft: 5 }} hideOnlyOnePage {...{ ...paginationProps, pageSizeSelector: false }} /> */}
    </Card>
  );
}

export default Group;
