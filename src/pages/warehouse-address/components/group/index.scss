.request-history-search {
  .next-select {
    margin-right: 16px;
  }
  > *:last-child {
    margin-right: 0;
  }
}

.warehouse-address-group-table {
  table {
    overflow: hidden;
  }
  .table-row {
    .box-column {
      flex: 1 1;
      // padding: 12px 0 12px 22px;
    }
    // > *:first-child {
    //   padding: 12px 0 12px 16px;
    // }
  }
}
.request-history-title {
  font-size: 20px;
  line-height: 44px;
  color: #111111;
  font-weight: 700;
}

.request-history-row-render {
  // .next-box {
  //   flex: 1 1;
  //   margin-right: 16px;
  // }
  // > &:last-child {
  //   margin-right: 0;
  // }
}
