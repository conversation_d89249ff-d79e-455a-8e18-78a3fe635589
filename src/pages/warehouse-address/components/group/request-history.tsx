import React, { useState } from 'react';
import { Card, Box, Select, Form, Icon, Tag, Typography, Button } from '@alifd/next';
import { List, Expand, Description, Image } from '@alife/asc-components';
import { getReceiveType, getBindType, BindingType, RequestType, RequestStatus, getDate } from '../../utils';
import { useFusionTable, useMemoizedFn, useRequest } from 'ahooks';
import { requestHistories, getAllWarehouses } from '../../services';
import moment from 'moment';

const Item = Form.Item;
const DescriptionItem = Description.Item;

interface Iprops {
  locale: any;
  edit: (record) => void;
  setTtile: (title) => void;
}

function requestHistory({ locale, edit, setTtile }: Iprops) {
  const { data } = useRequest(getAllWarehouses);
  const { tableProps, pagination, run, refresh } = useFusionTable(requestHistories);

  if (data?.length) {
    data.unshift({
      label: locale.all,
      value: '-1',
    });
  }

  const onChange = (values, item) => {
    run({ ...values, ...pagination, current: 1 });
  };

  const historySearch = (
    <Box justify="flex-end" className="request-history-search">
      <Form inline onChange={onChange}>
        <Item label={locale?.bindingType}>
          <Select dataSource={BindingType} name="bindingType" />
        </Item>
        <Item label={locale?.requestType}>
          <Select dataSource={RequestType} name="requestType" />
        </Item>
        <Item label={locale?.warehouseCode}>
          <Select dataSource={data} name="warehouseCode" />
        </Item>
        <Item label={locale?.status}>
          <Select dataSource={RequestStatus} name="status" />
        </Item>
      </Form>
    </Box>
  );

  const renderStatus = useMemoizedFn(status => {
    if (!status) return null;
    let type: 'warning' | 'success' | 'yellow' | 'green' | 'red' | 'orange' | undefined = 'green';
    let text = '';

    switch (status) {
      case 1:
        type = 'yellow';
        text = locale?.inProcess;
        break;
      case 2:
        type = 'green';
        text = locale?.approved;
        break;
      case 3:
        type = 'red';
        text = locale?.rejected;
        break;
      case 4:
        type = 'orange';
        text = locale?.cancelled;
        break;
    }
    return (
      <Tag type="normal" color={type}>
        {text}
      </Tag>
    );
  });

  const getDescription = useMemoizedFn((Info, expand) => (
    <Description>
      <DescriptionItem label={locale?.sellerID}>{Info?.sellerShortCode}</DescriptionItem>
      <DescriptionItem label={locale?.warehouseName}>{Info?.warehouseName}</DescriptionItem>
      {expand ? (
        <>
          {/* <DescriptionItem label={'Seller ID'}>{Info?.sellerId}</DescriptionItem> */}
          <DescriptionItem label={locale?.warehouseCode}>{Info?.warehouseCode}</DescriptionItem>
          <DescriptionItem label={locale?.warehouseAddress}>{Info?.detailAddress}</DescriptionItem>
        </>
      ) : (
        ''
      )}
    </Description>
  ));

  const renderDate = useMemoizedFn(time => {
    try {
      return getDate(time);
    } catch (e) {
      return '';
    }
  });

  const renderRow = record => {
    // BINDING:0, UNBINDING:1  SENDER(1), RECEIVER(2),  IN_PROGRESS(1), APPROVED(2), REJECTED(3), CANCELED(4)
    const { senderInfo = {}, requestBindingType = 0, receiveType = 1, receiverInfo = {}, requestStatus = 1 } =
      record || {};
    const cellStatus = renderStatus(record?.requestStatus);
    const currentSellerId = window?.dadaConfig?.sellerId;
    const currentSellerIsReceive = currentSellerId === receiverInfo?.sellerId;

    return (
      <Expand type="normal" keepButton={true} locale={{ more: 'more', less: 'less' }}>
        {expand => {
          return (
            <Box className="request-history-row-render" justify="space-around" direction="row">
              <Box>
                <Image src={getReceiveType(receiveType)} />
              </Box>
              <Box style={{ width: '20%' }}>
                <Typography.H4>{locale?.from}:</Typography.H4>
                {getDescription(senderInfo, expand)}
              </Box>
              <Box>
                <Image src={getBindType(requestBindingType)} />
              </Box>
              <Box style={{ width: '20%' }}>
                <Typography.H4>{requestBindingType === 0 ? locale?.toAdd : locale?.toRemove}:</Typography.H4>
                {getDescription(receiverInfo, expand)}
              </Box>
              <Box justify="center" style={{ width: '22%' }}>
                {expand ? (
                  <>
                    <DescriptionItem label={locale?.requestStatus}>{cellStatus}</DescriptionItem>
                    <DescriptionItem label={locale?.requestTime}>{renderDate(record?.gmtCreate)}</DescriptionItem>
                    <DescriptionItem label={locale?.updateTime}>{renderDate(record?.gmtCreate)}</DescriptionItem>
                    <DescriptionItem label={locale?.notes}>{record?.notes}</DescriptionItem>
                  </>
                ) : (
                  <Typography.Text>{cellStatus}</Typography.Text>
                )}
              </Box>
              <Box align="center" style={{ width: '5%' }} justify="center">
                {requestStatus === 1 && currentSellerIsReceive ? (
                  <Button
                    type="primary"
                    className="aplus-auto-exp"
                    data-spm="d_view_history"
                    onClick={() => {
                      setTtile('history');
                      edit && edit({ ...record, type: 'history', refresh });
                    }}
                  >
                    {locale?.viewDetail}
                  </Button>
                ) : null}
              </Box>
            </Box>
          );
        }}
      </Expand>
    );
  };

  return (
    <>
      <Box direction="row" justify="space-between" align="flex-start">
        <Box className="request-history-title">{locale?.requestHistory}:</Box>
        {historySearch}
      </Box>
      <List toolbar={false} dataSource={tableProps?.dataSource} pagination={pagination} renderRow={renderRow} />
    </>
  );
}

export default requestHistory;
