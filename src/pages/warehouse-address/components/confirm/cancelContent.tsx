import React from 'react';
import { Input, Box, Button, Message } from '@alifd/next';
import CommonLocale from '../../../../constants/common-locale';
import locale from '../../../../constants/warehouse';
import { rejectInvitation } from '../../services';

function CancelContent({ record, close = () => {} }) {
  const { closedialog, warehouseGroupRequestId: requestId, warehouseGroupRequestDetailId: requestDetailId, refresh } =
    record || {};
  let rejectReason = '';

  const onOK = () => {
    rejectInvitation({ requestId, requestDetailId, rejectReason }).then(res => {
      if (!!res) Message.notice(locale.invitationReject);
      refresh && refresh();
    });
  };

  return (
    <Box>
      <Input
        onChange={v => {
          rejectReason = v;
        }}
      />
      <Box margin={[8, 0]} direction="row" justify="flex-end">
        <Button
          style={{ marginRight: '16px' }}
          onClick={() => {
            closedialog && closedialog();
            close();
          }}
        >
          {CommonLocale['page.common.btn.cancel']}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            onOK();
            closedialog && closedialog();
            close();
          }}
        >
          {CommonLocale['page.common.btn.ok']}
        </Button>
      </Box>
    </Box>
  );
}

export default CancelContent;
