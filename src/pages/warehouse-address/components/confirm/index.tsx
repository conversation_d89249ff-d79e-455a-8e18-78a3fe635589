import React, { useState } from 'react';
import { Box, Button, Typography, Message } from '@alifd/next';
import { Description, SafeHtmlText } from '@alife/asc-components';
import { When } from '@alife/workstation-utils';
import locale from '../../../../constants/warehouse';
import CommonLocale from '../../../../constants/common-locale';
import { useFormDialog } from '@alife/asc-components';
import Content from './cancelContent';
import { okClick, getDisable } from '../../utils';

import './index.scss';

const Item = Description.Item;

function ConfirmContent({ record, close = () => {} }) {
  const {
    type,
    sellerShortCode,
    groupMemberSellerShortCode,
    warehouseCode,
    warehouseName,
    detailAddress,
    sellerId,
    groupOwner,
    refresh,
  } = record || {};
  const { sellerId: ownerId, warehouseGroupMemberInfoDTO } = groupOwner || {};
  const {
    warehouseName: ownerWarehouseName,
    warehouseCode: ownerWarehouseCode,
    groupMemberSellerShortCode: ownSellerCode,
    detailAddress: ownerAddress,
  } = warehouseGroupMemberInfoDTO || {};

  const showSellerCode = type === 'leave' ? ownSellerCode : sellerShortCode || groupMemberSellerShortCode;
  const showWarehouseName = ['remove', 'history'].includes(type) ? warehouseName : ownerWarehouseName;
  const showWarehouseCode = ['remove', 'history'].includes(type) ? warehouseCode : ownerWarehouseCode;
  const showDetailAddress = ['remove', 'history'].includes(type) ? detailAddress : ownerAddress;

  // Main account canot approve
  const disabled = getDisable(type, sellerId, ownerId);

  const [CancelDialog, { edit }] = useFormDialog({
    content: Content,
    dialogProps: {
      v2: true,
    },
    hasMessage: false,
    footer: false,
  });

  const title =
    type === 'leave'
      ? locale.leaveConfirmSubTitle
      : type === 'remove'
      ? locale.removeConfirmSubTitle
      : locale.invitationConfirmSubTitle;

  const bottom =
    type === 'leave'
      ? locale.leaveConfirmBottom
      : type === 'remove'
      ? locale.removeConfirmBottom
      : locale.invitationConfirmBottom;

  return (
    <Box className="multiple-warehouses-dialog-wrap">
      <CancelDialog title={locale.cancelReason} />
      <Typography.Text>{title}</Typography.Text>
      <Box className="message-area">
        <When condition={type === 'history'} else={<Item label={locale.sellerID}>{showSellerCode}</Item>}>
          <Item label={locale.warehouseName}>{showWarehouseName}</Item>
        </When>
        <Item label={locale.warehouseCode}>{showWarehouseCode}</Item>
        <When condition={type === 'history'}>
          <Item label={locale.warehouseAddress}>{showDetailAddress}</Item>
        </When>
      </Box>
      <Box className="bottom-descript">
        <SafeHtmlText html={bottom} />
      </Box>
      <Box style={{ marginTop: '8px' }} direction="row" justify="flex-end" align="flex-end">
        <Button
          style={{ width: 'fitContent', marginRight: '16px' }}
          disabled={disabled}
          type="secondary"
          onClick={() => {
            if (type === 'history') {
              edit({ ...record, closedialog: close, refresh });
              return;
            }
            close();
            refresh && refresh();
          }}
        >
          {type === 'history' ? CommonLocale['page.common.tips.rejected'] : CommonLocale['page.common.btn.cancel']}
        </Button>
        <Button
          style={{ width: 'fitContent' }}
          disabled={disabled}
          type="primary"
          onClick={() => {
            close();
            okClick(record, type);
            refresh && refresh();
          }}
        >
          {CommonLocale['page.common.btn.ok']}
        </Button>
      </Box>
    </Box>
  );
}

export default ConfirmContent;
