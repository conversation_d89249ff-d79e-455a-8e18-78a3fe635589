import React, { ReactNode } from 'react';
import { Button, Balloon, Box, Message, Icon } from '@alifd/next';
import { When } from '@alife/workstation-utils';

interface Iprops {
  text?: string;
  dataSpm?: string;
  type?: 'warning' | 'loading' | 'success' | 'error' | 'help' | 'notice' | undefined;
  url?: string;
  okText?: string;
  tips?: string;
  isText?: boolean;
  iconType?: string;
  buttonType?: 'primary' | 'secondary' | 'normal';
  disabled?: boolean;
  showTips?: boolean;
  content?: ReactNode;
}
function ButtonTip({
  buttonType = 'normal',
  text,
  isText,
  tips,
  okText,
  type = 'warning',
  url,
  dataSpm,
  iconType,
  content,
  disabled,
  showTips = true,
}: Iprops) {
  const bt = (
    <Button className="aplus-auto-exp" data-spm={dataSpm} disabled={!!disabled} text={!!isText} type={buttonType}>
      {iconType ? <Icon type={iconType}></Icon> : null}
      {text}
    </Button>
  );

  if (!showTips)
    return (
      <Button
        disabled={!!disabled}
        component="a"
        className="aplus-auto-exp"
        data-spm={dataSpm}
        href={url}
        text={!!isText}
        type={buttonType}
      >
        {iconType ? <Icon type={iconType}></Icon> : null}
        {text}
      </Button>
    );

  return (
    <Balloon closable triggerType="click" trigger={bt}>
      <When condition={!content} else={content}>
        <Box direction="column">
          <Box margin={[8, 0]}>
            <Message type={type}>{tips}</Message>
          </Box>
          <Box direction="row" justify="flex-end" align="flex-end">
            <Button component="a" className="aplus-auto-exp" data-spm={dataSpm} href={url} type="primary">
              {okText}
            </Button>
          </Box>
        </Box>
      </When>
    </Balloon>
  );
}

export default ButtonTip;
