import React from 'react';
import { Box, Message, Button } from '@alifd/next';
import commonLocale from '../../../../constants/common-locale';
import * as i18n from '@alife/lazada-i18n';

import { When } from '@alife/workstation-utils';
import { SafeHtmlText } from '@alife/asc-components';

export interface IProps {
  value?: any;
  index?: number;
  record?: any;
}

export function EditTip({ value = '', index, record = {} }: IProps) {
  return (
    <Box>
      <When condition={!value}>
        <Message type="warning">
          <SafeHtmlText
            html={i18n.formatMessage({
              id: 'page.account.setting.yourInformationDoesntAsyncPlease',
              defaultMessage: 'Your information doesnt async, Please edit and save your information',
            })}
          />
        </Message>
      </When>
      {value}
    </Box>
  );
}
