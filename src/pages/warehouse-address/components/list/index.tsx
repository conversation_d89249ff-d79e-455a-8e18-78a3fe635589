import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../../../constants/common-locale';
import lcoale from '../../../../constants/warehouse';
import React from 'react';
import { Button, Card, Loading, Field, Table, Box, Icon, Switch, Message } from '@alifd/next';
import { Pagination, useFormDialog } from '@alife/asc-components';
import { When, axios } from '@alife/workstation-utils';
import { useFusionTable, useRequest, useMemoizedFn } from 'ahooks';
import { AddressTip } from '../address-tip';
import { useTableUrlState } from '../../hooks';
import { Filter } from '../../filter';
import locale from '../../../../constants/warehouse';
import { EditTip } from '../edit-tip';
import ButtomTip from '../button-tip';
import { getInitData, getReturnAddress, getWarehouseList, updateRCHolidayMode } from '../../services';
import { DialogContent } from './edit-dialog';

function WarehouseList(props) {
  // 物流团队提供的新接口
  const { data } = useRequest(getInitData);
  const { paginationProps: holdayPagination, tableProps: holidayAll, refresh: holidayRefresh } = useFusionTable(
    pageInfo =>
      axios
        .get('mtop.lazada.rc.warehouse.holiday.query', {
          params: pageInfo,
        })
        .then(({ data: res }) => ({
          total: res?.totalCount || 0,
          list: res?.modelList || [],
        })),

    {},
  );

  const { data: returnAddressData, loading: returnAddressLoading } = useRequest(getReturnAddress, {
    throwOnError: true,
  });

  const syncUrlParams = useTableUrlState();
  const field = Field.useField({});

  const { paginationProps, tableProps, search, refresh } = useFusionTable(getWarehouseList, {
    ready: !!data,
    field,
    ...syncUrlParams,
  });
  const { submit } = search;
  // Place useFormDialog here will generate 3 DialogContent inside page
  // but we can avoid the call delegate layer by layer on props
  const [EditDialog, { edit }] = useFormDialog({
    content: DialogContent,
    dialogProps: {
      footer: false,
      title: i18n.formatMessage({
        id: 'page.account.setting.updateHolidayMode',
        defaultMessage: 'Update Holiday Mode',
      }),
    },
  });

  const renderAction = useMemoizedFn((value, index, record, type) => {
    // The new update API is not ready, use old DADA page for temporary
    const { warehouseGroupMemberDTO } = record;
    const { memberType = 2 } = warehouseGroupMemberDTO || {};
    const disabled = !!warehouseGroupMemberDTO && memberType !== 1;
    return (
      <ButtomTip
        isText
        dataSpm="d_edit"
        showTips={!!warehouseGroupMemberDTO ? true : false}
        content={disabled ? locale.editDisableTip : ''}
        disabled={disabled}
        text={commonLocale['page.common.btn.edit']}
        tips={locale['address.edit.tip']}
        okText={commonLocale['page.common.btn.ok']}
        url={`/apps/setting/warehouse_address_map/edit?addressId=${record.addressId}&groupId=${record.groupId}&code=${record.code}`}
      />
    );
  });

  const renderHolidayAction = useMemoizedFn((value, index, row) => {
    return (
      <Switch
        className="aplus-auto-exp"
        data-spm="d_switch"
        onChange={v => {
          onSwitch(v, row);
        }}
        checked={row.isOpen}
      />
    );
  });

  const getColumns = useMemoizedFn((addressType: string) => {
    const coloums = [
      <Table.Column
        title={i18n.formatMessage({ id: 'page.account.setting.name', defaultMessage: 'Name' })}
        width={220}
        dataIndex="name"
      />,

      <Table.Column
        title={i18n.formatMessage({ id: 'page.account.setting.address', defaultMessage: 'Address' })}
        dataIndex="address"
        cell={(value, index, record) => (
          <AddressTip
            value={value}
            record={record}
            tip={i18n.formatMessage({
              id: 'page.account.setting.pleaseUpdateYourAddress',
              defaultMessage: 'Please update your address',
            })}
          />
        )}
      />,

      <Table.Column
        title={i18n.formatMessage({ id: 'page.account.setting.phone', defaultMessage: 'Phone' })}
        dataIndex="phone"
        cell={(value, index, record) => EditTip({ value, index, record })}
      />,

      <Table.Column
        title={i18n.formatMessage({ id: 'page.account.setting.email', defaultMessage: 'Email' })}
        dataIndex="email"
        cell={(value, index, record) => EditTip({ value, index, record })}
      />,

      // <Table.Column
      //   title={i18n.formatMessage({ id: 'page.account.setting.holiday', defaultMessage: 'Holiday' })}
      //   dataIndex=""
      //   cell={(value, index, record) => {
      //     const holiday = holidayAll.dataSource.find(item => item.code === record.code);
      //     return (
      //       <div className="holiday-status">
      //         <div>{renderDate(holiday?.dateFormat, index, { ...record, ...holiday })}</div>
      //         <Switch onChange={v => onSwitch(v, { ...record, ...holiday })} checked={!!holiday?.isOpen} />
      //       </div>
      //     );
      //   }}
      // />,

      <Table.Column
        title={i18n.formatMessage({ id: 'page.account.setting.action', defaultMessage: 'Action' })}
        lock="right"
        cell={(value, index, record) => renderAction(value, index, record, addressType)}
      />,
    ];

    addressType !== 'return' &&
      coloums.splice(
        1,
        0,
        <Table.Column
          width={137}
          title={i18n.formatMessage({ id: 'page.account.setting.warehouseCode', defaultMessage: 'Warehouse Code' })}
          dataIndex="code"
        />,
      );
    return coloums;
  });

  const renderDate = useMemoizedFn((value, index, row) => {
    return row.isOpen ? value : <span className="not-set-text">{commonLocale['page.common.tips.notSet']}</span>;
  });

  const onSwitch = useMemoizedFn((v, r) => {
    if (!v) {
      updateRCHolidayMode({
        holidayMode: v,
        type: 'settingPerWhTable',
        targetTable: 'settingPerWhTable',
        code: r.code,
        date: `${r.start},${r.end}`,
      })
        .then(() => {
          Message.success(commonLocale['page.common.tips.success']);
          if (typeof refresh === 'function') {
            refresh();
          }
          holidayRefresh();
        })
        .catch(e => {
          Message.error(e.message);
        });
    } else {
      edit({
        ...r,
        targetTable: 'settingPerWhTable',
      })
        .then(() => {
          Message.success(commonLocale['page.common.tips.success']);
          if (typeof refresh === 'function') {
            refresh();
          }
          holidayRefresh();
        })
        .catch(() => {});
    }
  });

  return (
    <Box margin={[16, 0]}>
      <EditDialog />
      <Loading style={{ marginBottom: '16px' }} visible={returnAddressLoading}>
        <Card
          showHeadDivider={false}
          showTitleBullet={false}
          title={i18n.formatMessage({ id: 'page.account.setting.returnAddress', defaultMessage: 'Return Address' })}
          className="mb-standard"
          extra={
            <When condition={Array.isArray(returnAddressData?.list) && !returnAddressData?.list.length}>
              <Button
                component="a"
                type="primary"
                className="aplus-auto-exp"
                href="/apps/setting/warehouse_address_map/edit?groupId=11"
                data-spm="d_add_return"
              >
                <Icon type="add"></Icon>
                {i18n.formatMessage({
                  id: 'page.account.setting.return.addWarehouse',
                  defaultMessage: 'Add Return Warehouse',
                })}
              </Button>
            </When>
          }
        >
          <Table
            rowProps={(_, index) => ({
              'data-spm': `return_tb_${index}`,
            })}
            dataSource={returnAddressData?.list}
          >
            {getColumns('return')}
          </Table>
        </Card>
      </Loading>
      <Card
        showHeadDivider={false}
        showTitleBullet={false}
        style={{ marginBottom: '16px' }}
        title={i18n.formatMessage({ id: 'page.account.setting.warehouseAddress', defaultMessage: 'Warehouse Address' })}
        extra={
          <When condition={(data?.isMultiAddress && data?.allowMultiFeature) || tableProps.dataSource?.length === 0}>
            <Button
              component="a"
              type="primary"
              className="aplus-auto-exp"
              href="/apps/setting/warehouse_address_map/edit?groupId=7"
              data-spm="d_add"
            >
              <Icon type="add"></Icon>
              {i18n.formatMessage({ id: 'page.account.setting.addWarehouse', defaultMessage: 'Add Warehouse' })}
            </Button>
          </When>
        }
      >
        {data?.warehouseNameList?.length ? (
          <Filter field={field} submit={submit} warehouseOptions={data?.warehouseNameList} />
        ) : null}
        <Table
          rowProps={(_, index) => ({
            'data-spm': `wh_tb_${index}`,
          })}
          {...tableProps}
        >
          {getColumns('warehouse')}
        </Table>
        <Pagination hideOnlyOnePage {...paginationProps} />
      </Card>
      <Card
        title={i18n.formatMessage({
          id: 'page.account.setting.holidayModeForPerWh',
          defaultMessage: 'Holiday Mode for per WH',
        })}
      >
        <Table {...holidayAll}>
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.name', defaultMessage: 'Name' })}
            width={200}
            dataIndex="name"
          />
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.date', defaultMessage: 'Date' })}
            dataIndex="dateFormat"
            cell={renderDate}
          />
          <Table.Column
            title={i18n.formatMessage({ id: 'page.account.setting.action', defaultMessage: 'Action' })}
            lock="right"
            cell={renderHolidayAction}
          />
        </Table>
        <Pagination hideOnlyOnePage {...holdayPagination} />
      </Card>
    </Box>
  );
}

export default WarehouseList;
