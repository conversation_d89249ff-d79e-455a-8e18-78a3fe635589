import * as i18n from '@alife/lazada-i18n';
import { Box, Button, Card, DatePicker, Field, Form, Select } from '@alifd/next';
import { SelectorItem } from './models';
import React from 'react';

const formItemLayout = {
  labelCol: {
    style: {
      flexGrow: 0,
      flexBasis: 'auto',
    },
  },
};

const FormItem = Form.Item;

export function Filter(props: { field: Field; warehouseOptions: SelectorItem[]; submit: () => void }) {
  const { field, submit, warehouseOptions = [] } = props;

  return (
    <Form className="search-table-container" field={field} {...formItemLayout} onChange={submit}>
      <FormItem {...formItemLayout}>
        <Select
          name="warehouseName"
          hasClear
          label={i18n.formatMessage({ id: 'page.account.setting.warehouseName', defaultMessage: 'Warehouse Name' })}
          showSearch
          dataSource={[
            { label: i18n.formatMessage({ id: 'page.account.setting.all', defaultMessage: 'All' }), value: '' },
            ...warehouseOptions,
          ]}
        />
      </FormItem>
    </Form>
  );
}
