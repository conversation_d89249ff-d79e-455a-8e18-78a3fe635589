import { Vmo } from '@vmojs/decorator';

@Vmo()
export class SelectorItem {
  constructor(data: any) {}

  @Vmo()
  label: string;

  @Vmo()
  value: string;
}

@Vmo()
export class Result {
  constructor(data: any) {}

  @Vmo()
  success: boolean;

  @Vmo()
  message?: string;
}

@Vmo()
export class AddressInfo {
  constructor(data: any) {}

  @Vmo()
  addressId: number;

  @Vmo()
  groupId?: number;

  // address field for list item
  @Vmo(obj => obj.detail || obj.address || obj.address1)
  address?: string;

  // detail field for edit API
  @Vmo(obj => obj.detail || obj.address || obj.address1)
  detail?: string;

  @Vmo(obj => obj.code || obj.addressCode)
  code: string;

  @Vmo()
  venture?: string;

  @Vmo()
  locationLevel1?: string;

  @Vmo()
  locationLevel2?: string;

  @Vmo()
  locationLevel3?: string;

  @Vmo()
  email: string;

  @Vmo()
  isDefault: boolean;

  @Vmo()
  phone: string;

  @Vmo()
  name: string;

  @Vmo()
  isReturnAddress?: boolean;

  @Vmo()
  warehouseGroupMemberDTO?: object;

  @Vmo()
  errorAddress?: boolean;
}

@Vmo()
export class PageInitData {
  constructor(data: any) {}

  @Vmo()
  isCrossBoard: boolean;

  // 卖家是否打了多仓的标
  @Vmo()
  isMultiAddress: boolean;

  // 是否在白名单
  @Vmo()
  allowMultiFeature: boolean;

  @Vmo()
  venture: string;

  @Vmo(obj => obj.warehouseNameList.map(item => new SelectorItem(item)))
  warehouseNameList: SelectorItem[];
}

@Vmo()
export class WarehouseListData {
  constructor(data: any) {}

  @Vmo(obj => obj.list.map(item => new AddressInfo(item)))
  list: AddressInfo[];

  @Vmo()
  total: number;
}
