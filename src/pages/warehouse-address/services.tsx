import { axios, ASCTools } from '@alife/workstation-utils';
import { Message } from '@alifd/next';
import { AddressInfo, PageInitData, WarehouseListData, Result } from './models';
import dadaAdaptor from '../../utils/dada-adaptor';
import { adeptDTO } from './utils';
import {
  IGetAddressInfoParams,
  IGetReturnAddressParams,
  requestParams,
  unbindParams,
  GROUP_ID,
  agreeReject,
  IUpdateParams,
} from './interfaces';
const venture = ASCTools.getCountry() || 'SG';

export const getAddressInfo = ({ addressId, groupId }: IGetAddressInfoParams): Promise<AddressInfo> =>
  axios
    .get('mtop.global.merchant.subaccount.multi.address.edit', {
      params: { addressId, groupId: groupId || GROUP_ID.WAREHOUSE },
    })
    .then(({ data: res }) => {
      const obj = dadaAdaptor(res.data, true);
      obj.groupId = obj.groupId || GROUP_ID.WAREHOUSE;
      return new AddressInfo(obj);
    });

export const updateAddressInfo = (addressInfo: AddressInfo): Promise<Result> => {
  const data = {
    ...addressInfo,
    address1: addressInfo.detail,
    addressCode: addressInfo.code,
    country: addressInfo.venture,
  };
  return axios.post('mtop.global.merchant.subaccount.multi.address.save', data).then(({ data: res }) => {
    return new Result({ success: res.data?.success });
  });
};

export const getInitData = (): Promise<PageInitData> =>
  axios
    .get('mtop.lazada.rc.multi.address.init', {
      // TODO delete this env declaration
      // headers: {
      //   'eagleeye-userdata': 'dpath_env=p2p_enhance',
      // },
    })
    .then(({ data: res }) => new PageInitData(res.data));

export const showaddressGroup = (): Promise<boolean> =>
  axios.get('mtop.lazada.rc.seller.warehouse.group.seller.bind.check').then(({ data: res }) => {
    return !!res.module;
  });

export const getWarehouseList = (pageInfo, query: object): Promise<WarehouseListData> =>
  axios
    .get('mtop.lazada.rc.multi.address.warehouse.list', {
      params: {
        ...pageInfo,
        ...query,
      },
      // TODO delete this env declaration
      // headers: {
      //   'eagleeye-userdata': 'dpath_env=p2p_enhance',
      // },
    })
    .then(
      ({ data: res }) =>
        new WarehouseListData({
          total: res?.data?.pageInfo?.total || 0,
          list: res?.data?.dataSource?.map(item => {
            item.groupId = item.groupId || GROUP_ID.WAREHOUSE;
            return item;
          }),
        }),
    );

let returnAddressPromise;
export const getReturnAddress = (params: IGetReturnAddressParams): Promise<WarehouseListData> => {
  // 退货地址只有一个，不需要每次刷新
  const { force = false } = params || {};
  if (!returnAddressPromise || force) {
    // 中台的 Dada 的老接口获取退货的 addressId, 然后通过 addressId 到 edit 接口获取 return address 的信息
    // mtop.lazada.rc.return.address.warehouse.list  mtop.global.merchant.subaccount.multi.address.init
    returnAddressPromise = axios.get('mtop.lazada.rc.return.address.warehouse.list').then(({ data }) => {
      const dataSource = data?.data?.dataSource || [];
      return new WarehouseListData({
        list: dataSource,
        total: 1,
      });
    });
  }
  return returnAddressPromise;
};

export const getGroup = pageInfo => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.list', { data: { ...pageInfo } })
    .then(res => {
      const list = res?.data?.module?.map(element => {
        const result = adeptDTO(element);
        return result;
      });
      return {
        total: 0,
        list: list || [],
      };
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const unDealwithMsg = () => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.request.inprogress.count')
    .then(res => res?.data?.module)
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return 0;
    });
};

export const requestHistories = (params: requestParams) => {
  const keys = ['bindingType', 'requestType', 'warehouseCode', 'status'];
  keys?.map(el => {
    params[el] = !!params[el] ? params[el] : -1;
  });

  if (params && typeof params?.pageSize !== 'number') params.pageSize = 10;

  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.request.query', {
      data: {
        ...params,
        pageIndex: params?.current,
      },
    })
    .then(res => {
      const list =
        res?.data?.modelList?.map(el => {
          return { ...el, ...el?.senderInfo };
        }) || [];
      return {
        list,
        total: res?.data?.totalCount || 0,
      };
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return {
        list: [],
        total: 0,
      };
    });
};

export const unbind = (params: unbindParams) => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.unbind', {
      data: { ...params },
    })
    .then(res => !!res?.data?.module)
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const ownerLeave = (params: unbindParams) => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.leave', {
      data: { ...params },
    })
    .then(res => !!res?.data?.module);
};

export const rejectInvitation = (params: agreeReject) => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.reject', { data: { ...params } })
    .then(res => !!res?.data?.module)
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const agreeInvitation = (params: agreeReject) => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.approve', { data: { ...params } })
    .then(res => !!res?.data?.module)
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const getAllWarehouses = (): Promise<Array<{ label?: string; value?: string }>> => {
  return axios
    .get('mtop.lazada.rc.multi.address.warehouse.list', {
      data: {
        pageSize: 200,
        current: 1,
      },
    })
    .then(res => {
      const sources = res?.data?.data?.dataSource || [];
      return sources?.map(el => {
        el.label = el?.code;
        el.value = el?.code;
        return el;
      });
    });
};

export const updateRCHolidayMode = (params: IUpdateParams) => {
  // targetTable: settingAllNewWhTable/settingPerWhTable
  // holidayMode: false
  // code: SG101BB-WH-10003
  // date: 2021-06-01 00:00:00,2021-07-01 00:00:00
  // Imigration to RC mtop.global.merchant.subaccount.multi.address.holiday.update

  return axios.post('mtop.lazada.rc.holiday.update', params).catch(e => {
    throw new Error(e.data?.data?.error || e.message);
  });
};
