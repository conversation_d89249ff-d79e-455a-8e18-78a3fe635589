import * as i18n from '@alife/lazada-i18n';
import React, { useEffect } from 'react';
// import { useRequest } from 'ahooks';

import { Button, Message } from '@alifd/next';
import { createForm } from '@formily/core';
import { Field } from '@formily/react';
import { Form, FormItem, Input, FormLayout, FormButtonGroup } from '@formily/next';
import AddressWithRegion from '../../components/address-with-region';
import { getAddressInfo, updateAddressInfo } from './services';
import { useMemoizedFn } from 'ahooks';
import { AddressInfo, Result } from './models';
import commonLocale from '../../constants/common-locale';

export const DialogContent = ({ record, operateType, isCrossBoard, venture, ok, close }) => {
  // console.log('dialog form context', operateType, record);
  // console.log('dialog form values', field.getValues());

  const form = createForm({});

  useEffect(() => {
    if (operateType === 'edit' && record?.addressId) {
      getAddressInfo({ addressId: record.addressId, groupId: record.groupId }).then(info => {
        form.setValues({
          groupId: info.groupId,
          addressId: info.addressId,
          detail: info.detail,
          email: info.email,
          phone: info.phone,
          name: info.name,
          code: info.code,
          isDefault: info.isDefault,
          address: {
            venture: info.venture || venture,
            locationLevel1: info.locationLevel1,
            locationLevel2: info.locationLevel2,
            locationLevel3: info.locationLevel3,
          },
        });
      });
    } else {
      form.setValues({
        address: {
          venture,
        },
      });
    }
  }, [record?.addressId]);

  const onSubmit = useMemoizedFn(() => {
    const addressInfo = new AddressInfo({
      ...form.values,
      ...form.values?.address,
    });

    return updateAddressInfo(addressInfo)
      .then((res: Result) => {
        if (res.success) {
          throw new Error(res.message);
        }
        ok();
      })
      .catch(e => {
        Message.error(e.message);
        throw e;
      });
  });
  const onCancel = () => {
    close();
  };

  return (
    <Form form={form}>
      <FormLayout layout="vertical" wrapperWidth={450} colon={false}>
        <Field
          name="name"
          title={i18n.formatMessage({ id: 'page.account.setting.warehouseName', defaultMessage: 'Warehouse Name' })}
          required
          decorator={[FormItem]}
          component={[Input]}
        />

        <Field
          name="email"
          title={i18n.formatMessage({ id: 'page.account.setting.email', defaultMessage: 'Email' })}
          required
          decorator={[FormItem]}
          component={[Input]}
        />

        <Field
          name="phone"
          title={i18n.formatMessage({ id: 'page.account.setting.phone', defaultMessage: 'Phone' })}
          required
          decorator={[FormItem]}
          component={[Input]}
        />

        <Field
          name="address"
          title={i18n.formatMessage({ id: 'page.account.setting.address', defaultMessage: 'Address' })}
          required
          decorator={[FormItem]}
          component={[AddressWithRegion, { isCrossBoard }]}
        />

        <Field
          name="detail"
          title={i18n.formatMessage({ id: 'page.account.setting.detail', defaultMessage: 'Detail' })}
          required
          decorator={[FormItem]}
          component={[
            Input,
            {
              placeholder: i18n.formatMessage({
                id: 'page.account.setting.pleaseEnterDetailAddress',
                defaultMessage: 'Please enter detail address',
              }),
            },
          ]}
        />
      </FormLayout>
      <FormButtonGroup className="address-dialog-footer">
        <Button loading={form.submitting} onClick={onCancel}>
          {commonLocale['page.common.btn.cancel']}
        </Button>
        <Button
          type="primary"
          loading={form.submitting}
          onClick={() => {
            form.submit(onSubmit);
          }}
        >
          {commonLocale['page.common.btn.submit']}
        </Button>
      </FormButtonGroup>
    </Form>
  );
};
