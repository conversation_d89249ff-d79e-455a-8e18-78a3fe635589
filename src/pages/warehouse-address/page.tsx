import commonLocale from '../../constants/common-locale';
import * as i18n from '@alife/lazada-i18n';
import React, { useEffect, useState } from 'react';
import { Tab, Badge, Message, Button, Tag } from '@alifd/next';
import { PageContainer, SafeHtmlText } from '@alife/asc-components';
import { When, ASCTools } from '@alife/workstation-utils';
import SettingPageTitle from '../../components/setting-page-title';
import locale from '../../constants/warehouse';
import useUrlState from '@ahooksjs/use-url-state';
import List from './components/list';
import Group from './components/group';
import { showaddressGroup, unDealwithMsg } from './services';

import './page.scss';
import { useRequest } from 'ahooks';
import { getCBData } from '../../services/cb';

const Item = Tab.Item;
const country = ASCTools.getCountry();
const currentBU = ASCTools.getBU();

function Page() {
  const [urlParams] = useUrlState();
  const { data: showGroup } = useRequest(showaddressGroup);
  const [activeKey, setKey] = useState<string | number>('list');
  const { data, refresh } = useRequest(unDealwithMsg);

  useEffect(() => {
    const tab = urlParams?.tab;
    ['list', 'group'].includes(tab) && setKey(tab);
  }, [urlParams]);

  return (
    <PageContainer className="warehouse-list-page">
      <SettingPageTitle
        actions={
          currentBU !== 'DARAZ' && (
            <Button
              text
              className="aplus-auto-exp"
              data-spm="d_warehouse_to_myshipment"
              component="a"
              href="/apps/fulfillment/shipment"
            >
              <Tag style={{ marginRight: '4px' }} size="small" color="red">
                {i18n.formatMessage({
                  id: 'page.shipping.provider.title.action.tag',
                  defaultMessage: 'New',
                })}
              </Tag>
              {i18n.formatMessage({
                id: 'page.shipping.provider.title.action',
                defaultMessage: 'Go to My Shipment',
              })}
            </Button>
          )
        }
        title={commonLocale['page.common.nav.warehouse']}
      />

      <When condition={showGroup && !!locale['page.description']}>
        <Message type="notice">
          <SafeHtmlText html={locale['page.description']} />
        </Message>
      </When>

      <When condition={showGroup} else={<List />}>
        <Tab
          activeKey={activeKey}
          defaultActiveKey="list"
          data-spm="warehouse_address_tab"
          onClick={key => {
            key === 'group' && refresh();
            setKey(key);
          }}
        >
          <Item title={locale['warehouse.list']} key="list">
            <List />
          </Item>
          {currentBU !== 'DARAZ' && (
            <Item
              className="aplus-auto-exp"
              data-spm="d_tab_group"
              title={
                <Badge count={data || 0} overflowCount={99}>
                  {locale['pickup.address.group']}
                </Badge>
              }
              key="group"
            >
              <Group />
            </Item>
          )}
        </Tab>
      </When>
    </PageContainer>
  );
}

export default Page;
