import { ASCTools } from '@alife/workstation-utils';
export const existWarehouseCode = (values = {}, key, v) => {
  let exist = false;
  Object.keys(values)?.forEach(el => {
    if (el?.indexOf(key) === 0) {
      const isObj = typeof v === 'object';
      if ((isObj && JSON.stringify(values[el]) === JSON.stringify(v)) || values[el] === v) {
        exist = true;
      }
    }
  });
  return exist;
};

export const venturePhone = (): { maxLength: number; minLength: number; code?: number } => {
  const country = ASCTools.getCountry();
  switch (country) {
    case 'ID':
      return {
        maxLength: 13,
        minLength: 9,
        code: 62,
      };
    case 'MY':
      return {
        maxLength: 10,
        minLength: 9,
        code: 60,
      };
    case 'PH':
      return {
        maxLength: 10,
        minLength: 9,
        code: 63,
      };
    case 'SG':
      return {
        maxLength: 8,
        minLength: 8,
        code: 65,
      };
    case 'TH':
      return {
        maxLength: 9,
        minLength: 8,
        code: 66,
      };
    case 'VN':
      return {
        maxLength: 11,
        minLength: 9,
        code: 84,
      };
    default:
      return {
        maxLength: 11,
        minLength: 1,
        code: 86,
      };
  }
};
