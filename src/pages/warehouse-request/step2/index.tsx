import React, { useEffect, useState } from 'react';
import { ProCard, SubmitAffix } from '@alife/asc-components';
import { Box, Button, Collapse, Icon, Message, Typography } from '@alifd/next';
import { Form, Submit } from '@formily/next';
import { createForm } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { When } from '@alife/workstation-utils';
import * as requestLocale from '../locale';
import { warehouseOwner } from '../interface';
import Request from './request';
import { useCreation, useMemoizedFn } from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import { deleteField, uniqueId, submitAdept, getParams } from './submit-adept';
import { getCache } from '../../../utils/tools';
import { bindGroup, addMembers } from '../service';

interface IProps {
  title?: string;
  warehouseCode?: string;
  warehouseName?: string;
  address?: string;
  ownerWarehouse?: warehouseOwner;
  tip?: string;
}

const Panel = Collapse.Panel;

const { autoFormTrack, setError } = withTrackEffects({
  formKey: 'setting-warehouse-request',
});

function Step(props: IProps) {
  const [params] = useUrlState<{ action?: string; code?: string; groupId?: string }>({});
  const { title, ownerWarehouse = {}, tip = '' } = props;

  const [data, setData] = useState<Array<{ open?: boolean; key: string }>>([]);
  const [formDisabled, setDisabled] = useState(false);
  const [expandKeys, setKeys] = useState<Array<string>>([]);

  const form = useCreation(
    () =>
      createForm({
        effects() {
          autoFormTrack();
        },
      }),
    [],
  );

  useEffect(() => {
    // const { action } = params || {};
    // const ownerCache = getCache('multipleWarehouseAddress');
    if (!ownerWarehouse?.code) {
      setDisabled(true);
    } else {
      setDisabled(false);
    }
    form?.reset();
  }, [ownerWarehouse]);

  const onClick = () => {
    form
      .validate()
      .then(res => {
        const uid = uniqueId();
        expandKeys.push(`expand_${uid}`);
        setKeys([...expandKeys]);
        setData(data.concat({ key: uid, open: true }));
      })
      .catch(e => {
        Message.warning(requestLocale.finishSubAccount);
      });
  };

  const deleteItem = key => {
    const findIndex = data?.findIndex(el => el?.key === key);
    data?.splice(1, findIndex);
    deleteField(form, key);
    setData(data);
    submitAdept(form?.values);
  };

  const submit = useMemoizedFn(values => {
    if (!data?.length) {
      Message.warning(tip);
      return;
    }
    const currentParams = getParams(values, ownerWarehouse);
    const { action } = params || {};
    const addMember = action === 'add';

    const server = addMember ? addMembers : bindGroup;
    server(currentParams).then(res => {
      if (!!res) {
        Message.success(requestLocale.bindSuccess);
        try {
          location.href = '/apps/setting/warehouse_address?tab=group';
        } catch {}
      }
    });
  });

  return (
    <ProCard title={title} className="request-step2-wrap">
      <Form form={form} className="step2-request-form" layout="vertical">
        <Collapse disabled={formDisabled} expandedKeys={expandKeys} onExpand={v => setKeys(v)}>
          {data?.map((el, index) => {
            const key = el?.key || uniqueId();
            const panelKey = `expand_${key}`;
            return (
              <Panel
                key={panelKey}
                title={
                  <Box justify="space-between" margin={[0, 16, 0, 0]} direction="row">
                    <Typography.H4>{requestLocale.invitation}</Typography.H4>
                    <When condition={!expandKeys?.includes(panelKey)}>
                      <Button onClick={() => deleteItem(key)} className="aplus-auto-exp" text>
                        <Icon type="delete" />
                      </Button>
                    </When>
                  </Box>
                }
              >
                <Request formInstance={form} ownerWarehouse={ownerWarehouse} index={key} />
              </Panel>
            );
          })}
        </Collapse>
        <Box justify="flex-start" margin={[16, 0]}>
          <Button
            disabled={formDisabled}
            onClick={onClick}
            className="aplus-auto-exp invitation"
            data-spm="d_new_invitation"
            type="secondary"
          >
            {' '}
            <Icon type="add" />
            {requestLocale.newInvitation}
          </Button>
        </Box>

        <SubmitAffix>
          <Submit className="aplus-auto-exp" data-spm="d_bind" type="primary" disabled={formDisabled} onSubmit={submit}>
            {requestLocale.bind}
          </Submit>
        </SubmitAffix>
      </Form>
    </ProCard>
  );
}

export default Step;
