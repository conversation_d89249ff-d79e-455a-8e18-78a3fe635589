import React, { useRef, useState } from 'react';
import { Grid, Icon, Message, Balloon } from '@alifd/next';
import { FormItem, Input, Select } from '@formily/next';
import { Field } from '@formily/react';
import { venturePhone } from '../utils';
import * as locale from '../locale';
import FieldSelect from '../components/field-select';
import { warehouseOwner } from '../interface';
import { When } from '@alife/workstation-utils';

interface IProps {
  title?: string;
  warehouseCode?: string;
  warehouseName?: string;
  address?: string;
  index?: number | string;
  ownerWarehouse?: warehouseOwner;
  formInstance?: any;
}

const { Row, Col } = Grid;
const Tooltip = Balloon.Tooltip;

const addBefore = venturePhone();

function Request(props: IProps) {
  const { index, ownerWarehouse, formInstance = {} } = props;
  const [singleWarehouse, setWarehouse] = useState(false);
  const [error, setError] = useState('');

  const sellerCodeKey = `sellerCode-${index}`;
  const selleridKey = `sellerId-${index}`;
  const phoneKey = `phone-${index}`;
  const warehouseCodeKey = `warehouseCode-${index}`;
  const warehouseNameKey = `warehouseName-${index}`;
  const warehouseAddressKey = `warehouseAddress-${index}`;

  return (
    <>
      <Row>
        <When condition={!!error}>
          <Message type="error" title={error} />
        </When>
      </Row>
      <Row gutter={16}>
        <Field
          required
          title={
            <>
              {locale.sellerID}
              <Tooltip trigger={<Icon style={{ marginLeft: '4px' }} size="xs" type="help" />}>
                {locale.sellerIdTip}
              </Tooltip>
            </>
          }
          name={sellerCodeKey}
          decorator={[FormItem]}
          component={[Input, {}]}
        />
        <Field
          required
          title={
            <>
              {locale.loginPhoneNumber}
              <Tooltip trigger={<Icon style={{ marginLeft: '4px' }} size="xs" type="help" />}>
                {locale.sellerPhoneTip}
              </Tooltip>
            </>
          }
          name={phoneKey}
          decorator={[FormItem]}
          component={[Input, { addonTextBefore: `+${addBefore.code}` }]}
        />
      </Row>
      <Row gutter={16}>
        <Field
          required
          title={locale.warehouseCode}
          name={warehouseCodeKey}
          decorator={[FormItem]}
          component={[
            FieldSelect,
            {
              formInstance,
              setWarehouse,
              ownerWarehouse,
              sellerCodeKey,
              selleridKey,
              phoneKey,
              warehouseCodeKey,
              warehouseNameKey,
              warehouseAddressKey,
              setError,
            },
          ]}
        />
        <When condition={singleWarehouse}>
          <Field
            required
            title={locale.warehouseName}
            disabled
            name={warehouseNameKey}
            decorator={[FormItem]}
            component={[Input, {}]}
          />
        </When>
      </Row>
      <When condition={singleWarehouse}>
        <Row gutter={16}>
          <Field
            required
            title={locale.warehouseAddress}
            disabled
            name={warehouseAddressKey}
            decorator={[FormItem]}
            component={[Input.TextArea, {}]}
          />
        </Row>
      </When>
    </>
  );
}

export default Request;
