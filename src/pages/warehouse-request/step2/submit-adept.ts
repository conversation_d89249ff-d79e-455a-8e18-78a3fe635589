import { getCache } from '../../../utils/tools';
import { bindParams, warehouseItem } from '../interface';
export const submitAdept = values => {
  values = values || {};
  const groups = new Map();
  Object.keys(values)?.forEach(el => {
    const keyValue = el?.split('-');
    const value = keyValue?.[0] || '';
    const key = keyValue?.[1] || '';
    if (!groups.has(key)) {
      groups.set(key, { [value]: values[el] });
    } else {
      const existValue = groups.get(key);
      groups.set(key, { ...existValue, [value]: values[el] });
    }
  });
  return groups;
};

export const memberList = (
  groups: Map<
    string,
    { sellerCode: string; sellerId: string; warehouseCode: string; warehouseName: string; warehouseAddress: string }
  >,
): Array<warehouseItem> => {
  let list: Array<object> = [];
  groups?.forEach(value => {
    if (Array.isArray(value?.warehouseName) && typeof value?.warehouseName?.[0] === 'object') {
      list = list.concat(value?.warehouseName);
    } else {
      const { sellerCode, sellerId, warehouseCode, warehouseName, warehouseAddress } = value || {};
      const item = {
        sellerId: sellerId,
        sellerShortCode: sellerCode,
        warehouseCode: warehouseCode,
        warehouseName: warehouseName,
        detailAddress: warehouseAddress,
      };
      list.push(item);
    }
  });
  return list;
};

export const getParams = (values, ownerWarehouse): bindParams => {
  const map = submitAdept(values);
  const members = memberList(map);
  const { code, name, sellerId, groupId, sellerShortCode, address } = ownerWarehouse;
  const params = {
    groupId,
    ownerWarehouseInfo: {
      sellerId,
      sellerShortCode,
      warehouseCode: code,
      warehouseName: name,
      detailAddress: address,
    },
    memberWarehouseInfoList: members,
  };
  return params;
};

export const deleteField = (form, outkey) => {
  const values = form?.values || {};
  Object.keys(values)?.forEach(el => {
    const key = el?.split('-')?.[1] || '';
    if (key === outkey) {
      delete values[el];
    }
  });
};

export const uniqueId = () => {
  const value = Math.random()
    .toFixed(8)
    .toString()
    ?.replace('.', '');
  return value.toString();
};
