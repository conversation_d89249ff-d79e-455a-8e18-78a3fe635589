import React, { useState } from 'react';
import commonLocale from '../../constants/common-locale';
import { Message, Step } from '@alifd/next';
import SettingPageTitle from '../../components/setting-page-title';
import { PageContainer } from '@alife/asc-components';
import * as locale from './locale';
import Step1 from './step1';
import Step2 from './step2';

import './index.scss';

const Item = Step.Item;

function Page() {
  const [ownerWarehouse, setOwner] = useState();

  return (
    <PageContainer className="warehouse-request-list-wrap">
      <SettingPageTitle title={commonLocale['page.common.nav.warehouse']} />

      <Message type="notice" title={locale.entranceTips} />

      <Step direction="ver">
        <Item title="Step1" content={<Step1 setOwner={setOwner} title={locale.step1Title} />} />
        <Item
          title="Step2"
          content={<Step2 ownerWarehouse={ownerWarehouse} title={locale.step2Title} tip={locale.step2AddTip} />}
        />
      </Step>
    </PageContainer>
  );
}

export default Page;
