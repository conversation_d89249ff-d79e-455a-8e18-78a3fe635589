import * as i18n from '@alife/lazada-i18n';

export const entranceTips = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.entrance.tip',
  defaultMessage:
    'Create a group to share same pick up address shipping provider and first mile type will be reassigned in group level Your account will be the group owner your warehouse address will be the pick up address for the whole group',
});

export const step1Title = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.step1.title',
  defaultMessage: 'Choose an address to be the pickup address in the group',
});

export const step2Title = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.step2.title',
  defaultMessage: 'Invite members who already shared same address or want to share same address',
});

export const step2AddTip = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.step2.add.tip',
  defaultMessage: 'Please Add Sub Account!',
});

export const step1ConfirmTitle = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.step1.confirm.title',
  defaultMessage: 'Change Preferred Pickup Address',
});

export const step1ConfirmContent = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.step1.confirm.content',
  defaultMessage: 'Are you sure to change preferred pickup address Information in Step 2 will be reset',
});

export const invitation = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.invitation',
  defaultMessage: 'Invitation',
});

export const bind = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.bind',
  defaultMessage: 'Bind',
});

export const bindSuccess = i18n.formatMessage({
  id: 'page.setting.warehouse.address.request.bind.success',
  defaultMessage: 'Bind Success',
});

export const searchForEachWarehouse = i18n.formatMessage({
  id: 'page.setting.warehouse.address.search.warehouse',
  defaultMessage: 'Search Warehouse',
});

export const emailPhoneTip = i18n.formatMessage({
  id: 'page.setting.warehouse.address.search.email.phone',
  defaultMessage: 'Please input seller and phone',
});

export const validateError = i18n.formatMessage({
  id: 'page.setting.warehouse.address.search.email.phone.error',
  defaultMessage: 'There is no available warehouse to bind under this seller ID',
});

export const incorrectEmailPhone = i18n.formatMessage({
  id: 'page.setting.warehouse.address.search.email.phone.incorrect',
  defaultMessage: 'Incorrect Seller ID or Phone number',
});

export const noPermission = i18n.formatMessage({
  id: 'page.setting.warehouse.address.search.warehouse.onpermission',
  defaultMessage: 'This warehouse has no permission',
});

export const existWarehouseTip = i18n.formatMessage({
  id: 'page.setting.warehouse.address.exist.warehouse.tip',
  defaultMessage: 'You have add this warehouses',
});

export const warehouseName = i18n.formatMessage({
  id: 'page.common.sellerConfig.fm.shippingColumns.warehouse.name',
  defaultMessage: 'Warehouse Name',
});

export const warehouseCode = i18n.formatMessage({
  id: 'page.setting.warehouse.address.warehouse.code',
  defaultMessage: 'Warehouse Code',
});

export const warehouseAddress = i18n.formatMessage({
  id: 'page.setting.warehouse.address.address',
  defaultMessage: 'Warehouse Address',
});

export const newInvitation = i18n.formatMessage({
  id: 'page.warehouse.request.newInvitation',
  defaultMessage: 'New Invitation',
});

export const finishSubAccount = i18n.formatMessage({
  id: 'page.setting.warehouse.address.finish.sub.account',
  defaultMessage: 'Please finish current Sub Account',
});

export const sellerID = i18n.formatMessage({
  id: 'page.setting.warehouse.address.account.sellerid',
  defaultMessage: 'Seller ID',
});

export const sellerIdTip = i18n.formatMessage({
  id: 'page.setting.warehouse.address.sellerid.tip',
  defaultMessage: 'Tip',
});

export const loginPhoneNumber = i18n.formatMessage({
  id: 'page.setting.warehouse.address.login.number',
  defaultMessage: 'Login Phone Numebr',
});

export const sellerPhoneTip = i18n.formatMessage({
  id: 'page.setting.warehouse.address.seller.phone.tip',
  defaultMessage: 'Tip',
});
