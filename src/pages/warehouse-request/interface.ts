export interface warehouseItem {
  sellerId?: string;
  sellerShortCode?: string;
  warehouseCode?: string;
  warehouseName?: string;
  detailAddress?: string;
}
export interface bindParams {
  ownerWarehouseInfo: warehouseItem;
  memberWarehouseInfoList: Array<warehouseItem>;
  groupId?: number | string;
}

export interface bindParam {
  currentWarehouseCode: string;
  groupMemberSellerPhone?: string;
  groupMemberSellerShortCode?: string;
}

export interface warehouseOwner {
  name?: string;
  address?: string;
  code?: string;
  addressId?: string;
}
