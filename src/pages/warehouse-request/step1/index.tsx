import React, { useEffect } from 'react';
import { ProCard } from '@alife/asc-components';
import { Box, Select, Typography, Dialog, Message } from '@alifd/next';
import Item from '../item';
import { useMemoizedFn, useRequest } from 'ahooks';
import { validatePermission, getAllWarehouses } from '../service';
import { warehouseOwner } from '../interface';
import * as requestLocale from '../locale';
import useUrlState from '@ahooksjs/use-url-state';
import { getCache } from '../../../utils/tools';
import { useState } from 'react';

interface IProps {
  title?: string;
  warehouseCode?: string;
  warehouseName?: string;
  address?: string;
  setOwner?: (v) => void;
}

function Step(props: IProps) {
  const { title, setOwner = () => {} } = props;
  const [params] = useUrlState<{ action?: string }>({});
  const [warehouse, setWarehouse] = useState<warehouseOwner>({});
  const { data } = useRequest(getAllWarehouses);
  const [disabled, setDisabled] = useState(false);
  const [value, setValue] = useState<string>('');
  const ownerCache = getCache('multipleWarehouseAddress');

  useEffect(() => {
    initialWarehouse();
  }, [data]);

  const initialWarehouse = async () => {
    if (!data) return;
    const onlyOne = data?.length === 1;
    let item: any = null;
    const { code, detailAddress: address, warehouseName: name } = ownerCache || {};

    if (params?.action === 'add' && !!code) {
      item = { ...ownerCache, label: code, value: code, address, name, code };
    } else if (onlyOne) {
      item = data[0];
      if (!!item?.code) {
        const permission = await validate({ currentWarehouseCode: item?.code });
        if (!permission) {
          return;
        }
      }
    }
    if (!!item) {
      setWarehouse(item);
      setOwner({ ...ownerCache, ...item });
      setValue(item.value);
      setDisabled(true);
    }
  };

  const validate = async v => {
    const res = await validatePermission(v);
    if (!res) {
      Message.warning(requestLocale.noPermission);
      return false;
    }
    return true;
  };

  const updateWarehouse = item => {
    setOwner({ ...ownerCache, ...item });
    setWarehouse(item);
  };

  const onSelect = async (v, index, item) => {
    const permission = await validate({ currentWarehouseCode: v });
    if (!permission) return;

    if (!warehouse?.addressId) {
      updateWarehouse(item);
      setValue(v);
      return;
    }
    if (item.addressId !== warehouse?.addressId) {
      Dialog.confirm({
        title: requestLocale.step1ConfirmTitle,
        content: requestLocale.step1ConfirmContent,
        onOk: () => {
          updateWarehouse(item);
          setValue(v);
        },
      });
    }
  };

  return (
    <ProCard title={title}>
      <Box spacing={24} className="step1-request-show" direction="row">
        <Box className="first-row">
          <Typography.Text className="warehouse-code">{requestLocale.warehouseCode}</Typography.Text>
          <Select value={value} disabled={disabled} onChange={onSelect} dataSource={data} />
        </Box>
        <Box>
          <Item className="right-panel" label={`${requestLocale.warehouseName}:`} value={warehouse?.name} />
          <Item label={`${requestLocale.warehouseAddress}:`} value={warehouse?.address} />
        </Box>
      </Box>
    </ProCard>
  );
}

export default Step;
