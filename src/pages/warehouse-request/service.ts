import { axios } from '@alife/workstation-utils';
import { bindParams, bindParam } from './interface';
import { Message } from '@alifd/next';

export const allowCreateGroup = currentWarehouseCode => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.bind.check', { data: { currentWarehouseCode } })
    .then(res => {
      return !!res?.data?.module;
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const validatePermission = (params: bindParam): Promise<boolean> => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.bind.check', {
      data: {
        ...params,
      },
    })
    .then(res => {
      return !!res?.data?.module;
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const bindGroup = ({ ownerWarehouseInfo = {}, memberWarehouseInfoList }: bindParams) => {
  delete ownerWarehouseInfo.sellerShortCode;
  ownerWarehouseInfo.sellerId = window?.dadaConfig?.sellerId;

  return axios
    .post('mtop.lazada.rc.seller.warehouse.group.bind', {
      ownerWarehouseInfo,
      memberWarehouseInfoList,
    })
    .then(res => {
      return !!res?.data?.module;
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};
export const addMembers = ({ ownerWarehouseInfo, memberWarehouseInfoList, groupId }: bindParams) => {
  if (ownerWarehouseInfo && !ownerWarehouseInfo.sellerId) ownerWarehouseInfo.sellerId = window?.dadaConfig?.sellerId;
  return axios
    .post('mtop.lazada.rc.seller.warehouse.group.add.member', {
      ownerWarehouseInfo,
      memberWarehouseInfoList,
      groupId,
    })
    .then(res => {
      console.log(res);
      return !!res?.data?.module;
    })
    .catch(e => {
      Message.error(e?.data?.data?.errorCode?.displayMessage);
      return false;
    });
};

export const warehouseList = (params: bindParam) => {
  return axios
    .get('mtop.lazada.rc.seller.warehouse.group.can.bind.list', {
      data: { ...params },
    })
    .then(res => {
      const sources = res?.data?.module;
      const success = res?.data?.success;
      if (success === false) return false;
      if (success === true && !sources) return 'invalid';
      return sources?.map(el => {
        el.value = el?.code;
        el.label = el?.code;
        return el;
      });
    });
};

export const getAllWarehouses = () => {
  return axios
    .get('mtop.lazada.rc.multi.address.warehouse.list', {
      data: {
        pageSize: 200,
        current: 1,
      },
    })
    .then(res => {
      const sources = res?.data?.data?.dataSource || [];
      return sources?.map(el => {
        el.label = el?.code;
        el.value = el?.code;
        return el;
      });
    });
};
