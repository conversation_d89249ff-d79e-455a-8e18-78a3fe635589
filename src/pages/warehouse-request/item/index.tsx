import React from 'react';
import { Box, Typography } from '@alifd/next';

interface IProps {
  label?: string;
  value?: string;
  required?: boolean;
  className?: string;
}

function Item({ label, value, required, className }: IProps) {
  return (
    <Box className={className}>
      <Typography.Text className="warehouse-label">{label}</Typography.Text>
      <Box className="warehouse-value">{value}</Box>
    </Box>
  );
}

export default Item;
