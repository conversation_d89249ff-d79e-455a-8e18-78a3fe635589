.select-warehouse-wrap {
  position: relative;
  .search-button {
    position: absolute;
    top: -72px;
    right: -449px;
  }
}

.warehouse-address-request-code-select {
  .next-menu.next-ver .next-menu-item {
    // padding: 0 8px;
    margin: 5px;
  }
  .next-menu-item-inner {
    height: initial;
  }
  .item-select {
    border-radius: 4px;
    padding: 5px;
    .next-typography {
      font-size: 14px;
      line-height: 20px;
    }
    .value {
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      color: #333333;
    }
  }
  .item-select:hover {
    background-color: #f2f3f7;
  }
}
