import React, { useEffect, useState } from 'react';
import { warehouseList } from '../../service';
import { Message, Box, Button, Typography } from '@alifd/next';
import { Select } from '@formily/next';
import { existWarehouseCode } from '../../utils';
import {
  searchForEachWarehouse,
  emailPhoneTip,
  validateError,
  incorrectEmailPhone,
  existWarehouseTip,
  warehouseName,
  warehouseAddress,
} from '../../locale';
import { useMemoizedFn } from 'ahooks';

import './index.scss';

function FeildSelect({
  formInstance,
  setWarehouse,
  selleridKey,
  sellerCodeKey,
  warehouseCodeKey,
  warehouseNameKey,
  warehouseAddressKey,
  phoneKey,
  ownerWarehouse,
  setError,
}) {
  const [dataSource, setDataSource] = useState<Array<{ label?: string; value?: string }>>([]);
  const [value, setValue] = useState<Array<string> | string>();
  const { values = {} } = formInstance;

  const singleWarehouse = dataSource?.length <= 1;

  const onSearch = () => {
    if (!values[sellerCodeKey] || !values[phoneKey]) {
      Message.warning(emailPhoneTip);
      return;
    }
    loadData({
      currentWarehouseCode: ownerWarehouse?.code,
      groupMemberSellerShortCode: values[sellerCodeKey],
      groupMemberSellerPhone: values[phoneKey],
    })
      .then(data => {
        if (!data || data?.length === 0) {
          setError(validateError);
          return;
        }
        if (data === 'invalid') {
          setError(incorrectEmailPhone);
          return;
        }

        setError('');
        if (data?.length <= 1) {
          setWarehouse(true);
        } else {
          setWarehouse(false);
          setValue([]);
        }
        setDataSource(data);
      })
      .catch(e => {
        const key = e?.data?.data?.errorCode?.key;
        const err = key === 'LZD_RC_WAREHOUSE_GROUP_INVALID_SELLER' ? incorrectEmailPhone : validateError;
        // const msg = e?.data?.data?.errorCode?.displayMessage;
        setError(err);
      });
  };

  const loadData = useMemoizedFn(param => {
    return warehouseList(param);
  });

  const itemRender = (item, v) => {
    const { name, address, value: code } = item || {};
    if (singleWarehouse) return code;
    return (
      <Box className="item-select" direction="column">
        <Typography.H6>{code}</Typography.H6>
        <Typography.Text>{warehouseName}:</Typography.Text>
        <Typography.Text className="value">{name}</Typography.Text>
        <Typography.Text>{warehouseAddress}:</Typography.Text>
        <Typography.Text className="value">{address}</Typography.Text>
      </Box>
    );
  };

  const selectChange = (v, index, item) => {
    if (existWarehouseCode(values, 'warehouseCode-', v)) {
      Message.error(existWarehouseTip);
      return;
    }
    setValue(v);
    if (!!singleWarehouse) {
      const { sellerId, address, name } = item || {};
      formInstance?.setValues({
        [selleridKey]: sellerId,
        [warehouseCodeKey]: v,
        [warehouseNameKey]: name,
        [warehouseAddressKey]: address,
      });
    } else {
      // for multiple warehouses
      formInstance?.setValues({
        [warehouseCodeKey]: v,
        [warehouseNameKey]: item?.map(el => {
          const sellerShortCode = values?.[sellerCodeKey];
          const warehouseCode = el?.code;
          const wName = el?.name;
          const detailAddress = el?.address;
          const sellerId = el?.sellerId;
          return { sellerId, sellerShortCode, warehouseCode, warehouseName: wName, detailAddress };
        }),
      });
    }
  };

  return (
    <Box className="select-warehouse-wrap">
      <Select
        dataSource={dataSource}
        popupClassName="warehouse-address-request-code-select"
        itemRender={itemRender}
        value={value}
        onChange={selectChange}
        mode={singleWarehouse ? 'single' : 'multiple'}
      />
      <Button type="primary" data-spm="d_warehosue_search" className="aplus-auto-exp search-button" onClick={onSearch}>
        {searchForEachWarehouse}
      </Button>
    </Box>
  );
}

export default FeildSelect;
