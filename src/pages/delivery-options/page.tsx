import * as $18n from '@alife/lazada-i18n';
import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React, { useState } from 'react';
import { Button, Card, Loading, Field, Table, Icon, Box } from '@alifd/next';
import { Pagination, PageContainer, usePromisePercent } from '@alife/asc-components';
import { useFusionTable, useMemoizedFn } from 'ahooks';
// import { useTableUrlState } from './hooks';
import { ColumnItem } from './models';
import SettingPageTitle from '../../components/setting-page-title';

import './page.scss';
import { axios } from '@alife/workstation-utils';

//@ts-ignore
const sellerId = window.dadaConfig?.sellerId;

const colums = [
  {
    title: $18n.formatMessage({ id: 'page.account.setting.type', defaultMessage: 'Type' }),
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: $18n.formatMessage({ id: 'page.account.setting.available', defaultMessage: 'Available' }),
    dataIndex: 'available',
    key: 'available',
  },
];

function Page() {
  // const syncUrlParams = useTableUrlState();
  // const field = Field.useField({});

  const { tableProps, loading, run, error } = useFusionTable(() =>
    // mtop.lazada.asc.seller.product.deliveryoptions.query mtop.lazada.asc.seller.product.deliveryoptions.dbs.query
    axios.get('mtop.lazada.asc.seller.product.deliveryoptions.query').then(({ data: res }) => {
      const data = res?.data;
      if (data?.[0]) {
        return {
          list: data[0].dataSource,
          total: data[0].dataSource.length,
        };
      }
      return {
        list: res?.data.dataSource || [],
        total: res?.data.dataSource?.length,
      };
    }),
  );

  const renderError = useMemoizedFn(() => (
    <Box align="center" className="error-tip-box">
      <div className="error-tip">
        {i18n.formatMessage({
          id: 'page.account.setting.someting.wrong',
          defaultMessage: 'Something went wrong, please try again later!',
        })}
      </div>
      <Button
        loading={loading}
        onClick={() => {
          run({ current: 1, pageSize: 1000 });
        }}
      >
        {i18n.formatMessage({ id: 'page.account.setting.clickToReload', defaultMessage: 'Click to Reload' })}
      </Button>
    </Box>
  ));

  const renderCell = useMemoizedFn((v, k) => {
    if (!['available'].includes(k)) {
      return v;
    }
    if (!v) {
      return <Icon type="close" style={{ color: '#FF3333' }} size="small" />;
    }
    return <Icon type="select" style={{ color: '#1DC11D' }} />;
  });

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.deliveryOptions']} />

      <Card showHeadDivider={false} showTitleBullet={false}>
        {!!error ? (
          renderError()
        ) : (
          <>
            <Table
              rowProps={(_, index) => ({
                'data-spm': `wh_tb_${index}`,
              })}
              {...tableProps}
            >
              {colums.map((item: ColumnItem) => (
                <Table.Column {...item} cell={(v, index, record) => renderCell(v, item.key)} />
              ))}
            </Table>
          </>
        )}
      </Card>
    </PageContainer>
  );
}

export default Page;
