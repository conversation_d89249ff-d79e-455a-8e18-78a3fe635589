import useUrlState from '@ahooksjs/use-url-state';

export const useTableUrlState = (urlKey: string = 'table', { onSuccess } = {} as any) => {
  const [state, setState] = useUrlState({
    [urlKey]: JSON.stringify([{ pageSize: 10, current: 1 }, {}]),
  });

  const nextSuccess = (...args) => {
    const [, [pageInfo, payload]] = args;
    setState({ [urlKey]: JSON.stringify([pageInfo, payload]) });
    onSuccess && onSuccess(args);
  };

  const defaultParams = JSON.parse(state[urlKey]);

  return {
    defaultParams,
    onSuccess: nextSuccess,
  };
};
