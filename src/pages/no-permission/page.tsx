import React from 'react';
import { axios } from '@alife/workstation-utils';
import { PageContainer, PageTitle, Image, SafeHtmlText } from '@alife/asc-components';
import { When, Url } from '@alife/workstation-utils';
import { Card } from '@alifd/next';
import { useRequest } from 'ahooks';
import commonLocale from '../../constants/common-locale';
import './page.scss';

function ResultPage() {
  const originUrl = Url.getQueryParams()['_location_original_uri'] || '';
  const { data } = useRequest(() => {
    return axios
      .post('mtop.global.merchant.subaccount.user.no.permission.message', {
        resourceUri: decodeURIComponent(originUrl),
      })
      .then(({ data }) => data?.data)
      .catch(e => {
        return e?.data?.data;
      });
  });
  return (
    <PageContainer className="aplus-module-auto-exp acl-error-page" data-spm="permission_container">
      <PageTitle data-spm="title" title={commonLocale['page.common.nav.nopermission']} />
      <Card className="content-card" contentHeight="100%">
        <Image
          src="https://lzd-img-global.slatic.net/g/tps/imgextra/i3/O1CN01qYe5kq1ayKXK40LwT_!!*************-55-tps-123-117.svg"
          height={200}
          width={210}
        />
        <When condition={data?.errorMessage} else={<div className="content">{data?.error}</div>}>
          <SafeHtmlText className="content" html={data?.errorMessage} />
          {/* <div className="content">{data?.errorMessage}</div> */}
        </When>
      </Card>
    </PageContainer>
  );
}

export default ResultPage;
