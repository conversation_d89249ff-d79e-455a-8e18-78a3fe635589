import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React, { useState } from 'react';
import { Button, Card, Box, Field, Table, Icon } from '@alifd/next';
import { Pagination, PageContainer } from '@alife/asc-components';
import { useFusionTable, useMemoizedFn } from 'ahooks';
import { useTableUrlState } from './hooks';
import { ColumnItem } from './models';
import SettingPageTitle from '../../components/setting-page-title';

import './page.scss';
import { axios } from '@alife/workstation-utils';

function Page() {
  const syncUrlParams = useTableUrlState();
  const field = Field.useField({});

  const [isError, setIsError] = useState(false);
  const [tableColumns, setTableColumns] = useState([]);

  const { paginationProps, tableProps, loading, run } = useFusionTable(
    pageInfo =>
      axios
        .get('/seller/profile/shipping', {
          params: {
            pageIndex: pageInfo.current,
            pageSize: pageInfo.pageSize,
          },
        })
        .catch(e => {
          setIsError(true);
          console.error(e);
        })
        .then(res => {
          if (res?.status !== 200 || !res?.data?.tableRows) {
            setIsError(true);
            return { total: 0, list: [] };
          }

          setIsError(false);
          setTableColumns(res?.data?.tableColumns || []);
          return {
            total: res?.data?.pagination?.total || 0,
            list: res?.data?.tableRows,
          };
        }),
    {
      field,
      ...syncUrlParams,
      defaultPageSize: 20,
    },
  );

  const renderError = useMemoizedFn(() => (
    <Box align="center" className="error-tip-box">
      <div className="error-tip">
        {i18n.formatMessage({
          id: 'page.account.setting.someting.wrong',
          defaultMessage: 'Something went wrong, please try again later!',
        })}
      </div>
      <Button
        loading={loading}
        onClick={() => {
          run({ current: paginationProps.current, pageSize: paginationProps.pageSize || 20 });
        }}
      >
        {i18n.formatMessage({ id: 'page.account.setting.clickToReload', defaultMessage: 'Click to Reload' })}
      </Button>
    </Box>
  ));

  const renderCell = useMemoizedFn((v, k) => {
    if (!['is_default', 'enabled', 'cashOnDelivery'].includes(k)) {
      return v;
    }
    if (!v) {
      if (k === 'is_default') {
        return;
      }
      return <Icon type="close" style={{ color: '#FF3333' }} size="small" />;
    }
    return <Icon type="select" style={{ color: '#1DC11D' }} />;
  });

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.shipmentProvider']} />

      <Card showHeadDivider={false} showTitleBullet={false}>
        {isError ? (
          renderError()
        ) : (
          <>
            <Table
              rowProps={(_, index) => ({
                'data-spm': `wh_tb_${index}`,
              })}
              {...tableProps}
            >
              {tableColumns.map((item: ColumnItem) => (
                <Table.Column
                  title={item.text}
                  dataIndex={item.key}
                  key={item.key}
                  cell={v => renderCell(v, item.key)}
                />
              ))}
            </Table>
            <Pagination
              style={{ marginLeft: 5 }}
              hideOnlyOnePage
              {...{ ...paginationProps, pageSizeSelector: false }}
            />
          </>
        )}
      </Card>
    </PageContainer>
  );
}

export default Page;
