import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React from 'react';
import { NavTab as Tab, PageContainer, PageTitle } from '@alife/asc-components';
import { Card } from '@alifd/next';
import SettingPageTitle from '../../components/setting-page-title';

import './page.scss';

function ResultPage() {
  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={commonLocale['page.common.nav.serviceArea']} />

      <Card>links</Card>
    </PageContainer>
  );
}

export default ResultPage;
