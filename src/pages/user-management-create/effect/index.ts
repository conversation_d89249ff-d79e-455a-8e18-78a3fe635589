import {
  onFormInit,
} from '@formily/core';
import { patchFieldState } from '../../../utils/patchFieldState';
import * as Service from '../service';

export const useInitData = () => {
  onFormInit(async form => {
    const mapFields = await Service.initRequest();
    const data = Object.entries(mapFields).map(([name, value]) => ({
      name,
      ...value,
    }));
    if (data) {
      data.forEach(item => {
        if (item.name) {
          patchFieldState(form.setFieldState, item);
        }
      });
    }
  });
};

// function(params, elementDataObj) {
//   return new Promise(
//     function (resolve, reject) {
//       if (
//         elementDataObj.model.roles.value[0] === elementDataObj.model.needOtpRoles.value[0]
//         && !elementDataObj.model.verificationDialog.verifiedSuccess) {
//         elementDataObj.model.verificationDialog.visible = true;
//         elementDataObj.model.verificationDialog.formDataObjForMD5 = elementDataObj.model;
//       } else {
//         resolve()
//       }
//     });
// }

// function fn(model) {
//   if (model.verificationDialog.verifiedSuccess) {
//     console.log(model, store, 'xxx');
//     store.runAction([
//       {
//         "eventType": "submit",
//         "request": {
//           "url": "mtop.global.merchant.subaccount.user.save",
//         }
//       }
//     ]);
//   }
//   return !model.table.dataSource.length
// }

// function fn(effectResult, model){model.checkbox.disabled=effectResult}
