import React from 'react';
import { Field, ArrayField } from '@formily/react';
import { FormItem } from '@formily/next';
const itemStyles = {
  labelStyle: {
    fontSize: '16px',
  },
  asterisk: false,
};

export const NextField: typeof Field = props => <Field decorator={[FormItem, itemStyles]} {...props} />;
NextField.displayName = 'next-field';

export const ArrayNextField: typeof Field = props => <ArrayField decorator={[FormItem, itemStyles]} {...props} />;
ArrayNextField.displayName = 'array-next-field';
