import * as i18n from '@alife/lazada-i18n';
import React, { useState } from 'react';
import { useFormDialog, IDialogConfig, IDialogProps, showFunction, Result } from '@alife/asc-components';
import { axios } from '@alife/workstation-utils';
import { useCreation } from 'ahooks';
import { createForm, Field, onFormMount } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import { FormConsumer, createSchemaField } from '@formily/react';
import { Button, Message } from '@alifd/next';
import { Form, FormStep, FormItem, Input, FormLayout, FormButtonGroup } from '@formily/next';
import commonLocale from '../../../../constants/common-locale';
import VerificationContact from '../../../../hooks/update-contact/verification-contact';
import VerificationCode from '../../../../hooks/update-contact/verification-code';
import { useMemoizedFn } from 'ahooks';
import { getSign } from '../../service';

// 由于有多个 OTP 流程，各个流程的接口不能复用，
// 这里把更新联系方式的 OTP 流程的组件全部放在一起
// 避免跟其他流程混淆

// udpate Login Account OTP bizcode
const OTP_BIZ_CODE = 'addNewUser';
const SchemaField = createSchemaField({
  components: {
    FormItem,
    FormStep,
    Input,
    VerificationCode,
    VerificationContact,
    Result,
  },
});

const AddSubAccountContent = props => {
  const record = props.record;
  const phoneFields = ['phone', 'individual_phone'];
  const isUpdatePhone = phoneFields.includes(record.fieldName);

  const [formContact, formStep, setTrackerError] = useCreation(() => {
    const { autoFormTrack, setError } = withTrackEffects({
      formKey: 'add_sub_account_verify',
    });
    return [
      createForm({
        effects() {
          autoFormTrack();
          onFormMount(() => {
            getSign(props.record).then(fingerPrint => {
              formContact.setFieldState('verification', (field: Field) => {
                field.setComponentProps({
                  fingerPrint,
                });
              });
            });
          });
        },
      }),
      FormStep?.createFormStep?.(),
      setError,
    ];
  }, [record?.name]);

  const onSubmit = useMemoizedFn(() => {
    const updateService = axios.post('mtop.global.merchant.subaccount.user.save', {
      ...record,
    });
    return updateService
      .then(() => {
        props.ok();
      })
      .catch(e => {
        const message = e.data?.data?.error || e.message;
        Message.error(message);
        setTrackerError(message);
      });
  });

  return (
    <div data-spm="um_verify_dialog">
      <Form form={formContact} labelCol={6} wrapperCol={10}>
        <FormLayout layout="vertical">
          <SchemaField>
            <SchemaField.Void x-component="FormStep" x-component-props={{ formStep }}>
              <SchemaField.Void
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: i18n.formatMessage({
                    id: 'page.account.setting.pleaseVerifyYourAccount',
                    defaultMessage: 'Please verify your account',
                  }),
                }}
              >
                <SchemaField.String
                  name="verification"
                  x-decorator="FormItem"
                  x-validator={{
                    triggerType: 'onBlur',
                    validator: VerificationCode.validator,
                  }}
                  x-component="VerificationCode"
                  x-component-props={{
                    bizCode: OTP_BIZ_CODE,
                  }}
                />
              </SchemaField.Void>
              <SchemaField.Void
                type="void"
                x-component="FormStep.StepPane"
                x-component-props={{
                  title: commonLocale['page.common.btn.confirm'],
                }}
              >
                <SchemaField.String
                  name="review"
                  x-decorator="FormItem"
                  x-component="Result"
                  x-component-props={{
                    type: 'success',
                    title: i18n.formatMessage({
                      id: 's-wb-subaccount@verifiedSuccess',
                      defaultMessage: 'Verified Successfully',
                      app: 's-wb-subaccount',
                    }),
                    style: {
                      minHeight: 'auto',
                    },
                    imgUrl:
                      'https://img.alicdn.com/imgextra/i1/O1CN01OqJoVc1ONtdeuPXLc_!!*************-2-tps-607-527.png',
                  }}
                  x-reactions={field => {
                    field.componentProps.newContact = isUpdatePhone
                      ? formContact.values.newContact?.phone
                      : formContact.values.newContact?.email;
                  }}
                />
              </SchemaField.Void>
            </SchemaField.Void>
          </SchemaField>
        </FormLayout>
        <FormConsumer>
          {() => (
            <FormButtonGroup className="dialog-footer-btn-group">
              <Button
                disabled={!formStep?.allowBack}
                onClick={() => {
                  formStep?.back();
                }}
              >
                {commonLocale['page.common.btn.previous']}
              </Button>
              <Button
                type="primary"
                disabled={!formStep?.allowNext}
                loading={formContact.validating}
                onClick={() => {
                  formStep?.next();
                }}
              >
                {commonLocale['page.common.btn.next']}
              </Button>
              <Button
                type="primary"
                loading={formContact.submitting}
                disabled={formStep?.allowNext}
                onClick={() => {
                  formStep?.submit(onSubmit);
                }}
              >
                {commonLocale['page.common.btn.submit']}
              </Button>
            </FormButtonGroup>
          )}
        </FormConsumer>
      </Form>
    </div>
  );
};

export const useVerifySubAccount = (
  config: IDialogConfig = {} as IDialogConfig,
): [React.FC<IDialogProps>, { onContactEdit: showFunction }] => {
  const [Dialog, { view }] = useFormDialog({
    content: AddSubAccountContent,
    ...config,
    locale: {
      successMessage: commonLocale['page.common.tips.success'],
      failMessage: commonLocale['page.common.tips.failed'],
    },
  });

  return [Dialog, { onContactEdit: view }];
};
