import { Message } from '@alifd/next';
import { axios } from '@alife/workstation-utils';

export const submitRequest = data => {
  return axios
    .post('mtop.global.merchant.subaccount.user.save', data)
    .then(res => res?.data?.data)
};

export const initRequest = () => {
  return axios.get('mtop.global.merchant.subaccount.user.form.render').then(res => {
    const data = res.data.data;
    return data.map(item => {
      if (item.name === 'roles' && !!item.value) {
        return {
          ...item,
          value: item.value?.[0]
        }
      }
      return item
    })
  });
};

export const getSign = (data: any = {}): Promise<string> => {
  return axios.post('mtop.global.merchant.subaccount.otp.sign', {
    rawMap: {
      roles: data.roles,
      email: data.email,
      userName: data.userName,
      password: data.password
    }
  }).then(res => res?.data?.data).catch(err => Message.error(err?.message))
};
