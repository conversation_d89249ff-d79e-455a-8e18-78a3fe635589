import * as i18n from '@alife/lazada-i18n'; // import * as i18n from '@alife/lazada-i18n';
import commonLocale from '../../constants/common-locale';
import React from 'react';
import { PageContainer, SubmitAffix } from '@alife/asc-components';
import { Button, Card, Box, Message } from '@alifd/next';
import { useCreation } from 'ahooks';
import { Form, Submit, Select, Input, Checkbox } from '@formily/next';
import { createForm } from '@formily/core';
import { withTrackEffects } from '@alife/form-track-plugin';
import SettingPageTitle from '../../components/setting-page-title';
import * as locale from './locale';
import { submitRequest } from './service';
import { ArrayNextField, NextField } from './components';
import { useInitData } from './effect';
import { useVerifySubAccount } from './components';
import './page.scss';
import { awaitCatch } from '@alife/workstation-utils';

const { autoFormTrack, setError } = withTrackEffects({
  formKey: 'form-user-management-create',
  submitUrl: 'mtop.global.merchant.flexi.combo.update',
});

const commonStyle = {
  width: 300,
};

function App() {
  const [form] = useCreation(
    () => [
      createForm({
        effects() {
          useInitData();
          autoFormTrack();
        },
      }),
    ],
    [],
  );
  const [Dialog, { onContactEdit }] = useVerifySubAccount({
    dialogProps: {
      title: i18n.formatMessage({
        id: 's-wb-subaccount@makeSureText',
        defaultMessage: 'Let’s Make Sure It’s really you',
        app: 's-wb-subaccount',
      }),
    },
  });
  const onSubmit = async (values: any = {}) => {
    // 往下提交
    const { needOtpRoles = [], roles } = values;
    const IsNeedOtpVerify = !!needOtpRoles?.find(item => item === roles);
    const data = {
      ...values,
      roles: [roles],
    };
    if (IsNeedOtpVerify) {
      onContactEdit(data).then(res => {
        location.href = '/apps/setting/member/list';
      });
    } else {
      const [err] = await awaitCatch(submitRequest(data));
      if (err) {
        const message = err.data?.data?.error || err.message;
        Message.error(message);
        setError(message);
        return;
      }
      location.href = '/apps/setting/member/list';
    }
  };

  return (
    <PageContainer className="manage-image-page">
      <SettingPageTitle title={locale.title} />
      <Form layout="vertical" form={form} data-spm="d_create_form">
        <Card style={{ marginBottom: 20 }}>
          <Box spacing={16}>
            <NextField hidden name="needOtpRoles" />
            <ArrayNextField
              name="roles"
              required
              component={[
                Select,
                {
                  style: commonStyle,
                },
              ]}
            />
            <NextField
              name="email"
              required
              component={[
                Input,
                {
                  style: commonStyle,
                },
              ]}
            />
            <NextField
              name="userName"
              required
              component={[
                Input,
                {
                  // multiple: true,
                  style: commonStyle,
                  maxLength: 30,
                },
              ]}
            />
            <NextField
              name="language"
              required
              component={[
                Select,
                {
                  style: commonStyle,
                },
              ]}
            />
            <NextField
              name="password"
              required
              visible={false}
              component={[
                Input,
                {
                  style: commonStyle,
                  htmlType: 'password',
                },
              ]}
            />
            <NextField
              name="isSendEmail"
              dataSource={[
                {
                  label: i18n.formatMessage({
                    id: 's-wb-subaccount@isSendEmail',
                    defaultMessage: 'Send a username and password to a sub-account email',
                    app: 's-wb-subaccount',
                  }),
                  value: true,
                },
              ]}
              component={[
                Checkbox.Group,
                {
                  style: commonStyle,
                },
              ]}
            />
          </Box>
        </Card>
        <SubmitAffix>
          <Button className="aplus-auto-exp" data-spm="d_back" component={'a'} href="/apps/setting/member/list">
            {commonLocale['page.common.btn.cancel']}
          </Button>
          <Submit
            className="aplus-auto-exp"
            data-spm="d_submit"
            onSubmit={onSubmit}
            onSubmitFailed={() => {
              const errors = document.getElementsByClassName('next-formily-item-error-help');
              if (errors) {
                errors?.[0].scrollIntoView({
                  block: 'center',
                });
              }
            }}
          >
            {commonLocale['page.common.btn.submit']}
          </Submit>
        </SubmitAffix>
      </Form>
      <Dialog />
    </PageContainer>
  );
}

export default App;
