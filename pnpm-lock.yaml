lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      '@ahooksjs/use-url-state':
        specifier: ^2.5.6
        version: 2.5.12(react-router@5.3.4(react@16.14.0))(react@16.14.0)
      '@ali/filebroker':
        specifier: ^0.2.6
        version: 0.2.6(@ali/trace-sdk@1.3.25)
      '@ali/global-locale':
        specifier: ^1.0.5
        version: 1.0.5
      '@ali/global-string-format':
        specifier: ^1.0.7
        version: 1.0.7
      '@alife/asc-campaign-components':
        specifier: ^1.0.3
        version: 1.0.6(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/asc-components':
        specifier: ^1.30.8
        version: 1.39.4(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alife/asc-logistics-components':
        specifier: ^1.1.14
        version: 1.1.14(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(typescript@5.4.5)
      '@alife/form-track-plugin':
        specifier: ^1.1.0
        version: 1.1.0(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(@babel/core@7.24.5)(@formily/core@2.3.1)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      '@alife/intl-util':
        specifier: ^1.0.131
        version: 1.0.131
      '@alife/lago-utils':
        specifier: ^1.0.2
        version: 1.0.2(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alife/lazada-asc-theme':
        specifier: ^3.23.0
        version: 3.26.9(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/lazada-i18n':
        specifier: ^1.0.11
        version: 1.0.12
      '@alife/workstation-utils':
        specifier: ^1.14.2
        version: 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      '@amap/amap-jsapi-loader':
        specifier: ^1.0.1
        version: 1.0.1
      '@formily/core':
        specifier: ^2.0.8
        version: 2.3.1
      '@formily/next':
        specifier: ^2.0.8
        version: 2.3.1(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(@types/react-dom@16.9.24)(@types/react@16.14.60)(prop-types@15.8.1)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)(typescript@5.4.5)
      '@formily/react':
        specifier: ^2.0.8
        version: 2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)(typescript@5.4.5)
      '@formily/shared':
        specifier: ^2.3.1
        version: 2.3.1
      '@vmojs/decorator':
        specifier: ^1.0.15
        version: 1.0.17
      ahooks:
        specifier: ^3.3.0
        version: 3.7.11(react@16.14.0)
      bizcharts:
        specifier: ^4.1.16
        version: 4.1.23(@babel/core@7.24.5)(react@16.14.0)
      classnames:
        specifier: ^2.3.0
        version: 2.5.1
      dayjs:
        specifier: ^1.11.11
        version: 1.11.11
      lodash:
        specifier: 4.17.21
        version: 4.17.21
      lodash-es:
        specifier: ^4.17.21
        version: 4.17.21
      react-router:
        specifier: ^5.2.0
        version: 5.3.4(react@16.14.0)
      react-router-dom:
        specifier: ^5.2.0
        version: 5.3.4(react@16.14.0)
    devDependencies:
      '@ali/eslint-plugin-comments':
        specifier: ^1.0.0
        version: 1.0.0
      '@alifd/next':
        specifier: ^1.22.16
        version: 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@types/lodash':
        specifier: ^4.14.192
        version: 4.17.1
      '@types/lodash-es':
        specifier: ^4.17.11
        version: 4.17.12
      '@types/react':
        specifier: ^16.8.0
        version: 16.14.60
      '@types/react-dom':
        specifier: ^16.8.0
        version: 16.9.24
      babel-eslint:
        specifier: ^8.1.2
        version: 8.2.6
      eslint:
        specifier: ^5.10.0
        version: 5.16.0
      eslint-config-ali:
        specifier: ^4.0.0
        version: 4.1.1(eslint-plugin-import@2.29.1(eslint@5.16.0))(eslint-plugin-react@7.34.1(eslint@5.16.0))(eslint@5.16.0)
      eslint-plugin-import:
        specifier: ^2.22.1
        version: 2.29.1(eslint@5.16.0)
      eslint-plugin-react:
        specifier: ^7.21.5
        version: 7.34.1(eslint@5.16.0)
      moment:
        specifier: ^2.29.1
        version: 2.30.1
      moment-timezone:
        specifier: ^0.5.33
        version: 0.5.45
      pre-commit:
        specifier: ^1.2.2
        version: 1.2.2
      prettier:
        specifier: ^1.17.0
        version: 1.19.1
      pretty-quick:
        specifier: ^1.11.0
        version: 1.11.1(prettier@1.19.1)
      react:
        specifier: ^16.8.0
        version: 16.14.0
      react-dom:
        specifier: ^16.8.0
        version: 16.14.0(react@16.14.0)
      sass:
        specifier: ^1.32.8
        version: 1.77.1

packages:
  '@ahooksjs/use-request@2.8.15':
    resolution:
      { integrity: sha512-xhVaM4fyIiAMdVFuuU5i3CFUdFa/IblF+fvITVMFaUEO3w/V5tVCAF6WIA3T03n1/RPuzRkA7Ao1PFtSGtGelw== }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0

  '@ahooksjs/use-url-state@2.5.12':
    resolution:
      { integrity: sha512-NrQuEtumTYNag8y/H8eByYU3An1a25EMKdxQnPsC1FVq2gmBny1AyKYFqC82PV3e4efi9F88Od/FgoNjsm1frg== }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0
      react-router: ^5.0.0

  '@ali/eslint-plugin-comments@1.0.0':
    resolution:
      { integrity: sha512-5G9ySqNp9dPNVdcukvCsM4nir6QiHIGDl7pom4EFrGadsNGCcDQamv6A83PQMJA0qjvy4oRe8B1TjflHIOlOwQ== }
    engines: { node: '>=0.10.0' }

  '@ali/filebroker@0.2.6':
    resolution:
      { integrity: sha512-sUfNnpiGUez2RcYlZx5fhPvtUli7ndjzjaAofaTxnh0CFHMVFTcNnyu/BY21d3eTwov5GnrY/39+xWIr2RI4xw== }
    peerDependencies:
      '@ali/trace-sdk': '>=1.3.17'

  '@ali/form-log@0.1.10':
    resolution:
      { integrity: sha512-+0NTX+fncEJTAUiNAGnVpUUC3taf0B7SAyI7ebNPyrCQOpI6YDeOTAeEI2g6Es9p6HKaLc+cC8ABCFie2YU29Q== }
    engines: { install-alinode: '4' }

  '@ali/gcom-lzd-cookie@1.3.0':
    resolution:
      { integrity: sha512-shgUcQtdtceDdwioavZTY5vgjn5i43b1p7PnyG6CKdnxhjLp98dkZXv6gNHRhObu2TvC75STatv2cjyw3x4yoA== }

  '@ali/gcom-lzd-env@1.2.7':
    resolution:
      { integrity: sha512-R18TR1mdDYSN9tbMfOL1Vr/J17jf29OADHW4jtclzGYZXs7QenOr46oRRkxQyylDHWPwKSwaVJ01bl3nNyosUg== }

  '@ali/gcom-lzd-resize@1.3.1':
    resolution:
      { integrity: sha512-5cECv6Pxwo4dEsui4AxqBMJYO2+gp0V2POPIOXkVxdBN/1AO5HARJciLy1KJvBiAozqG0jva+XQo+I2DXNTp9A== }
    engines: { npm: '>=3.0.0' }

  '@ali/gcom-lzd-sites@1.5.0':
    resolution:
      { integrity: sha512-j+Et351o62B8Fu4AMvLaoRi/8J0aT0uIbxQkbCDEU0PJR1jAVIwGxARHWzZElGj/V3cF+EUwhi1EPK8urxNfFA== }

  '@ali/global-locale@1.0.5':
    resolution:
      { integrity: sha512-chTfyfq/B2ayAl2rFHYVrIqzoaKtapuTxADWtUy6lx5LeAbExhCo5DAxj6Sf0JEhn22Z7xcneTRyvZu5owHxTA== }

  '@ali/global-string-format@1.0.7':
    resolution:
      { integrity: sha512-TCF4tBlUGsA9xlngHpnXhGgJGimGcOIa5v8c1BNd8X3viltq5oyQ+VdXh5h7iNAH6AWoYXkoW2FuXJGS0J+GJA== }

  '@ali/i18n-utils@1.1.5':
    resolution:
      { integrity: sha512-jTj9itO6TAVCxhpJ5Sgftu7cW4VtRBqzQ5SEddpX+REp5Pu2yhAGbtgnoO84hCk9z5Gb7VF11ZhftMrpulFdGg== }

  '@ali/locale-common-asc@1.2.1':
    resolution:
      { integrity: sha512-RUf/2vvrB+RZeler9J+MY3iXZsQqyhsu2sFUWGhF7xfYsImiU4X4VRBwbuLKjAtEnTLVo+KObPmsMoZZVds9cA== }

  '@ali/lzd-h5-resize@2.1.30':
    resolution:
      { integrity: sha512-gafiR7IHBl3U0wQ9KldhYlSk3ZvBf/Dh30izFLN6XiGoGM+d4ujuIlQkOwd9uEJQhwNWfHWwjEmfhZ3nx/1CMw== }

  '@ali/merlion-ui-v1@1.48.1':
    resolution:
      { integrity: sha512-Yo9mBoGyNyaRlkvk5p4qlML7tSTLopnr6y0dUMf3+SACQnm2fmCbrheHzCinsYu2PVEJm/0teTh/9Cpat7p51g== }

  '@ali/merlion-ui@1.48.1':
    resolution:
      { integrity: sha512-CuHO/jV1z4/v0+FgJUZtHdJ4Ij/0hFVAhSHnb6sBHc4cV3Qmh26pHJ27W/xkBR01gljkvGsv/vpyCel1GnHYIw== }

  '@ali/retcode-util@1.0.5':
    resolution:
      { integrity: sha512-O05C51BhZlPf91OKMCwa27ZzL42XCxgemAWJNQLrJpcGA4h2sX+DpYXiL5zU/5Ev0dH3AUzYtoNxW3UpQD2ocA== }

  '@ali/trace-core@1.3.25':
    resolution:
      { integrity: sha512-Aj6iYnl2h0iEJvbHC6nmxyEPsKDWkoqVQ3Vz6CL06hZKNK8aeEdESgtYg6k1eBOhM8EBcZ7HSifCsTOrCnUW8g== }

  '@ali/trace-log-map@1.3.25':
    resolution:
      { integrity: sha512-C7P8PhS0bI0cOJLyBAUlUrn9mvL6MDBID7ICZPv7Mn8hBLJ6o2YtwLOFGGS9TI5lf1RzhzDZZaD9YCNd6ehoAA== }

  '@ali/trace-plugin-error@1.3.25':
    resolution:
      { integrity: sha512-ddRVpVK2YVaHywHSOeoXsp6PMrH7iOHHHAjU1t2dddJ9GvJ5ijCOn2S/AajBLVWs/mYDFYW+9cYxDWkxPLDA3A== }

  '@ali/trace-plugin-form@1.3.25':
    resolution:
      { integrity: sha512-HWIgDxrIOmJDo8HBOob/hrml0F+ljHm10QaHTPXujQDPuM/91UAfUh2qlzjatVYfSGS/OA65GAgkqRMvuJMuGA== }

  '@ali/trace-sdk@1.3.25':
    resolution:
      { integrity: sha512-1nkK8RXAu78AfkTlqhzBmn/24hp0O1UyXy/Wl8LhbGFevk7WNDccPyAQzByvK2Mp/hu6d8zXQWb8cAAC3x0rcw== }

  '@ali/trace-util@1.3.25':
    resolution:
      { integrity: sha512-31L0iSjzvGL3fWW89/xqG0n0lG6ihAyQT3BiWCtaGPfWpi8IuazCePwqPIkFwICMbpmuzD1guUaahLPWoZW9pw== }

  '@alifd/babel-runtime-jsx-style-transform@1.0.0':
    resolution:
      { integrity: sha512-+9K1r90w1XxN8b/Z45RxzH3NpwKgyi7BxyMg03vkF6LS4anKAuT3bPtamXBGDU871aGnjNelM8TndLkDQcKupA== }

  '@alifd/field@1.7.0':
    resolution:
      { integrity: sha512-rXqtuJudWaSl+1EOjUqr1OHUh8PWnzvyMmm6sKev9uTbctrt+pHEkRh3fbXebJhtEXgZbqhh/UM2afqy1nIIeA== }

  '@alifd/field@2.0.0':
    resolution:
      { integrity: sha512-RWfUksnp0jP9QJjduZRC3XN63vg0zNS9Q4KDwFscKY3HcYdsR+CWVBPUcvQvbJzVZf5ZVLm46cerInRKdUadsg== }

  '@alifd/meet-react-component-one@1.3.2':
    resolution:
      { integrity: sha512-KxcBKh0q4hOubOL83jflfttPz5ikjL9HW1C5HDBhBHOGR1z9/vyi0feUL7XUrXvKcCcILq8DRXy1qTiJsP0MZw== }
    peerDependencies:
      react: '>=16.13.1'
      react-dom: '>=16.13.1'

  '@alifd/meet-react@2.9.9':
    resolution:
      { integrity: sha512-l8xUMwMpgdMlwsC2+8apAu22MCiCYan5S/36RxT0SfAbHp2O5ggov5gw0uAA108DIsQd7cV/5adW+kWHnN8sOg== }
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@alifd/next@1.26.37':
    resolution:
      { integrity: sha512-+sZgPM7apganmQEUE7fAI9zZr9j0hxGjkWmuzNQIRU+Sb+Jnmmu7eOHAvjkoCWpMCgQHKTnLdiyIbVoSh9I6mQ== }
    peerDependencies:
      '@alifd/meet-react': ^2.0.0
      moment: ^2.22.1
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@alifd/next@1.27.11':
    resolution:
      { integrity: sha512-x/ozbshaG5f0T6kWUS1B2kry7+atiLecSA53rpeV35XNDiCgp9Hvv6FBqMNvtJr37oXzSiq8sLUJTl9q3LsejQ== }
    peerDependencies:
      '@alifd/meet-react': ^2.0.0
      moment: ^2.22.1
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@alifd/overlay@0.3.5':
    resolution:
      { integrity: sha512-kFud++NuXDoEE1iCvjqoomtuq+m5SDDO37WS85mKR1007qOf7XwhVgrwgxZ9yFfxGHVnHg863nIdDvQk+9/Jiw== }

  '@alifd/validate@1.2.3':
    resolution:
      { integrity: sha512-ggSBfpl3H8M2OEM95zC9NQc4cBvne/Eq4mTHZHWtqYI/6Vnz0k1fGx3hnYsdGu3c3hF4l6sUDPulactM6lSXtA== }

  '@alifd/validate@2.0.3':
    resolution:
      { integrity: sha512-xwA5XvpKhHIrYqpZ4gldppQ/bIIqqik1TMhtjLidi+LeIdRlxRc1NVC3FW9iF/6nPfiK3PhNpTYOjavQT0nKPQ== }

  '@alife/asc-afeedback@0.0.6':
    resolution:
      { integrity: sha512-zqcPTbIAeia66RcG9YdkzZ1Tx5KI625WPnv8aU7QAUJv6VZGMKlF5NZgFE55pHgyy4INg/bE18qBBmxmaCkP1A== }

  '@alife/asc-campaign-components@1.0.6':
    resolution:
      { integrity: sha512-IuXX2dYwbIeeWWCIkwZ4P6jQLPh9gDDlz1Q8iuMkeVl4XRv1M15M5EsQ7sLW8Onv5Maj8Tw60FvHn7tw8oL1Qg== }

  '@alife/asc-components@1.39.4':
    resolution:
      { integrity: sha512-CMChAb4JHZ8RgbToyfUrLnwUb8ZSWbmBCKVeoKROnZHJD6cy3Sz/K5YEmsJ7PA4FcHKXSYqfuQCe4nK4goPr7A== }
    deprecated: This package has been migrated to **@ali/merlion-ui**.

  '@alife/asc-logistics-components@1.1.14':
    resolution:
      { integrity: sha512-FzjjvNHlILw30WCFjVSPjL6cPBkIils+tfy7vF81CakOUOeeghj4po0KRb/iEvznXcCOBCiAR7nJPc/9muP9fQ== }

  '@alife/cco-general@0.1.7':
    resolution:
      { integrity: sha512-cA3cSYCickxamt4cOwWvgumW2VlpvhHaC278oEH0WvMnu2WiVeFIFUGr73+NTJE55AYzTRPJjqa6TKjK2Pkm9A== }

  '@alife/cndzk-entrance-api@0.2.6':
    resolution:
      { integrity: sha512-S+oDyK+bxUWWfRSukG+rM0LkdlsyxoWLccxWms/rL7bXrhXymTqPJw5rACs2lo+ayODARian0+7kUzPrzdJUwA== }
    peerDependencies:
      moment: ^2.22.1
      react: ^16.x

  '@alife/form-track-plugin@1.1.0':
    resolution:
      { integrity: sha512-jRURIFwqXHgi+y83Q0CgPUAkCUHrz1y71C+zXi6dG6x2Bwx5whur6VVQnrs3237C4wNc4tq84XQznxbuMv6pag== }
    peerDependencies:
      '@alifd/next': ^1.23.22
      '@formily/core': ^2.0.0

  '@alife/intl-request@2.4.0':
    resolution:
      { integrity: sha512-5GQX+cQ3Srhr0hDwC3CqZqbT1t5T2pUeb1B2bT1BmsN6u6Cvt1YwxifAVEuIaxXKUkRGXkA1RNHkT4AI/x+LKw== }

  '@alife/intl-util@1.0.131':
    resolution:
      { integrity: sha512-E/i7F71dK+bCKYCQNqjRvRe1hp9BnNbUySImvWBiQ0W1Gck+UJcE1WDiYHpTuvhYlMDUJNKyHKIxfpiVhYbLQA== }

  '@alife/lago-utils@1.0.2':
    resolution:
      { integrity: sha512-zemlcIw0iEHAdEON/mGqLvKdVwahvBnjZoW+GvV71mHLclprFtsgtgMcW0P59XnN6u2ksA7YsSWByznlspK/cA== }

  '@alife/lazada-asc-theme@3.26.9':
    resolution:
      { integrity: sha512-V6TLxxfW35tPQsRZDXve4ffF+bXbSFpwwlZlQH8+OEDU4HfJHNLel8D71+B85FkfHB5tmCMtFX/ZiIK2gpeySg== }
    peerDependencies:
      '@alifd/next': ~1.26.x
      moment: ^2.22.1
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@alife/lazada-i18n@1.0.12':
    resolution:
      { integrity: sha512-DlXzjMEBoMjxFVIF8XwnxebI20jtjbp6a4jDVE5CYphsAHmSnsZ1h0Haj/k758bfHNJlki00hSIsvVe0BLyizw== }

  '@alife/lazada-i18n@2.0.2':
    resolution:
      { integrity: sha512-eYuko4nooUsPBvR4JOgVyhVVYtfdbbVoFBmYjUwpmBvzkbwrZg5Zq2Eeo8mOpladQSTBUwEoxUw1RQBI/pJGbw== }

  '@alife/mcms_dada_common@0.0.8':
    resolution:
      { integrity: sha512-HoogvcWpK/Udn4pH4iRjHkuCalfgsXSJq+t7CROTwukIBPMoEeVvJyjEvjxMMFGptMYr2jvosBjv0PqtlEnECg== }

  '@alife/theme-27318@0.3.2':
    resolution:
      { integrity: sha512-ehVM0nnNUJFTCGCbG2uM32u6/52ny0+qyN/mUUelipxEjSk2pEUvx1kE525F54nsy2TLapIc24D8VEfOuaVFvQ== }
    peerDependencies:
      '@alifd/next': ~1.26.x
      moment: ^2.22.1
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@alife/workstation-upload@2.1.0':
    resolution:
      { integrity: sha512-rocQ6cg218u3JyKemJGy3e1puFCreh8KL4Zm90K50jHZj3aifXOFXlv7Re9M48Bm9xV3WEDvXYQ1vY3xtnceFg== }

  '@alife/workstation-utils@1.14.2':
    resolution:
      { integrity: sha512-pfNP180L5jBo9m4zL/NKSvqyJxYYJEXAzg0FGuxN3Vp5QD0ViaY1FSgm0kcJI6OCj2OM7k7YXza9XPmq7BWH/A== }

  '@amap/amap-jsapi-loader@1.0.1':
    resolution:
      { integrity: sha512-nPyLKt7Ow/ThHLkSvn2etQlUzqxmTVgK7bIgwdBRTg2HK5668oN7xVxkaiRe3YZEzGzfV2XgH5Jmu2T73ljejw== }

  '@ampproject/remapping@2.3.0':
    resolution:
      { integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw== }
    engines: { node: '>=6.0.0' }

  '@antv/adjust@0.2.5':
    resolution:
      { integrity: sha512-MfWZOkD9CqXRES6MBGRNe27Q577a72EIwyMnE29wIlPliFvJfWwsrONddpGU7lilMpVKecS3WAzOoip3RfPTRQ== }

  '@antv/attr@0.3.5':
    resolution:
      { integrity: sha512-wuj2gUo6C8Q2ASSMrVBuTcb5LcV+Tc0Egiy6bC42D0vxcQ+ta13CLxgMmHz8mjD0FxTPJDXSciyszRSC5TdLsg== }

  '@antv/color-util@2.0.6':
    resolution:
      { integrity: sha512-KnPEaAH+XNJMjax9U35W67nzPI+QQ2x27pYlzmSIWrbj4/k8PGrARXfzDTjwoozHJY8qG62Z+Ww6Alhu2FctXQ== }

  '@antv/component@0.8.35':
    resolution:
      { integrity: sha512-VnRa5X77nBPI952o2xePEEMSNZ6g2mcUDrQY8mVL2kino/8TFhqDq5fTRmDXZyWyIYd4ulJTz5zgeSwAnX/INQ== }

  '@antv/coord@0.3.1':
    resolution:
      { integrity: sha512-rFE94C8Xzbx4xmZnHh2AnlB3Qm1n5x0VT3OROy257IH6Rm4cuzv1+tZaUBATviwZd99S+rOY9telw/+6C9GbRw== }

  '@antv/dom-util@2.0.4':
    resolution:
      { integrity: sha512-2shXUl504fKwt82T3GkuT4Uoc6p9qjCKnJ8gXGLSW4T1W37dqf9AV28aCfoVPHp2BUXpSsB+PAJX2rG/jLHsLQ== }

  '@antv/event-emitter@0.1.3':
    resolution:
      { integrity: sha512-4ddpsiHN9Pd4UIlWuKVK1C4IiZIdbwQvy9i7DUSI3xNJ89FPUFt8lxDYj8GzzfdllV0NkJTRxnG+FvLk0llidg== }

  '@antv/g-base@0.5.16':
    resolution:
      { integrity: sha512-jP06wggTubDPHXoKwFg3/f1lyxBX9ywwN3E/HG74Nd7DXqOXQis8tsIWW+O6dS/h9vyuXLd1/wDWkMMm3ZzXdg== }

  '@antv/g-canvas@0.5.17':
    resolution:
      { integrity: sha512-sXYJMWTOlb/Ycb6sTKu00LcJqInXJY4t99+kSM40u2OfqrXYmaXDjHR7D2V0roMkbK/QWiWS9UnEidCR1VtMOA== }

  '@antv/g-math@0.1.9':
    resolution:
      { integrity: sha512-KHMSfPfZ5XHM1PZnG42Q2gxXfOitYveNTA7L61lR6mhZ8Y/aExsYmHqaKBsSarU0z+6WLrl9C07PQJZaw0uljQ== }

  '@antv/g-svg@0.5.7':
    resolution:
      { integrity: sha512-jUbWoPgr4YNsOat2Y/rGAouNQYGpw4R0cvlN0YafwOyacFFYy2zC8RslNd6KkPhhR3XHNSqJOuCYZj/YmLUwYw== }

  '@antv/g2@4.1.32':
    resolution:
      { integrity: sha512-vJC0LgFyCjN3RdPA6JOi59qC8O4Z70TqFh/th+kzdWlt9KXDJc3MBBYcJI97m1IlrT9XqTGKqkZyGduZw4HCoA== }

  '@antv/g2plot@2.3.39':
    resolution:
      { integrity: sha512-B6/b+MiUOuO3vlvY19Qt0v+3B7ds72pxESI714hzuH2niXQ35AW8GaQ7+1U6Y7Kk7btoaB2AaCyWcvZuloWoPw== }

  '@antv/matrix-util@3.0.4':
    resolution:
      { integrity: sha512-BAPyu6dUliHcQ7fm9hZSGKqkwcjEDVLVAstlHULLvcMZvANHeLXgHEgV7JqcAV/GIhIz8aZChIlzM1ZboiXpYQ== }

  '@antv/matrix-util@3.1.0-beta.3':
    resolution:
      { integrity: sha512-W2R6Za3A6CmG51Y/4jZUM/tFgYSq7vTqJL1VD9dKrvwxS4sE0ZcXINtkp55CdyBwJ6Cwm8pfoRpnD4FnHahN0A== }

  '@antv/path-util@2.0.15':
    resolution:
      { integrity: sha512-R2VLZ5C8PLPtr3VciNyxtjKqJ0XlANzpFb5sE9GE61UQqSRuSVSzIakMxjEPrpqbgc+s+y8i+fmc89Snu7qbNw== }

  '@antv/scale@0.3.18':
    resolution:
      { integrity: sha512-GHwE6Lo7S/Q5fgaLPaCsW+CH+3zl4aXpnN1skOiEY0Ue9/u+s2EySv6aDXYkAqs//i0uilMDD/0/4n8caX9U9w== }

  '@antv/util@2.0.17':
    resolution:
      { integrity: sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q== }

  '@babel/code-frame@7.0.0-beta.44':
    resolution:
      { integrity: sha512-cuAuTTIQ9RqcFRJ/Y8PvTh+paepNcaGxwQwjIDRWPXmzzyAeCO4KqS9ikMvq0MCbRk6GlYKwfzStrcP3/jSL8g== }

  '@babel/code-frame@7.24.2':
    resolution:
      { integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ== }
    engines: { node: '>=6.9.0' }

  '@babel/compat-data@7.24.4':
    resolution:
      { integrity: sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ== }
    engines: { node: '>=6.9.0' }

  '@babel/core@7.24.5':
    resolution:
      { integrity: sha512-tVQRucExLQ02Boi4vdPp49svNGcfL2GhdTCT9aldhXgCJVAI21EtRfBettiuLUwce/7r6bFdgs6JFkcdTiFttA== }
    engines: { node: '>=6.9.0' }

  '@babel/generator@7.0.0-beta.44':
    resolution:
      { integrity: sha512-5xVb7hlhjGcdkKpMXgicAVgx8syK5VJz193k0i/0sLP6DzE6lRrU1K3B/rFefgdo9LPGMAOOOAWW4jycj07ShQ== }

  '@babel/generator@7.24.5':
    resolution:
      { integrity: sha512-x32i4hEXvr+iI0NEoEfDKzlemF8AmtOP8CcrRaEcpzysWuoEb1KknpcvMsHKPONoKZiDuItklgWhB18xEhr9PA== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-annotate-as-pure@7.22.5':
    resolution:
      { integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-compilation-targets@7.23.6':
    resolution:
      { integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-define-polyfill-provider@0.6.2':
    resolution:
      { integrity: sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ== }
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.22.20':
    resolution:
      { integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-function-name@7.0.0-beta.44':
    resolution:
      { integrity: sha512-MHRG2qZMKMFaBavX0LWpfZ2e+hLloT++N7rfM3DYOMUOGCD8cVjqZpwiL8a0bOX3IYcQev1ruciT0gdFFRTxzg== }

  '@babel/helper-function-name@7.23.0':
    resolution:
      { integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-get-function-arity@7.0.0-beta.44':
    resolution:
      { integrity: sha512-w0YjWVwrM2HwP6/H3sEgrSQdkCaxppqFeJtAnB23pRiJB5E/O9Yp7JAAeWBl+gGEgmBFinnTyOv2RN7rcSmMiw== }

  '@babel/helper-hoist-variables@7.22.5':
    resolution:
      { integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-module-imports@7.24.3':
    resolution:
      { integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-module-transforms@7.24.5':
    resolution:
      { integrity: sha512-9GxeY8c2d2mdQUP1Dye0ks3VDyIMS98kt/llQ2nUId8IsWqTF0l1LkSX0/uP7l7MCDrzXS009Hyhe2gzTiGW8A== }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.24.5':
    resolution:
      { integrity: sha512-xjNLDopRzW2o6ba0gKbkZq5YWEBaK3PCyTOY1K2P/O07LGMhMqlMXPxwN4S5/RhWuCobT8z0jrlKGlYmeR1OhQ== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-simple-access@7.24.5':
    resolution:
      { integrity: sha512-uH3Hmf5q5n7n8mz7arjUlDOCbttY/DW4DYhE6FUsjKJ/oYC1kQQUvwEQWxRwUpX9qQKRXeqLwWxrqilMrf32sQ== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-split-export-declaration@7.0.0-beta.44':
    resolution:
      { integrity: sha512-aQ7QowtkgKKzPGf0j6u77kBMdUFVBKNHw2p/3HX/POt5/oz8ec5cs0GwlgM8Hz7ui5EwJnzyfRmkNF1Nx1N7aA== }

  '@babel/helper-split-export-declaration@7.24.5':
    resolution:
      { integrity: sha512-5CHncttXohrHk8GWOFCcCl4oRD9fKosWlIRgWm4ql9VYioKm52Mk2xsmoohvm7f3JoiLSM5ZgJuRaf5QZZYd3Q== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-string-parser@7.24.1':
    resolution:
      { integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-validator-identifier@7.24.5':
    resolution:
      { integrity: sha512-3q93SSKX2TWCG30M2G2kwaKeTYgEUp5Snjuj8qm729SObL6nbtUldAi37qbxkD5gg3xnBio+f9nqpSepGZMvxA== }
    engines: { node: '>=6.9.0' }

  '@babel/helper-validator-option@7.23.5':
    resolution:
      { integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw== }
    engines: { node: '>=6.9.0' }

  '@babel/helpers@7.24.5':
    resolution:
      { integrity: sha512-CiQmBMMpMQHwM5m01YnrM6imUG1ebgYJ+fAIW4FZe6m4qHTPaRHti+R8cggAwkdz4oXhtO4/K9JWlh+8hIfR2Q== }
    engines: { node: '>=6.9.0' }

  '@babel/highlight@7.0.0-beta.44':
    resolution:
      { integrity: sha512-Il19yJvy7vMFm8AVAh6OZzaFoAd0hbkeMZiX3P5HGD+z7dyI7RzndHB0dg6Urh/VAFfHtpOIzDUSxmY6coyZWQ== }

  '@babel/highlight@7.24.5':
    resolution:
      { integrity: sha512-8lLmua6AVh/8SLJRRVD6V8p73Hir9w5mJrhE+IPpILG31KKlI9iz5zmBYKcWPS59qSfgP9RaSBQSHHE81WKuEw== }
    engines: { node: '>=6.9.0' }

  '@babel/parser@7.24.5':
    resolution:
      { integrity: sha512-EOv5IK8arwh3LI47dz1b0tKUb/1uhHAnHJOrjgtQMIpu1uXd9mlFrJg9IUgGUgZ41Ch0K8REPTYpO7B76b4vJg== }
    engines: { node: '>=6.0.0' }
    hasBin: true

  '@babel/plugin-syntax-jsx@7.24.1':
    resolution:
      { integrity: sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA== }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.24.1':
    resolution:
      { integrity: sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw== }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.24.3':
    resolution:
      { integrity: sha512-J0BuRPNlNqlMTRJ72eVptpt9VcInbxO6iP3jaxr+1NPhC0UkKL+6oeX6VXMEYdADnuqmMmsBspt4d5w8Y/TCbQ== }
    engines: { node: '>=6.9.0' }
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.24.5':
    resolution:
      { integrity: sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g== }
    engines: { node: '>=6.9.0' }

  '@babel/template@7.0.0-beta.44':
    resolution:
      { integrity: sha512-w750Sloq0UNifLx1rUqwfbnC6uSUk0mfwwgGRfdLiaUzfAOiH0tHJE6ILQIUi3KYkjiCDTskoIsnfqZvWLBDng== }

  '@babel/template@7.24.0':
    resolution:
      { integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA== }
    engines: { node: '>=6.9.0' }

  '@babel/traverse@7.0.0-beta.44':
    resolution:
      { integrity: sha512-UHuDz8ukQkJCDASKHf+oDt3FVUzFd+QYfuBIsiNu/4+/ix6pP/C+uQZJ6K1oEfbCMv/IKWbgDEh7fcsnIE5AtA== }

  '@babel/traverse@7.24.5':
    resolution:
      { integrity: sha512-7aaBLeDQ4zYcUFDUD41lJc1fG8+5IU9DaNSJAgal866FGvmD5EbWQgnEC6kO1gGLsX0esNkfnJSndbTXA3r7UA== }
    engines: { node: '>=6.9.0' }

  '@babel/types@7.0.0-beta.44':
    resolution:
      { integrity: sha512-5eTV4WRmqbaFM3v9gHAIljEQJU4Ssc6fxL61JN+Oe2ga/BwyjzjamwkCVVAQjHGuAX8i0BWo42dshL8eO5KfLQ== }

  '@babel/types@7.24.5':
    resolution:
      { integrity: sha512-6mQNsaLeXTw0nxYUYu+NSa4Hx4BlF1x1x8/PMFbiR+GBSr+2DkECc69b8hgy2frEodNcvPffeH8YfWd3LI6jhQ== }
    engines: { node: '>=6.9.0' }

  '@emotion/is-prop-valid@1.2.2':
    resolution:
      { integrity: sha512-uNsoYd37AFmaCdXlg6EYD1KaPOaRWRByMCYzbKUX4+hhMfrxdVSelShywL4JVaAeM/eHUOSprYBQls+/neX3pw== }

  '@emotion/memoize@0.8.1':
    resolution:
      { integrity: sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA== }

  '@emotion/stylis@0.8.5':
    resolution:
      { integrity: sha512-h6KtPihKFn3T9fuIrwvXXUOwlx3rfUvfZIcP5a6rh8Y7zjE3O06hT5Ss4S/YI1AYhuZ1kjaE/5EaOOI2NqSylQ== }

  '@emotion/unitless@0.7.5':
    resolution:
      { integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg== }

  '@formatjs/ecma402-abstract@1.11.4':
    resolution:
      { integrity: sha512-EBikYFp2JCdIfGEb5G9dyCkTGDmC57KSHhRQOC3aYxoPWVZvfWCDjZwkGYHN7Lis/fmuWl906bnNTJifDQ3sXw== }

  '@formatjs/fast-memoize@1.2.1':
    resolution:
      { integrity: sha512-Rg0e76nomkz3vF9IPlKeV+Qynok0r7YZjL6syLz4/urSg0IbjPZCB/iYUMNsYA643gh4mgrX3T7KEIFIxJBQeg== }

  '@formatjs/icu-messageformat-parser@2.1.0':
    resolution:
      { integrity: sha512-Qxv/lmCN6hKpBSss2uQ8IROVnta2r9jd3ymUEIjm2UyIkUCHVcbUVRGL/KS/wv7876edvsPe+hjHVJ4z8YuVaw== }

  '@formatjs/icu-skeleton-parser@1.3.6':
    resolution:
      { integrity: sha512-I96mOxvml/YLrwU2Txnd4klA7V8fRhb6JG/4hm3VMNmeJo1F03IpV2L3wWt7EweqNLES59SZ4d6hVOPCSf80Bg== }

  '@formatjs/intl-displaynames@5.4.3':
    resolution:
      { integrity: sha512-4r12A3mS5dp5hnSaQCWBuBNfi9Amgx2dzhU4lTFfhSxgb5DOAiAbMpg6+7gpWZgl4ahsj3l2r/iHIjdmdXOE2Q== }

  '@formatjs/intl-listformat@6.5.3':
    resolution:
      { integrity: sha512-ozpz515F/+3CU+HnLi5DYPsLa6JoCfBggBSSg/8nOB5LYSFW9+ZgNQJxJ8tdhKYeODT+4qVHX27EeJLoxLGLNg== }

  '@formatjs/intl-localematcher@0.2.25':
    resolution:
      { integrity: sha512-YmLcX70BxoSopLFdLr1Ds99NdlTI2oWoLbaUW2M406lxOIPzE1KQhRz2fPUkq34xVZQaihCoU29h0KK7An3bhA== }

  '@formatjs/intl@2.2.1':
    resolution:
      { integrity: sha512-vgvyUOOrzqVaOFYzTf2d3+ToSkH2JpR7x/4U1RyoHQLmvEaTQvXJ7A2qm1Iy3brGNXC/+/7bUlc3lpH+h/LOJA== }
    peerDependencies:
      typescript: ^4.5
    peerDependenciesMeta:
      typescript:
        optional: true

  '@formily/core@2.3.1':
    resolution:
      { integrity: sha512-dCBPnmzDpQKdN4ddxi98VbdurpbH6SHb12S9y9SnzW/QTslZnkvrvBhTlucWB5XqXJfwcyCzpZIn/GdMAbPpZg== }
    engines: { npm: '>=3.0.0' }

  '@formily/grid@2.3.1':
    resolution:
      { integrity: sha512-Je+sWa7b71qZgysbVQXiv+3bOqXnKZ3vU4RIhMXrTL4ac46+9dNSXGSVrC85C98lq9EJp/uUJhtLdUb6P3WStg== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      typescript: 4.x || 5.x

  '@formily/json-schema@2.3.1':
    resolution:
      { integrity: sha512-LcJmU1BOYGdoip+Q9YAxHdrpjdl781WFSg0fDTD9/0A7c3xUXWwxdCYZIN0cvoXi2qq8Khyzba0pizl3YSkf0A== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      typescript: '>4.1.5'

  '@formily/next@2.3.1':
    resolution:
      { integrity: sha512-BRw/A1TaWvruk23EQHr3zkx3kaBq+pz2Rui+bUtSl11go73U9195y9UgyGMJaHqOAQXtPsJyVr7bUhQO90jtzA== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      '@alifd/next': ^1.19.0
      '@types/react': '>=16.8.0'
      '@types/react-dom': '>=16.8.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      react-is: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@formily/path@2.3.1':
    resolution:
      { integrity: sha512-BVo89K5nAFntx02+EV696If1b1bVIm5I1tRPtVyCVIjBIfAgga5hK4k80GZ01Dlk3tpReHpiIbZVg2DNVfw7jA== }
    engines: { npm: '>=3.0.0' }

  '@formily/react@2.3.1':
    resolution:
      { integrity: sha512-8KkFJe2OnbsgiXwY/txUcUaCQIfIkfowWQinOSni02U2ssgs2bpb1ifFHlZrFGfrrW/xhce4ANUHRgoVYt7W4Q== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      '@types/react': '>=16.8.0'
      '@types/react-dom': '>=16.8.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      react-is: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@formily/reactive-react@2.3.1':
    resolution:
      { integrity: sha512-r6d94JBWhwXGpsffNLnRzgksQJtmqudD1cq7ky2Oljtxg5Ynl8H0Ppcnsv0bd9NbNSAeDeFs2uwWz56exgDzjA== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      '@types/react': '>=16.8.0'
      '@types/react-dom': '>=16.8.0'
      react: '>=16.8.0'
      react-dom: '>=16.8.0'
      react-is: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@formily/reactive@2.3.1':
    resolution:
      { integrity: sha512-IVHOZW7VBc+Gq9eB/gPldi7pEC3wDonDb99KvHlS8SmzsY6+a/iAdrw2mDagXXUficsC2gT4y4EcJ2f1ALMKtQ== }
    engines: { npm: '>=3.0.0' }

  '@formily/shared@2.3.1':
    resolution:
      { integrity: sha512-qnlh6jnnIbUjcK0rWF9bm6AxgyxuBgURrzU5vMSxTNAN86P7K9+mSc/28qPsdNP9flEA2/clSexP5WEJAGYVgw== }
    engines: { npm: '>=3.0.0' }

  '@formily/validator@2.3.1':
    resolution:
      { integrity: sha512-hM/IDvU/bachpS3fOUe02C9f5EytlNu6OJzDy+AyhWBmZYIVd6QVvPtjV8nyLOXIBJM9N4sxnGSliYQNAPjR1w== }
    engines: { npm: '>=3.0.0' }

  '@gcanvas/core@1.0.0':
    resolution:
      { integrity: sha512-v+moRYrngBYtaFTABYjzeve9H+EAvh1zJd7RCzELQM/vLQCqjcpjh3R+R80W4i4y6dos1yQhMB2SVH8tfx0iEg== }

  '@jridgewell/gen-mapping@0.3.5':
    resolution:
      { integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg== }
    engines: { node: '>=6.0.0' }

  '@jridgewell/resolve-uri@3.1.2':
    resolution:
      { integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw== }
    engines: { node: '>=6.0.0' }

  '@jridgewell/set-array@1.2.1':
    resolution:
      { integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A== }
    engines: { node: '>=6.0.0' }

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution:
      { integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg== }

  '@jridgewell/trace-mapping@0.3.25':
    resolution:
      { integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ== }

  '@juggle/resize-observer@3.4.0':
    resolution:
      { integrity: sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA== }

  '@ljharb/resumer@0.0.1':
    resolution:
      { integrity: sha512-skQiAOrCfO7vRTq53cxznMpks7wS1va95UCidALlOVWqvBAzwPVErwizDwoMqNVMEn1mDq0utxZd02eIrvF1lw== }
    engines: { node: '>= 0.4' }

  '@ljharb/through@2.3.13':
    resolution:
      { integrity: sha512-/gKJun8NNiWGZJkGzI/Ragc53cOdcLNdzjLaIa+GEjguQs0ulsurx8WN0jijdK9yPqDvziX995sMRLyLt1uZMQ== }
    engines: { node: '>= 0.4' }

  '@swc/helpers@0.5.11':
    resolution:
      { integrity: sha512-YNlnKRWF2sVojTpIyzwou9XoTNbzbzONwRhOoniEioF1AtaitTvVZblaQRrAzChWQ1bLYyYSWzM18y4WwgzJ+A== }

  '@types/d3-timer@2.0.3':
    resolution:
      { integrity: sha512-jhAJzaanK5LqyLQ50jJNIrB8fjL9gwWZTgYjevPvkDLMU+kTAZkYsobI59nYoeSrH1PucuyJEi247Pb90t6XUg== }

  '@types/hoist-non-react-statics@3.3.5':
    resolution:
      { integrity: sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg== }

  '@types/js-cookie@2.2.7':
    resolution:
      { integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA== }

  '@types/json5@0.0.29':
    resolution:
      { integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ== }

  '@types/lodash-es@4.17.12':
    resolution:
      { integrity: sha512-0NgftHUcV4v34VhXm8QBSftKVXtbkBG3ViCjs6+eJ5a6y6Mi/jiFGPc1sC7QK+9BFhWrURE3EOggmWaSxL9OzQ== }

  '@types/lodash@4.17.1':
    resolution:
      { integrity: sha512-X+2qazGS3jxLAIz5JDXDzglAF3KpijdhFxlf/V1+hEsOUc+HnWi81L/uv/EvGuV90WY+7mPGFCUDGfQC3Gj95Q== }

  '@types/prop-types@15.7.12':
    resolution:
      { integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q== }

  '@types/query-string@6.3.0':
    resolution:
      { integrity: sha512-yuIv/WRffRzL7cBW+sla4HwBZrEXRNf1MKQ5SklPEadth+BKbDxiVG8A3iISN5B3yC4EeSCzMZP8llHTcUhOzQ== }
    deprecated: This is a stub types definition. query-string provides its own type definitions, so you do not need this installed.

  '@types/react-dom@16.9.24':
    resolution:
      { integrity: sha512-Gcmq2JTDheyWn/1eteqyzzWKSqDjYU6KYsIvH7thb7CR5OYInAWOX+7WnKf6PaU/cbdOc4szJItcDEJO7UGmfA== }

  '@types/react-transition-group@4.4.10':
    resolution:
      { integrity: sha512-hT/+s0VQs2ojCX823m60m5f0sL5idt9SO6Tj6Dg+rdphGPIeJbJ6CxvBYkgkGKrYeDjvIpKTR38UzmtHJOGW3Q== }

  '@types/react@16.14.60':
    resolution:
      { integrity: sha512-wIFmnczGsTcgwCBeIYOuy2mdXEiKZ5znU/jNOnMZPQyCcIxauMGWlX0TNG4lZ7NxRKj7YUIZRneJQSSdB2jKgg== }

  '@types/scheduler@0.16.8':
    resolution:
      { integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A== }

  '@uni/action-sheet@1.0.8':
    resolution:
      { integrity: sha512-3L+ZHK6qYv/3w/ODGZugfbTYc8vT+lkxt/XAl5WRpiFCukjwP5yxRp+feE1SEHdprJXUlHBYTT/Tp0JfRIELJQ== }

  '@uni/clipboard@1.0.9':
    resolution:
      { integrity: sha512-NoqYayQCHB0KIFc2r8akf1S3UtSnBhk+Nc3fX+wFnpRx6qmHHzZSeBk+mTqKVOTfeE3OdcubQAAt/sWfWS/4mw== }

  '@uni/env@1.1.1':
    resolution:
      { integrity: sha512-oQGRQg3cFVb6ByppV0WVue/BE98cw0xvAniX9L0wQtzU94RvZg9/GpkFIDwrlgcvzXlTgUPTTMG9B/riiiFQyQ== }

  '@uni/file@1.1.1':
    resolution:
      { integrity: sha512-gbymGoyD02cWHGVGapxp0zl3VAEU/u4vpDSyfS1tSnIGFjwIbCGq+W+uTAnJYduDbdy4Xiuwzbf0b/4slY9bmQ== }

  '@uni/image@1.1.3':
    resolution:
      { integrity: sha512-68RGzXMYAp8EUZ5jogdQd+KVqyVlKZwuncWmZ96aDqwHhd/J1MnAJuAOAEBL3jCNqXNsvXHLBr5yleg2gdf1yQ== }

  '@uni/navigate@1.0.11':
    resolution:
      { integrity: sha512-7xUVksKKcIMqsxpPBgYRRrkOIVy9bmWmgbinISnZaVobmqSr0oFWN9pHgeCOuvxN66jlVqPIEKHcWyD8IV1oEg== }

  '@uni/page-scroll-to@1.0.0':
    resolution:
      { integrity: sha512-fQTndD14OTezRzXAtsuhdrruO0lz0+lTXa/eSeekVqEkDq9L/OK+T9B6IJS3Ui4Xc1aEkWGSyGe0TaTOfKE9tQ== }

  '@uni/vibrate@1.0.1':
    resolution:
      { integrity: sha512-IocrIbBaZYjBHzvRIGSyN3K2He9Y7BS/VMEri2On9QITU3U2kampDiGGPyA/lQxVSZNemyK6/xtxWoxTjNh91w== }

  '@uni/video@1.0.8':
    resolution:
      { integrity: sha512-Gzo+7Qsq5jmBxcW/B9EF0l77nneBeuHuCBUIVSetSTCQtjdxoF9LF/K3UZE2Z20Raq7/MLrtaDa2KqeLmwWu8A== }

  '@vmojs/decorator@1.0.17':
    resolution:
      { integrity: sha512-Oi9OUgtyBlGmCHMxWMgw32CS5Fu/P1Pox5OMoM2IMvY4mYlCvoJT8r4BdfidzJrVotijsNLwMexRsMbOxGW5Hw== }

  acorn-jsx@5.3.2:
    resolution:
      { integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ== }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@6.4.2:
    resolution:
      { integrity: sha512-XtGIhXwF8YM8bJhGxG5kXgjkEuNGLTkoYqVE+KMR+aspr4KGYmKYg7yUe3KghyQ9yheNwLnjmzh/7+gfDBmHCQ== }
    engines: { node: '>=0.4.0' }
    hasBin: true

  address@1.2.2:
    resolution:
      { integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA== }
    engines: { node: '>= 10.0.0' }

  agentkeepalive@3.5.3:
    resolution:
      { integrity: sha512-yqXL+k5rr8+ZRpOAntkaaRgWgE5o8ESAj5DyRmVTCSoZxXmqemb9Dd7T4i5UzwuERdLAJUy6XzR9zFVuf0kzkw== }
    engines: { node: '>= 4.0.0' }

  ahooks@2.10.14:
    resolution:
      { integrity: sha512-axWa7VoAgu7bxA56dDl0CXW4rvaQmDBiov/d3tAy0x1YNYywYMKokL8TdLgJ5zO/oXGiWmG7BxlGOQGkqE/zkQ== }
    engines: { node: '>=8.0.0' }
    peerDependencies:
      react: ^16.8.6 || ^17.0

  ahooks@3.7.11:
    resolution:
      { integrity: sha512-BfSq7HJ9wk/7a2vX7WbLdwzHyQHmbNe21ipX1PfIzssXIzQfAl79WVJ9GjZaqNl4PFPsJusj/Xjg2OF+gIgGaQ== }
    engines: { node: '>=8.0.0' }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  ajv@6.12.6:
    resolution:
      { integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g== }

  ali-oss@6.20.0:
    resolution:
      { integrity: sha512-TzFXgGlw81sy2JvcCveSYsa2b2+6kv+HA6WTc+cXg6bu8nUAmVPfncRGbn3x2getSOniOFA+TyGy3V4l3Fks+Q== }
    engines: { node: '>=8' }

  align-text@0.1.4:
    resolution:
      { integrity: sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg== }
    engines: { node: '>=0.10.0' }

  amdefine@1.0.1:
    resolution:
      { integrity: sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg== }
    engines: { node: '>=0.4.2' }

  ansi-escapes@3.2.0:
    resolution:
      { integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ== }
    engines: { node: '>=4' }

  ansi-regex@2.1.1:
    resolution:
      { integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA== }
    engines: { node: '>=0.10.0' }

  ansi-regex@3.0.1:
    resolution:
      { integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw== }
    engines: { node: '>=4' }

  ansi-regex@4.1.1:
    resolution:
      { integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g== }
    engines: { node: '>=6' }

  ansi-styles@2.2.1:
    resolution:
      { integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA== }
    engines: { node: '>=0.10.0' }

  ansi-styles@3.2.1:
    resolution:
      { integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA== }
    engines: { node: '>=4' }

  any-promise@1.3.0:
    resolution:
      { integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A== }

  anymatch@3.1.3:
    resolution:
      { integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw== }
    engines: { node: '>= 8' }

  argparse@1.0.10:
    resolution:
      { integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg== }

  array-buffer-byte-length@1.0.1:
    resolution:
      { integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg== }
    engines: { node: '>= 0.4' }

  array-differ@2.1.0:
    resolution:
      { integrity: sha512-KbUpJgx909ZscOc/7CLATBFam7P1Z1QRQInvgT0UztM9Q72aGKCunKASAl7WNW0tnPmPyEMeMhdsfWhfmW037w== }
    engines: { node: '>=6' }

  array-includes@3.1.8:
    resolution:
      { integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ== }
    engines: { node: '>= 0.4' }

  array-union@1.0.2:
    resolution:
      { integrity: sha512-Dxr6QJj/RdU/hCaBjOfxW+q6lyuVE6JFWIrAUpuOOhoJJoQ99cUn3igRaHVB5P9WrgFVN0FfArM3x0cueOU8ng== }
    engines: { node: '>=0.10.0' }

  array-uniq@1.0.3:
    resolution:
      { integrity: sha512-MNha4BWQ6JbwhFhj03YK552f7cb3AzoE8SzeljgChvL1dl3IcvggXVz1DilzySZkCja+CXuZbdW7yATchWn8/Q== }
    engines: { node: '>=0.10.0' }

  array.prototype.findlast@1.2.5:
    resolution:
      { integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ== }
    engines: { node: '>= 0.4' }

  array.prototype.findlastindex@1.2.5:
    resolution:
      { integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ== }
    engines: { node: '>= 0.4' }

  array.prototype.flat@1.3.2:
    resolution:
      { integrity: sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA== }
    engines: { node: '>= 0.4' }

  array.prototype.flatmap@1.3.2:
    resolution:
      { integrity: sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ== }
    engines: { node: '>= 0.4' }

  array.prototype.toreversed@1.1.2:
    resolution:
      { integrity: sha512-wwDCoT4Ck4Cz7sLtgUmzR5UV3YF5mFHUlbChCzZBQZ+0m2cl/DH3tKgvphv1nKgFsJ48oCSg6p91q2Vm0I/ZMA== }

  array.prototype.tosorted@1.1.3:
    resolution:
      { integrity: sha512-/DdH4TiTmOKzyQbp/eadcCVexiCb36xJg7HshYOYJnNZFDj33GEv0P7GxsynpShhq4OLYJzbGcBDkLsDt7MnNg== }

  arraybuffer.prototype.slice@1.0.3:
    resolution:
      { integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A== }
    engines: { node: '>= 0.4' }

  arrify@1.0.1:
    resolution:
      { integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA== }
    engines: { node: '>=0.10.0' }

  astral-regex@1.0.0:
    resolution:
      { integrity: sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg== }
    engines: { node: '>=4' }

  asynckit@0.4.0:
    resolution:
      { integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q== }

  available-typed-arrays@1.0.7:
    resolution:
      { integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ== }
    engines: { node: '>= 0.4' }

  axios@0.19.2:
    resolution:
      { integrity: sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA== }
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410

  axios@1.6.8:
    resolution:
      { integrity: sha512-v/ZHtJDU39mDpyBoFVkETcd/uNdxrWRrg3bKpOKzXFA6Bvqopts6ALSMU3y6ijYxbw2B+wPrIv46egTzJXCLGQ== }

  babel-eslint@8.2.6:
    resolution:
      { integrity: sha512-aCdHjhzcILdP8c9lej7hvXKvQieyRt20SF102SIGyY4cUIiw6UaAtK4j2o3dXX74jEmy0TJ0CEhv4fTIM3SzcA== }
    engines: { node: '>=4' }
    deprecated: babel-eslint is now @babel/eslint-parser. This package will no longer receive updates.

  babel-plugin-polyfill-corejs2@0.4.11:
    resolution:
      { integrity: sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q== }
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.4:
    resolution:
      { integrity: sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg== }
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.2:
    resolution:
      { integrity: sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg== }
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-styled-components@2.1.4:
    resolution:
      { integrity: sha512-Xgp9g+A/cG47sUyRwwYxGM4bR/jDRg5N6it/8+HxCnbT5XNKSKDT9xm4oag/osgqjC2It/vH0yXsomOG6k558g== }
    peerDependencies:
      styled-components: '>= 2'

  babel-plugin-transform-replace-object-assign@2.0.0:
    resolution:
      { integrity: sha512-PMT+dRz6JAHbXIsJB4XjcIstmKK9SFj9MYZGcEWW/1jISiemGz9w6TVLrj4hgpR89X0J9mFuHq61zPvP8lgZZQ== }
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-runtime@6.26.0:
    resolution:
      { integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g== }

  babylon@7.0.0-beta.44:
    resolution:
      { integrity: sha512-5Hlm13BJVAioCHpImtFqNOF2H3ieTOHd0fmFGMxOJ9jgeFqeAwsv3u5P5cR7CSeFrkgHsT19DgFJkHV0/Mcd8g== }
    engines: { node: '>=4.2.0' }
    hasBin: true

  balanced-match@1.0.2:
    resolution:
      { integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw== }

  base64-arraybuffer@1.0.2:
    resolution:
      { integrity: sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ== }
    engines: { node: '>= 0.6.0' }

  big.js@6.2.1:
    resolution:
      { integrity: sha512-bCtHMwL9LeDIozFn+oNhhFoq+yQ3BNdnsLSASUxLciOb1vgvpHsIO1dsENiGMgbb4SkP5TrzWzRiLddn8ahVOQ== }

  binary-extensions@2.3.0:
    resolution:
      { integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw== }
    engines: { node: '>=8' }

  bizcharts@4.1.23:
    resolution:
      { integrity: sha512-chkMApdjmxEukzqO4Knc2M0/abPOqkK/12XQhCzIwjphj+XAzKARVjLPq3JoDvf0jUC4MZfUEb7wbQVqwb8xaA== }

  bowser@1.9.4:
    resolution:
      { integrity: sha512-9IdMmj2KjigRq6oWhmwv1W36pDuA4STQZ8q6YO9um+x07xgYNCD3Oou+WP/3L1HNz7iqythGet3/p4wvc8AAwQ== }

  brace-expansion@1.1.11:
    resolution:
      { integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA== }

  braces@3.0.2:
    resolution:
      { integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A== }
    engines: { node: '>=8' }

  browserslist@4.23.0:
    resolution:
      { integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ== }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  buffer-from@1.1.2:
    resolution:
      { integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ== }

  builtin-status-codes@3.0.0:
    resolution:
      { integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ== }

  call-bind@1.0.7:
    resolution:
      { integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w== }
    engines: { node: '>= 0.4' }

  callsites@3.1.0:
    resolution:
      { integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ== }
    engines: { node: '>=6' }

  camel-case@4.1.2:
    resolution:
      { integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw== }

  camelcase@1.2.1:
    resolution:
      { integrity: sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g== }
    engines: { node: '>=0.10.0' }

  camelize@1.0.1:
    resolution:
      { integrity: sha512-dU+Tx2fsypxTgtLoE36npi3UqcjSSMNYfkqgmoEhtZrraP5VWq0K7FkWVTYa8eMPtnU/G2txVsfdCJTn9uzpuQ== }

  caniuse-lite@1.0.30001618:
    resolution:
      { integrity: sha512-p407+D1tIkDvsEAPS22lJxLQQaG8OTBEqo0KhzfABGk0TU4juBNDSfH0hyAp/HRyx+M8L17z/ltyhxh27FTfQg== }

  center-align@0.1.3:
    resolution:
      { integrity: sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ== }
    engines: { node: '>=0.10.0' }

  chalk@1.1.3:
    resolution:
      { integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A== }
    engines: { node: '>=0.10.0' }

  chalk@2.4.2:
    resolution:
      { integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ== }
    engines: { node: '>=4' }

  chardet@0.7.0:
    resolution:
      { integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA== }

  chokidar@3.6.0:
    resolution:
      { integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw== }
    engines: { node: '>= 8.10.0' }

  classnames@2.2.6:
    resolution:
      { integrity: sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q== }

  classnames@2.5.1:
    resolution:
      { integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow== }

  cli-cursor@2.1.0:
    resolution:
      { integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw== }
    engines: { node: '>=4' }

  cli-width@2.2.1:
    resolution:
      { integrity: sha512-GRMWDxpOB6Dgk2E5Uo+3eEBvtOOlimMmpbFiKuLFnQzYDavtLFY3K5ona41jgN/WdRZtG7utuVSVTL4HbZHGkw== }

  cliui@2.1.0:
    resolution:
      { integrity: sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA== }

  color-convert@1.9.3:
    resolution:
      { integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg== }

  color-name@1.1.3:
    resolution:
      { integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw== }

  combined-stream@1.0.8:
    resolution:
      { integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg== }
    engines: { node: '>= 0.8' }

  concat-map@0.0.1:
    resolution:
      { integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg== }

  concat-stream@1.6.2:
    resolution:
      { integrity: sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw== }
    engines: { '0': node >= 0.8 }

  content-type@1.0.5:
    resolution:
      { integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA== }
    engines: { node: '>= 0.6' }

  contour_plot@0.0.1:
    resolution:
      { integrity: sha512-Nil2HI76Xux6sVGORvhSS8v66m+/h5CwFkBJDO+U5vWaMdNC0yXNCsGDPbzPhvqOEU5koebhdEvD372LI+IyLw== }

  convert-source-map@2.0.0:
    resolution:
      { integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg== }

  copy-to@2.0.1:
    resolution:
      { integrity: sha512-3DdaFaU/Zf1AnpLiFDeNCD4TOWe3Zl2RZaTzUvWiIk5ERzcCodOE20Vqq4fzCbNoHURFHT4/us/Lfq+S2zyY4w== }

  core-js-compat@3.37.1:
    resolution:
      { integrity: sha512-9TNiImhKvQqSUkOvk/mMRZzOANTiEVC7WaBNhHcKM7x+/5E1l5NvsysR19zuDQScE8k+kfQXWRN3AtS/eOSHpg== }

  core-js@2.6.12:
    resolution:
      { integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ== }
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.

  core-util-is@1.0.3:
    resolution:
      { integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ== }

  cross-spawn@5.1.0:
    resolution:
      { integrity: sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A== }

  cross-spawn@6.0.5:
    resolution:
      { integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ== }
    engines: { node: '>=4.8' }

  css-color-keywords@1.0.0:
    resolution:
      { integrity: sha512-FyyrDHZKEjXDpNJYvVsV960FiqQyXc/LlYmsxl2BcdMb2WPx0OGRVgTg55rPSyLSNMqP52R9r8geSp7apN3Ofg== }
    engines: { node: '>=4' }

  css-line-break@2.1.0:
    resolution:
      { integrity: sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w== }

  css-to-react-native@3.2.0:
    resolution:
      { integrity: sha512-e8RKaLXMOFii+02mOlqwjbD00KSEKqblnpO9e++1aXS1fPQOpS1YoqdVHBqPjHNoxeF2mimzVqawm2KCbEdtHQ== }

  csstype@3.1.3:
    resolution:
      { integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw== }

  d3-color@3.1.0:
    resolution:
      { integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA== }
    engines: { node: '>=12' }

  d3-ease@1.0.7:
    resolution:
      { integrity: sha512-lx14ZPYkhNx0s/2HX5sLFUI3mbasHjSSpwO/KaaNACweVwxUruKyWVcb293wMv1RqTPZyZ8kSZ2NogUZNcLOFQ== }

  d3-hierarchy@2.0.0:
    resolution:
      { integrity: sha512-SwIdqM3HxQX2214EG9GTjgmCc/mbSx4mQBn+DuEETubhOw6/U3fmnji4uCVrmzOydMHSO1nZle5gh6HB/wdOzw== }

  d3-interpolate@3.0.1:
    resolution:
      { integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g== }
    engines: { node: '>=12' }

  d3-regression@1.3.10:
    resolution:
      { integrity: sha512-PF8GWEL70cHHWpx2jUQXc68r1pyPHIA+St16muk/XRokETzlegj5LriNKg7o4LR0TySug4nHYPJNNRz/W+/Niw== }

  d3-timer@1.0.10:
    resolution:
      { integrity: sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw== }

  data-view-buffer@1.0.1:
    resolution:
      { integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA== }
    engines: { node: '>= 0.4' }

  data-view-byte-length@1.0.1:
    resolution:
      { integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ== }
    engines: { node: '>= 0.4' }

  data-view-byte-offset@1.0.0:
    resolution:
      { integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA== }
    engines: { node: '>= 0.4' }

  dateformat@2.2.0:
    resolution:
      { integrity: sha512-GODcnWq3YGoTnygPfi02ygEiRxqUxpJwuRHjdhJYuxpcZmDq4rjBiXYmbCCzStxo176ixfLT6i4NPwQooRySnw== }

  dayjs@1.11.11:
    resolution:
      { integrity: sha512-okzr3f11N6WuqYtZSvm+F776mB41wRZMhKP+hc34YdW+KmtYYK9iqvHSwo2k9FEH3fhGXvOPV6yz2IcSrfRUDg== }

  debug@2.6.9:
    resolution:
      { integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA== }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.1.0:
    resolution:
      { integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g== }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution:
      { integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ== }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution:
      { integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ== }
    engines: { node: '>=6.0' }
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize@1.2.0:
    resolution:
      { integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA== }
    engines: { node: '>=0.10.0' }

  decode-uri-component@0.2.2:
    resolution:
      { integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ== }
    engines: { node: '>=0.10' }

  deep-equal@1.1.2:
    resolution:
      { integrity: sha512-5tdhKF6DbU7iIzrIOa1AOUt39ZRm13cmL1cGEh//aqR8x9+tNfbywRf0n5FD/18OKMdo7DNEtrX2t22ZAkI+eg== }
    engines: { node: '>= 0.4' }

  deep-is@0.1.4:
    resolution:
      { integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ== }

  default-user-agent@1.0.0:
    resolution:
      { integrity: sha512-bDF7bg6OSNcSwFWPu4zYKpVkJZQYVrAANMYB8bc9Szem1D0yKdm4sa/rOCs2aC9+2GMqQ7KnwtZRvDhmLF0dXw== }
    engines: { node: '>= 0.10.0' }

  define-data-property@1.1.4:
    resolution:
      { integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A== }
    engines: { node: '>= 0.4' }

  define-properties@1.2.1:
    resolution:
      { integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg== }
    engines: { node: '>= 0.4' }

  defined@1.0.1:
    resolution:
      { integrity: sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q== }

  delayed-stream@1.0.0:
    resolution:
      { integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ== }
    engines: { node: '>=0.4.0' }

  destroy@1.2.0:
    resolution:
      { integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg== }
    engines: { node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16 }

  detect-browser@5.3.0:
    resolution:
      { integrity: sha512-53rsFbGdwMwlF7qvCt0ypLM5V5/Mbl0szB7GPN8y9NCcbknYOeVVXdrXEq+90IwAfrrzt6Hd+u2E2ntakICU8w== }

  digest-header@1.1.0:
    resolution:
      { integrity: sha512-glXVh42vz40yZb9Cq2oMOt70FIoWiv+vxNvdKdU8CwjLad25qHM3trLxhl9bVjdr6WaslIXhWpn0NO8T/67Qjg== }
    engines: { node: '>= 8.0.0' }

  doctrine@2.1.0:
    resolution:
      { integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw== }
    engines: { node: '>=0.10.0' }

  doctrine@3.0.0:
    resolution:
      { integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w== }
    engines: { node: '>=6.0.0' }

  dom-helpers@3.4.0:
    resolution:
      { integrity: sha512-LnuPJ+dwqKDIyotW1VzmOZ5TONUN7CwkCR5hrgawTUbkBGYdeoNLZo6nNfGkCrjtE1nXXaj7iMMpDa8/d9WoIA== }

  dom7@3.0.0:
    resolution:
      { integrity: sha512-oNlcUdHsC4zb7Msx7JN3K0Nro1dzJ48knvBOnDPKJ2GV9wl1i5vydJZUSyOfrkKFDZEud/jBsTk92S/VGSAe/g== }

  dompurify@2.5.3:
    resolution:
      { integrity: sha512-09uyBM2URzOfXMUAqGRnm9R9IUeSkzO9PktXc2eVQIsBmmJUqRmfL1xW2QPBxVJEtlEVs5d8ndrsIQsyAqs81g== }

  dot-case@3.0.4:
    resolution:
      { integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w== }

  dotignore@0.1.2:
    resolution:
      { integrity: sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw== }
    hasBin: true

  driver-dom@2.2.2:
    resolution:
      { integrity: sha512-v/jCQnQkjv0q3Z51zYhG5MfzMjlfJURiC8mhaAwhHsih55j8AnPupurSBOJn67qAE4Ol5XiGlqDnjWAH9XM1OA== }

  driver-miniapp@0.1.5:
    resolution:
      { integrity: sha512-27SwcAaL50jhaQwhHDXqqiT1BtOw0sp1ZIk8YNvgyBLjrZJhkRx/LTS3xmfgXlKkft8wxsO3039lFXWxKTCDKA== }

  driver-universal@3.5.0:
    resolution:
      { integrity: sha512-Np6RFlzVyuy2xRmgbzlBIWYm3cIgpd2eVCNT0/Ai0fLpjaYhUUjejjobXGA7LiBR1C57YY51AbBsGZjjzQK99g== }

  driver-weex@2.1.0:
    resolution:
      { integrity: sha512-Hl/Bdubctm8Cr24acSe8NKwOztmXS7qE3kh+eNjp+NvTPr8DBpDlRIj1g/r1b2Ci82cz43b+Tm29L+sAveoh8g== }

  ee-first@1.1.1:
    resolution:
      { integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow== }

  electron-to-chromium@1.4.768:
    resolution:
      { integrity: sha512-z2U3QcvNuxdkk33YV7R1bVMNq7fL23vq3WfO5BHcqrm4TnDGReouBfYKLEFh5umoK1XACjEwp8mmnhXk2EJigw== }

  emoji-regex@7.0.3:
    resolution:
      { integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA== }

  end-of-stream@1.4.4:
    resolution:
      { integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q== }

  end-or-error@1.0.1:
    resolution:
      { integrity: sha512-OclLMSug+k2A0JKuf494im25ANRBVW8qsjmwbgX7lQ8P82H21PQ1PWkoYwb9y5yMBS69BPlwtzdIFClo3+7kOQ== }
    engines: { node: '>= 0.11.14' }

  es-abstract@1.23.3:
    resolution:
      { integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A== }
    engines: { node: '>= 0.4' }

  es-define-property@1.0.0:
    resolution:
      { integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ== }
    engines: { node: '>= 0.4' }

  es-errors@1.3.0:
    resolution:
      { integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw== }
    engines: { node: '>= 0.4' }

  es-iterator-helpers@1.0.19:
    resolution:
      { integrity: sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw== }
    engines: { node: '>= 0.4' }

  es-object-atoms@1.0.0:
    resolution:
      { integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw== }
    engines: { node: '>= 0.4' }

  es-set-tostringtag@2.0.3:
    resolution:
      { integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ== }
    engines: { node: '>= 0.4' }

  es-shim-unscopables@1.0.2:
    resolution:
      { integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw== }

  es-to-primitive@1.2.1:
    resolution:
      { integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA== }
    engines: { node: '>= 0.4' }

  escalade@3.1.2:
    resolution:
      { integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA== }
    engines: { node: '>=6' }

  escape-html@1.0.3:
    resolution:
      { integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow== }

  escape-string-regexp@1.0.5:
    resolution:
      { integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg== }
    engines: { node: '>=0.8.0' }

  eslint-config-ali@4.1.1:
    resolution:
      { integrity: sha512-vPItHMwM4e8TpXH/owvZCW0V4we7QlxSok2ObeOXwu99Gu26mbdqukH47EUDBrm93ru9HefNlYA7BQ8jrQy1dA== }
    peerDependencies:
      eslint: ^5.5.0
      eslint-plugin-import: ^2.14.0
      eslint-plugin-react: ^7.4.0

  eslint-import-resolver-node@0.3.9:
    resolution:
      { integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g== }

  eslint-module-utils@2.8.1:
    resolution:
      { integrity: sha512-rXDXR3h7cs7dy9RNpUlQf80nX31XWJEyGq1tRMo+6GsO5VmTe4UTwtmonAD4ZkAsrfMVDA2wlGJ3790Ys+D49Q== }
    engines: { node: '>=4' }
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-import@2.29.1:
    resolution:
      { integrity: sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw== }
    engines: { node: '>=4' }
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-react@7.34.1:
    resolution:
      { integrity: sha512-N97CxlouPT1AHt8Jn0mhhN2RrADlUAsk1/atcT2KyA/l9Q/E6ll7OIGwNumFmWfZ9skV3XXccYS19h80rHtgkw== }
    engines: { node: '>=4' }
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-scope@3.7.1:
    resolution:
      { integrity: sha512-ivpbtpUgg9SJS4TLjK7KdcDhqc/E3CGItsvQbBNLkNGUeMhd5qnJcryba/brESS+dg3vrLqPuc/UcS7jRJdN5A== }
    engines: { node: '>=4.0.0' }

  eslint-scope@4.0.3:
    resolution:
      { integrity: sha512-p7VutNr1O/QrxysMo3E45FjYDTeXBy0iTltPFNSqKAIfjDSXC+4dj+qfyuD8bfAXrW/y6lW3O76VaYNPKfpKrg== }
    engines: { node: '>=4.0.0' }

  eslint-utils@1.4.3:
    resolution:
      { integrity: sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q== }
    engines: { node: '>=6' }

  eslint-visitor-keys@1.3.0:
    resolution:
      { integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ== }
    engines: { node: '>=4' }

  eslint@5.16.0:
    resolution:
      { integrity: sha512-S3Rz11i7c8AA5JPv7xAH+dOyq/Cu/VXHiHXBPOU1k/JAM5dXqQPt3qcrhpHSorXmrpu2g0gkIBVXAqCpzfoZIg== }
    engines: { node: ^6.14.0 || ^8.10.0 || >=9.10.0 }
    hasBin: true

  espree@5.0.1:
    resolution:
      { integrity: sha512-qWAZcWh4XE/RwzLJejfcofscgMc9CamR6Tn1+XRXNzrvUSSbiAjGOI/fggztjIi7y9VLPqnICMIPiGyr8JaZ0A== }
    engines: { node: '>=6.0.0' }

  esprima@4.0.1:
    resolution:
      { integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A== }
    engines: { node: '>=4' }
    hasBin: true

  esquery@1.5.0:
    resolution:
      { integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg== }
    engines: { node: '>=0.10' }

  esrecurse@4.3.0:
    resolution:
      { integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag== }
    engines: { node: '>=4.0' }

  estraverse@4.3.0:
    resolution:
      { integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw== }
    engines: { node: '>=4.0' }

  estraverse@5.3.0:
    resolution:
      { integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA== }
    engines: { node: '>=4.0' }

  esutils@2.0.3:
    resolution:
      { integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g== }
    engines: { node: '>=0.10.0' }

  eventemitter3@4.0.7:
    resolution:
      { integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw== }

  execa@0.8.0:
    resolution:
      { integrity: sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA== }
    engines: { node: '>=4' }

  extend-shallow@2.0.1:
    resolution:
      { integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug== }
    engines: { node: '>=0.10.0' }

  external-editor@3.1.0:
    resolution:
      { integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew== }
    engines: { node: '>=4' }

  fast-deep-equal@3.1.3:
    resolution:
      { integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q== }

  fast-json-stable-stringify@2.1.0:
    resolution:
      { integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw== }

  fast-levenshtein@2.0.6:
    resolution:
      { integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw== }

  fecha@4.2.3:
    resolution:
      { integrity: sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw== }

  figures@2.0.0:
    resolution:
      { integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA== }
    engines: { node: '>=4' }

  file-entry-cache@5.0.1:
    resolution:
      { integrity: sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g== }
    engines: { node: '>=4' }

  fill-range@7.0.1:
    resolution:
      { integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ== }
    engines: { node: '>=8' }

  filter-obj@1.1.0:
    resolution:
      { integrity: sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ== }
    engines: { node: '>=0.10.0' }

  find-up@2.1.0:
    resolution:
      { integrity: sha512-NWzkk0jSJtTt08+FBFMvXoeZnOJD+jTtsRmBYbAIzJdX6l7dLgR7CTubCM5/eDdPUBvLCeVasP1brfVR/9/EZQ== }
    engines: { node: '>=4' }

  flat-cache@2.0.1:
    resolution:
      { integrity: sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA== }
    engines: { node: '>=4' }

  flatted@2.0.2:
    resolution:
      { integrity: sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA== }

  fmin@0.0.2:
    resolution:
      { integrity: sha512-sSi6DzInhl9d8yqssDfGZejChO8d2bAGIpysPsvYsxFe898z89XhCZg6CPNV3nhUhFefeC/AXZK2bAJxlBjN6A== }

  follow-redirects@1.15.6:
    resolution:
      { integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA== }
    engines: { node: '>=4.0' }
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  follow-redirects@1.5.10:
    resolution:
      { integrity: sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ== }
    engines: { node: '>=4.0' }

  for-each@0.3.3:
    resolution:
      { integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw== }

  form-data@4.0.0:
    resolution:
      { integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww== }
    engines: { node: '>= 6' }

  formstream@1.4.0:
    resolution:
      { integrity: sha512-nEJvWnCujHjFMZjn3woD3tFnojT/w0wvUgPFOuASPmFFCJ4fJacqXowppN7lvhBuZb+QF41ZRLT+N6A6SxHBgA== }

  fs.realpath@1.0.0:
    resolution:
      { integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw== }

  fsevents@2.3.3:
    resolution:
      { integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw== }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      { integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA== }

  function.prototype.name@1.1.6:
    resolution:
      { integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg== }
    engines: { node: '>= 0.4' }

  functional-red-black-tree@1.0.1:
    resolution:
      { integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g== }

  functions-have-names@1.2.3:
    resolution:
      { integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ== }

  gensync@1.0.0-beta.2:
    resolution:
      { integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg== }
    engines: { node: '>=6.9.0' }

  get-intrinsic@1.2.4:
    resolution:
      { integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ== }
    engines: { node: '>= 0.4' }

  get-ready@1.0.0:
    resolution:
      { integrity: sha512-mFXCZPJIlcYcth+N8267+mghfYN9h3EhsDa6JSnbA3Wrhh/XFpuowviFcsDeYZtKspQyWyJqfs4O6P8CHeTwzw== }

  get-stream@3.0.0:
    resolution:
      { integrity: sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ== }
    engines: { node: '>=4' }

  get-symbol-description@1.0.2:
    resolution:
      { integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg== }
    engines: { node: '>= 0.4' }

  gl-matrix@3.4.3:
    resolution:
      { integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA== }

  glob-parent@5.1.2:
    resolution:
      { integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow== }
    engines: { node: '>= 6' }

  glob@7.2.3:
    resolution:
      { integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q== }

  globals@11.12.0:
    resolution:
      { integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA== }
    engines: { node: '>=4' }

  globalthis@1.0.4:
    resolution:
      { integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ== }
    engines: { node: '>= 0.4' }

  gopd@1.0.1:
    resolution:
      { integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA== }

  has-ansi@2.0.0:
    resolution:
      { integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg== }
    engines: { node: '>=0.10.0' }

  has-bigints@1.0.2:
    resolution:
      { integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ== }

  has-flag@3.0.0:
    resolution:
      { integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw== }
    engines: { node: '>=4' }

  has-property-descriptors@1.0.2:
    resolution:
      { integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg== }

  has-proto@1.0.3:
    resolution:
      { integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q== }
    engines: { node: '>= 0.4' }

  has-symbols@1.0.3:
    resolution:
      { integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A== }
    engines: { node: '>= 0.4' }

  has-tostringtag@1.0.2:
    resolution:
      { integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw== }
    engines: { node: '>= 0.4' }

  has@1.0.4:
    resolution:
      { integrity: sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ== }
    engines: { node: '>= 0.4.0' }

  hasown@2.0.2:
    resolution:
      { integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ== }
    engines: { node: '>= 0.4' }

  history@4.10.1:
    resolution:
      { integrity: sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew== }

  hoist-non-react-statics@3.3.2:
    resolution:
      { integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw== }

  html2canvas@1.4.1:
    resolution:
      { integrity: sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA== }
    engines: { node: '>=8.0.0' }

  humanize-ms@1.2.1:
    resolution:
      { integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ== }

  iconv-lite@0.4.24:
    resolution:
      { integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA== }
    engines: { node: '>=0.10.0' }

  idb-keyval@5.1.5:
    resolution:
      { integrity: sha512-J1utxYWQokYjy01LvDQ7WmiAtZCGUSkVi9EIBfUSyLOr/BesnMIxNGASTh9A1LzeISSjSqEPsfFdTss7EE7ofQ== }

  idb-keyval@6.2.1:
    resolution:
      { integrity: sha512-8Sb3veuYCyrZL+VBt9LJfZjLUPWVvqn8tG28VqYNFCo43KHcKuq+b4EiXGeuaLAQWL2YmyDgMp2aSpH9JHsEQg== }

  ignore@3.3.10:
    resolution:
      { integrity: sha512-Pgs951kaMm5GXP7MOvxERINe3gsaVjUWFm+UZPSq9xYriQAksyhg0csnS0KXSNRD5NmNdapXEpjxG49+AKh/ug== }

  ignore@4.0.6:
    resolution:
      { integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg== }
    engines: { node: '>= 4' }

  imask@6.6.3:
    resolution:
      { integrity: sha512-a9MTDhm+ET4G2IRcdUGuVTXHS05WsRNPGM5CeNJnXiXuoi4zv7g0/UDFLlRF4lBBeb8EWds4C4JVwhI0nuAIug== }
    engines: { npm: '>=4.0.0' }

  immutable@4.3.6:
    resolution:
      { integrity: sha512-Ju0+lEMyzMVZarkTn/gqRpdqd5dOPaz1mCZ0SH3JV6iFw81PldE/PEB1hWVEA288HPt4WXW8O7AWxB10M+03QQ== }

  import-fresh@3.3.0:
    resolution:
      { integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw== }
    engines: { node: '>=6' }

  imurmurhash@0.1.4:
    resolution:
      { integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA== }
    engines: { node: '>=0.8.19' }

  inflight@1.0.6:
    resolution:
      { integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA== }

  inherits@2.0.4:
    resolution:
      { integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ== }

  inquirer@6.5.2:
    resolution:
      { integrity: sha512-cntlB5ghuB0iuO65Ovoi8ogLHiWGs/5yNrtUcKjFhSSiVeAIVpD7koaSU9RM8mpXw5YDi9RdYXGQMaOURB7ycQ== }
    engines: { node: '>=6.0.0' }

  internal-slot@1.0.7:
    resolution:
      { integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g== }
    engines: { node: '>= 0.4' }

  intersection-observer@0.12.2:
    resolution:
      { integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg== }

  intersection-observer@0.7.0:
    resolution:
      { integrity: sha512-Id0Fij0HsB/vKWGeBe9PxeY45ttRiBmhFyyt/geBdDHBYNctMRTE3dC1U3ujzz3lap+hVXlEcVaB56kZP/eEUg== }

  intl-messageformat-parser@1.4.0:
    resolution:
      { integrity: sha512-/XkqFHKezO6UcF4Av2/Lzfrez18R0jyw7kRFhSeB/YRakdrgSc9QfFZUwNJI9swMwMoNPygK1ArC5wdFSjPw+A== }
    deprecated: We've written a new parser that's 6x faster and is backwards compatible. Please use @formatjs/icu-messageformat-parser

  intl-messageformat@2.2.0:
    resolution:
      { integrity: sha512-I+tSvHnXqJYjDfNmY95tpFMj30yoakC6OXAo+wu/wTMy6tA/4Fd4mvV7Uzs4cqK/Ap29sHhwjcY+78a8eifcXw== }

  intl-messageformat@9.13.0:
    resolution:
      { integrity: sha512-7sGC7QnSQGa5LZP7bXLDhVDtQOeKGeBFGHF2Y8LVBwYZoQZCgWeKoPGTa5GMG8g/TzDgeXuYJQis7Ggiw2xTOw== }

  invariant@2.2.4:
    resolution:
      { integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA== }

  ip@1.1.9:
    resolution:
      { integrity: sha512-cyRxvOEpNHNtchU3Ln9KC/auJgup87llfQpQ+t5ghoC/UhL16SWzbueiCsdTnWmqAWl7LadfuwhlqmtOaqMHdQ== }

  is-arguments@1.1.1:
    resolution:
      { integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA== }
    engines: { node: '>= 0.4' }

  is-array-buffer@3.0.4:
    resolution:
      { integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw== }
    engines: { node: '>= 0.4' }

  is-async-function@2.0.0:
    resolution:
      { integrity: sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA== }
    engines: { node: '>= 0.4' }

  is-bigint@1.0.4:
    resolution:
      { integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg== }

  is-binary-path@2.1.0:
    resolution:
      { integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw== }
    engines: { node: '>=8' }

  is-boolean-object@1.1.2:
    resolution:
      { integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA== }
    engines: { node: '>= 0.4' }

  is-buffer@1.1.6:
    resolution:
      { integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w== }

  is-callable@1.2.7:
    resolution:
      { integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA== }
    engines: { node: '>= 0.4' }

  is-class-hotfix@0.0.6:
    resolution:
      { integrity: sha512-0n+pzCC6ICtVr/WXnN2f03TK/3BfXY7me4cjCAqT8TYXEl0+JBRoqBo94JJHXcyDSLUeWbNX8Fvy5g5RJdAstQ== }

  is-core-module@2.13.1:
    resolution:
      { integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw== }

  is-data-view@1.0.1:
    resolution:
      { integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w== }
    engines: { node: '>= 0.4' }

  is-date-object@1.0.5:
    resolution:
      { integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ== }
    engines: { node: '>= 0.4' }

  is-extendable@0.1.1:
    resolution:
      { integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw== }
    engines: { node: '>=0.10.0' }

  is-extglob@2.1.1:
    resolution:
      { integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ== }
    engines: { node: '>=0.10.0' }

  is-finalizationregistry@1.0.2:
    resolution:
      { integrity: sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw== }

  is-fullwidth-code-point@2.0.0:
    resolution:
      { integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w== }
    engines: { node: '>=4' }

  is-generator-function@1.0.10:
    resolution:
      { integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A== }
    engines: { node: '>= 0.4' }

  is-glob@4.0.3:
    resolution:
      { integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg== }
    engines: { node: '>=0.10.0' }

  is-map@2.0.3:
    resolution:
      { integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw== }
    engines: { node: '>= 0.4' }

  is-negative-zero@2.0.3:
    resolution:
      { integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw== }
    engines: { node: '>= 0.4' }

  is-number-object@1.0.7:
    resolution:
      { integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ== }
    engines: { node: '>= 0.4' }

  is-number@7.0.0:
    resolution:
      { integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng== }
    engines: { node: '>=0.12.0' }

  is-regex@1.1.4:
    resolution:
      { integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg== }
    engines: { node: '>= 0.4' }

  is-set@2.0.3:
    resolution:
      { integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg== }
    engines: { node: '>= 0.4' }

  is-shared-array-buffer@1.0.3:
    resolution:
      { integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg== }
    engines: { node: '>= 0.4' }

  is-stream@1.1.0:
    resolution:
      { integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ== }
    engines: { node: '>=0.10.0' }

  is-string@1.0.7:
    resolution:
      { integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg== }
    engines: { node: '>= 0.4' }

  is-symbol@1.0.4:
    resolution:
      { integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg== }
    engines: { node: '>= 0.4' }

  is-type-of@1.4.0:
    resolution:
      { integrity: sha512-EddYllaovi5ysMLMEN7yzHEKh8A850cZ7pykrY1aNRQGn/CDjRDE9qEWbIdt7xGEVJmjBXzU/fNnC4ABTm8tEQ== }

  is-typed-array@1.1.13:
    resolution:
      { integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw== }
    engines: { node: '>= 0.4' }

  is-weakmap@2.0.2:
    resolution:
      { integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w== }
    engines: { node: '>= 0.4' }

  is-weakref@1.0.2:
    resolution:
      { integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ== }

  is-weakset@2.0.3:
    resolution:
      { integrity: sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ== }
    engines: { node: '>= 0.4' }

  isarray@0.0.1:
    resolution:
      { integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ== }

  isarray@1.0.0:
    resolution:
      { integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ== }

  isarray@2.0.5:
    resolution:
      { integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw== }

  isexe@2.0.0:
    resolution:
      { integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw== }

  isstream@0.1.2:
    resolution:
      { integrity: sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g== }

  iterator.prototype@1.1.2:
    resolution:
      { integrity: sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w== }

  js-base64@2.6.4:
    resolution:
      { integrity: sha512-pZe//GGmwJndub7ZghVHz7vjb2LgC1m8B07Au3eYqeqv9emhESByMXxaEgkUkEqJe87oBbSniGYoQNIBklc7IQ== }

  js-cookie@2.2.1:
    resolution:
      { integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ== }

  js-tokens@3.0.2:
    resolution:
      { integrity: sha512-RjTcuD4xjtthQkaWH7dFlH85L+QaVtSoOyGdZ3g6HFhS9dFNDfLyqgm2NFe2X6cQpeFmt0452FJjFG5UameExg== }

  js-tokens@4.0.0:
    resolution:
      { integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ== }

  js-yaml@3.14.1:
    resolution:
      { integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g== }
    hasBin: true

  jsesc@2.5.2:
    resolution:
      { integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA== }
    engines: { node: '>=4' }
    hasBin: true

  json-schema-traverse@0.4.1:
    resolution:
      { integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg== }

  json-stable-stringify-without-jsonify@1.0.1:
    resolution:
      { integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw== }

  json2module@0.0.3:
    resolution:
      { integrity: sha512-qYGxqrRrt4GbB8IEOy1jJGypkNsjWoIMlZt4bAsmUScCA507Hbc2p1JOhBzqn45u3PWafUgH2OnzyNU7udO/GA== }
    hasBin: true

  json5@1.0.2:
    resolution:
      { integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA== }
    hasBin: true

  json5@2.2.3:
    resolution:
      { integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg== }
    engines: { node: '>=6' }
    hasBin: true

  jstoxml@2.2.9:
    resolution:
      { integrity: sha512-OYWlK0j+roh+eyaMROlNbS5cd5R25Y+IUpdl7cNdB8HNrkgwQzIS7L9MegxOiWNBj9dQhA/yAxiMwCC5mwNoBw== }

  jsx-ast-utils@3.3.5:
    resolution:
      { integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ== }
    engines: { node: '>=4.0' }

  kind-of@3.2.2:
    resolution:
      { integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ== }
    engines: { node: '>=0.10.0' }

  lazy-cache@1.0.4:
    resolution:
      { integrity: sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ== }
    engines: { node: '>=0.10.0' }

  levn@0.3.0:
    resolution:
      { integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA== }
    engines: { node: '>= 0.8.0' }

  locate-path@2.0.0:
    resolution:
      { integrity: sha512-NCI2kiDkyR7VeEKm27Kda/iQHyKJe1Bu0FlTbYp3CqJu+9IFe9bLyAjMxf5ZDDbEg+iMPzB5zYyUTSm8wVTKmA== }
    engines: { node: '>=4' }

  lodash-es@4.17.21:
    resolution:
      { integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw== }

  lodash.clonedeep@4.5.0:
    resolution:
      { integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ== }

  lodash.debounce@4.0.8:
    resolution:
      { integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow== }

  lodash.get@4.4.2:
    resolution:
      { integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ== }

  lodash.isequal@4.5.0:
    resolution:
      { integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ== }

  lodash.set@4.3.2:
    resolution:
      { integrity: sha512-4hNPN5jlm/N/HLMCO43v8BXKq9Z7QdAGc/VGrRD61w8gN9g/6jF9A4L1pbUgBLCffi0w9VsXfTOij5x8iTyFvg== }

  lodash.throttle@4.1.1:
    resolution:
      { integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ== }

  lodash@4.17.21:
    resolution:
      { integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg== }

  longest@1.0.1:
    resolution:
      { integrity: sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg== }
    engines: { node: '>=0.10.0' }

  loose-envify@1.4.0:
    resolution:
      { integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q== }
    hasBin: true

  lower-case@2.0.2:
    resolution:
      { integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg== }

  lru-cache@4.1.5:
    resolution:
      { integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g== }

  lru-cache@5.1.1:
    resolution:
      { integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w== }

  merge-descriptors@1.0.3:
    resolution:
      { integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ== }

  mime-db@1.52.0:
    resolution:
      { integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg== }
    engines: { node: '>= 0.6' }

  mime-types@2.1.35:
    resolution:
      { integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw== }
    engines: { node: '>= 0.6' }

  mime@2.6.0:
    resolution:
      { integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg== }
    engines: { node: '>=4.0.0' }
    hasBin: true

  mimic-fn@1.2.0:
    resolution:
      { integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ== }
    engines: { node: '>=4' }

  minimatch@3.1.2:
    resolution:
      { integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw== }

  minimist@1.2.8:
    resolution:
      { integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA== }

  mkdirp@0.5.6:
    resolution:
      { integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw== }
    hasBin: true

  mock-property@1.0.3:
    resolution:
      { integrity: sha512-2emPTb1reeLLYwHxyVx993iYyCHEiRRO+y8NFXFPL5kl5q14sgTK76cXyEKkeKCHeRw35SfdkUJ10Q1KfHuiIQ== }
    engines: { node: '>= 0.4' }

  moment-timezone@0.5.45:
    resolution:
      { integrity: sha512-HIWmqA86KcmCAhnMAN0wuDOARV/525R2+lOLotuGFzn4HO+FH+/645z2wx0Dt3iDv6/p61SIvKnDstISainhLQ== }

  moment@2.30.1:
    resolution:
      { integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how== }

  mri@1.2.0:
    resolution:
      { integrity: sha512-tzzskb3bG8LvYGFF/mDTpq3jpI6Q9wc3LEmBaghu+DdCssd1FakN7Bc0hVNmEyGq1bq3RgfkCb3cmQLpNPOroA== }
    engines: { node: '>=4' }

  ms@2.0.0:
    resolution:
      { integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A== }

  ms@2.1.2:
    resolution:
      { integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w== }

  ms@2.1.3:
    resolution:
      { integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA== }

  multimatch@3.0.0:
    resolution:
      { integrity: sha512-22foS/gqQfANZ3o+W7ST2x25ueHDVNWl/b9OlGcLpy/iKxjCpvcNCM51YCenUi7Mt/jAjjqv8JwZRs8YP5sRjA== }
    engines: { node: '>=6' }

  mute-stream@0.0.7:
    resolution:
      { integrity: sha512-r65nCZhrbXXb6dXOACihYApHw2Q6pV0M3V0PSxd74N0+D8nzAdEAITq2oAjA1jVnKI+tGvEBUpqiMh0+rW6zDQ== }

  mz@2.7.0:
    resolution:
      { integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q== }

  nanoid@4.0.2:
    resolution:
      { integrity: sha512-7ZtY5KTCNheRGfEFxnedV5zFiORN1+Y1N6zvPTnHQd8ENUvfaDBeuJDZb2bN/oXwXxu3qkTXDzy57W5vAmDTBw== }
    engines: { node: ^14 || ^16 || >=18 }
    hasBin: true

  natural-compare@1.4.0:
    resolution:
      { integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw== }

  nice-try@1.0.5:
    resolution:
      { integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ== }

  no-case@3.0.4:
    resolution:
      { integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg== }

  node-releases@2.0.14:
    resolution:
      { integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw== }

  normalize-path@3.0.0:
    resolution:
      { integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA== }
    engines: { node: '>=0.10.0' }

  npm-run-path@2.0.2:
    resolution:
      { integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw== }
    engines: { node: '>=4' }

  object-assign@4.1.1:
    resolution:
      { integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg== }
    engines: { node: '>=0.10.0' }

  object-inspect@1.12.3:
    resolution:
      { integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g== }

  object-inspect@1.13.1:
    resolution:
      { integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ== }

  object-is@1.1.6:
    resolution:
      { integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q== }
    engines: { node: '>= 0.4' }

  object-keys@1.1.1:
    resolution:
      { integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA== }
    engines: { node: '>= 0.4' }

  object.assign@4.1.5:
    resolution:
      { integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ== }
    engines: { node: '>= 0.4' }

  object.entries@1.1.8:
    resolution:
      { integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ== }
    engines: { node: '>= 0.4' }

  object.fromentries@2.0.8:
    resolution:
      { integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ== }
    engines: { node: '>= 0.4' }

  object.groupby@1.0.3:
    resolution:
      { integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ== }
    engines: { node: '>= 0.4' }

  object.hasown@1.1.4:
    resolution:
      { integrity: sha512-FZ9LZt9/RHzGySlBARE3VF+gE26TxR38SdmqOqliuTnl9wrKulaQs+4dee1V+Io8VfxqzAfHu6YuRgUy8OHoTg== }
    engines: { node: '>= 0.4' }

  object.values@1.2.0:
    resolution:
      { integrity: sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ== }
    engines: { node: '>= 0.4' }

  omit.js@2.0.2:
    resolution:
      { integrity: sha512-hJmu9D+bNB40YpL9jYebQl4lsTW6yEHRTroJzNLqQJYHm7c+NQnJGfZmIWh8S3q3KoaxV1aLhV6B3+0N0/kyJg== }

  once@1.4.0:
    resolution:
      { integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w== }

  onetime@2.0.1:
    resolution:
      { integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ== }
    engines: { node: '>=4' }

  optionator@0.8.3:
    resolution:
      { integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA== }
    engines: { node: '>= 0.8.0' }

  os-name@1.0.3:
    resolution:
      { integrity: sha512-f5estLO2KN8vgtTRaILIgEGBoBrMnZ3JQ7W9TMZCnOIGwHe8TRGSpcagnWDo+Dfhd/z08k9Xe75hvciJJ8Qaew== }
    engines: { node: '>=0.10.0' }
    hasBin: true

  os-shim@0.1.3:
    resolution:
      { integrity: sha512-jd0cvB8qQ5uVt0lvCIexBaROw1KyKm5sbulg2fWOHjETisuCzWyt+eTZKEMs8v6HwzoGs8xik26jg7eCM6pS+A== }
    engines: { node: '>= 0.4.0' }

  os-tmpdir@1.0.2:
    resolution:
      { integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g== }
    engines: { node: '>=0.10.0' }

  osx-release@1.1.0:
    resolution:
      { integrity: sha512-ixCMMwnVxyHFQLQnINhmIpWqXIfS2YOXchwQrk+OFzmo6nDjQ0E4KXAyyUh0T0MZgV4bUhkRrAbVqlE4yLVq4A== }
    engines: { node: '>=0.10.0' }
    hasBin: true

  p-finally@1.0.0:
    resolution:
      { integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow== }
    engines: { node: '>=4' }

  p-limit@1.3.0:
    resolution:
      { integrity: sha512-vvcXsLAJ9Dr5rQOPk7toZQZJApBl2K4J6dANSsEuh6QI41JYcsS/qhTGa9ErIUUgK3WNQoJYvylxvjqmiqEA9Q== }
    engines: { node: '>=4' }

  p-locate@2.0.0:
    resolution:
      { integrity: sha512-nQja7m7gSKuewoVRen45CtVfODR3crN3goVQ0DDZ9N3yHxgpkuBhZqsaiotSQRrADUrne346peY7kT3TSACykg== }
    engines: { node: '>=4' }

  p-try@1.0.0:
    resolution:
      { integrity: sha512-U1etNYuMJoIz3ZXSrrySFjsXQTWOx2/jdi86L+2pRvph/qMKL6sbcCYdH23fqsbm8TH2Gn0OybpT4eSFlCVHww== }
    engines: { node: '>=4' }

  param-case@3.0.4:
    resolution:
      { integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A== }

  parent-module@1.0.1:
    resolution:
      { integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g== }
    engines: { node: '>=6' }

  pascal-case@3.1.2:
    resolution:
      { integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g== }

  path-exists@3.0.0:
    resolution:
      { integrity: sha512-bpC7GYwiDYQ4wYLe+FA8lhRjhQCMcQGuSgGGqDkg/QerRWw9CmGRT0iSOVRSZJ29NMLZgIzqaljJ63oaL4NIJQ== }
    engines: { node: '>=4' }

  path-is-absolute@1.0.1:
    resolution:
      { integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg== }
    engines: { node: '>=0.10.0' }

  path-is-inside@1.0.2:
    resolution:
      { integrity: sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w== }

  path-key@2.0.1:
    resolution:
      { integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw== }
    engines: { node: '>=4' }

  path-parse@1.0.7:
    resolution:
      { integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw== }

  path-to-regexp@1.8.0:
    resolution:
      { integrity: sha512-n43JRhlUKUAlibEJhPeir1ncUID16QnEjNpwzNdO3Lm4ywrBpBZ5oLD0I6br9evr1Y9JTqwRtAh7JLoOzAQdVA== }

  pause-stream@0.0.11:
    resolution:
      { integrity: sha512-e3FBlXLmN/D1S+zHzanP4E/4Z60oFAa3O051qt1pxa7DEJWKAyil6upYVXCWadEnuoqa4Pkc9oUx9zsxYeRv8A== }

  pdfast@0.2.0:
    resolution:
      { integrity: sha512-cq6TTu6qKSFUHwEahi68k/kqN2mfepjkGrG9Un70cgdRRKLKY6Rf8P8uvP2NvZktaQZNF3YE7agEkLj0vGK9bA== }

  picocolors@1.0.1:
    resolution:
      { integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew== }

  picomatch@2.3.1:
    resolution:
      { integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA== }
    engines: { node: '>=8.6' }

  platform@1.3.6:
    resolution:
      { integrity: sha512-fnWVljUchTro6RiCFvCXBbNhJc2NijN7oIQxbwsyL0buWJPG85v81ehlHI9fXrJsMNgTofEoWIQeClKpgxFLrg== }

  possible-typed-array-names@1.0.0:
    resolution:
      { integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q== }
    engines: { node: '>= 0.4' }

  postcss-value-parser@4.2.0:
    resolution:
      { integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ== }

  pre-commit@1.2.2:
    resolution:
      { integrity: sha512-qokTiqxD6GjODy5ETAIgzsRgnBWWQHQH2ghy86PU7mIn/wuWeTwF3otyNQZxWBwVn8XNr8Tdzj/QfUXpH+gRZA== }

  prelude-ls@1.1.2:
    resolution:
      { integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w== }
    engines: { node: '>= 0.8.0' }

  prettier@1.19.1:
    resolution:
      { integrity: sha512-s7PoyDv/II1ObgQunCbB9PdLmUcBZcnWOcxDh7O0N/UwDEsHyqkW+Qh28jW+mVuCdx7gLB0BotYI1Y6uI9iyew== }
    engines: { node: '>=4' }
    hasBin: true

  pretty-quick@1.11.1:
    resolution:
      { integrity: sha512-kSXCkcETfak7EQXz6WOkCeCqpbC4GIzrN/vaneTGMP/fAtD8NerA9bPhCUqHAks1geo7biZNl5uEMPceeneLuA== }
    hasBin: true
    peerDependencies:
      prettier: '>=1.8.0'

  process-nextick-args@2.0.1:
    resolution:
      { integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag== }

  progress@2.0.3:
    resolution:
      { integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA== }
    engines: { node: '>=0.4.0' }

  prop-types@15.8.1:
    resolution:
      { integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg== }

  proxy-from-env@1.1.0:
    resolution:
      { integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg== }

  pseudomap@1.0.2:
    resolution:
      { integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ== }

  pump@3.0.0:
    resolution:
      { integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww== }

  punycode@1.4.1:
    resolution:
      { integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ== }

  punycode@2.3.1:
    resolution:
      { integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg== }
    engines: { node: '>=6' }

  qr.js@0.0.0:
    resolution:
      { integrity: sha512-c4iYnWb+k2E+vYpRimHqSu575b1/wKl4XFeJGpFmrJQz5I88v9aY2czh7s0w36srfCM1sXgC/xpoJz5dJfq+OQ== }

  qrcode.react@1.0.1:
    resolution:
      { integrity: sha512-8d3Tackk8IRLXTo67Y+c1rpaiXjoz/Dd2HpcMdW//62/x8J1Nbho14Kh8x974t9prsLHN6XqVgcnRiBGFptQmg== }
    peerDependencies:
      react: ^15.5.3 || ^16.0.0 || ^17.0.0

  qs@6.12.1:
    resolution:
      { integrity: sha512-zWmv4RSuB9r2mYQw3zxQuHWeU+42aKi1wWig/j4ele4ygELZ7PEO6MM7rim9oAQH2A5MWfsAVf/jPvTPgCbvUQ== }
    engines: { node: '>=0.6' }

  query-string@6.14.1:
    resolution:
      { integrity: sha512-XDxAeVmpfu1/6IjyT/gXHOl+S0vQ9owggJ30hhWKdHAsNPOcasn5o9BW0eejZqL2e4vMjhAxoW3jVHcD6mbcYw== }
    engines: { node: '>=6' }

  rax-children@1.0.0:
    resolution:
      { integrity: sha512-sBKEXAMj9ik6SsPfPGgcQnqggmbWFyBdvAV/Cz/0f04bRA86BtWgbMri/9Dce0k8nkEC/BGWiiTdyA8Q49zIiw== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      rax: ^1.0.0

  rax-clone-element@1.0.0:
    resolution:
      { integrity: sha512-TaQMVuzoglvCTjbWATlvvwARmeWnG8kpENWNXrNDv0++x29GHNND/TBbx7sdtVs/QmYwYc8YmwRUhaBwKQi5eQ== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      rax: ^1.0.0

  rax-create-factory@1.0.0:
    resolution:
      { integrity: sha512-blBaVrurj/BOWelJhQWiuc0Kk8Ons1jsNsX78omaPBLkSOL7OkyJ3NC/0iKXHu425yWrGB6e5vho/qabROC7VQ== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      rax: ^1.0.0

  rax-is-valid-element@1.0.1:
    resolution:
      { integrity: sha512-ajvQlLr7sr7UK4T9lyJyn1gcdu7NIrF3NUAj+SWWNwG2GZ5ygKdSCRFzuu7evuAwLxqrCCk2tQLV6uxw0zYUXQ== }
    engines: { npm: '>=3.0.0' }

  rax@1.2.3:
    resolution:
      { integrity: sha512-/lkZ7Yb3be06e68y4wn5WIyudzvrnHiTEjeAznJ7nPrfmjoCan2hcKPXmTYXdHU+IqkvGbzDlLZRWbVtmRTAcw== }
    engines: { npm: '>=3.0.0' }

  react-display-name-export@1.0.0:
    resolution:
      { integrity: sha512-lc1EIjV+3PVe172H+zgvxGIEtvNh5QlaF3uRb8N+WsBJI53ujiuAF8S+azSmYaNva1BRVaLVtEUno+4FHYfrwQ== }

  react-dom@16.14.0:
    resolution:
      { integrity: sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw== }
    peerDependencies:
      react: ^16.14.0

  react-dom@18.3.1:
    resolution:
      { integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw== }
    peerDependencies:
      react: ^18.3.1

  react-error-boundary@3.0.2:
    resolution:
      { integrity: sha512-KVzCusRTFpUYG0OFJbzbdRuxNQOBiGXVCqyNpBXM9z5NFsFLzMjUXMjx8gTja6M6WH+D2PvP3yKz4d8gD1PRaA== }
    engines: { node: '>=10', npm: '>=6' }
    peerDependencies:
      react: '>=16.13.1'

  react-error-boundary@3.1.4:
    resolution:
      { integrity: sha512-uM9uPzZJTF6wRQORmSrvOIgt4lJ9MC1sNgEOj2XGsDTRE4kmpWxg7ENK9EWNKJRMAOY9z0MuF4yIfl6gp4sotA== }
    engines: { node: '>=10', npm: '>=6' }
    peerDependencies:
      react: '>=16.13.1'

  react-fast-compare@3.2.2:
    resolution:
      { integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ== }

  react-imask@6.6.3:
    resolution:
      { integrity: sha512-ZjqKXqBtc1LlFQWEj/SNuk4rQWXDdzLnDqCfNMnqdHHMqSvD1J+JuQZTuWgCCr5cLJxaXshDHW5AmhClk367Gw== }
    engines: { npm: '>=4.0.0' }
    peerDependencies:
      react: '>=0.14.0'

  react-intl@5.25.1:
    resolution:
      { integrity: sha512-pkjdQDvpJROoXLMltkP/5mZb0/XqrqLoPGKUCfbdkP8m6U9xbK40K51Wu+a4aQqTEvEK5lHBk0fWzUV72SJ3Hg== }
    peerDependencies:
      react: ^16.3.0 || 17 || 18
      typescript: ^4.5
    peerDependenciesMeta:
      typescript:
        optional: true

  react-is@16.13.1:
    resolution:
      { integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ== }

  react-is@18.3.1:
    resolution:
      { integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg== }

  react-lifecycles-compat@3.0.4:
    resolution:
      { integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA== }

  react-reconciler@0.25.1:
    resolution:
      { integrity: sha512-R5UwsIvRcSs3w8n9k3tBoTtUHdVhu9u84EG7E5M0Jk9F5i6DA1pQzPfUZd6opYWGy56MJOtV3VADzy6DRwYDjw== }
    engines: { node: '>=0.10.0' }
    peerDependencies:
      react: ^16.13.1

  react-router-dom@5.3.4:
    resolution:
      { integrity: sha512-m4EqFMHv/Ih4kpcBCONHbkT68KoAeHN4p3lAGoNryfHi0dMy0kCzEZakiKRsvg5wHZ/JLrLW8o8KomWiz/qbYQ== }
    peerDependencies:
      react: '>=15'

  react-router@5.3.4:
    resolution:
      { integrity: sha512-Ys9K+ppnJah3QuaRiLxk+jDWOR1MekYQrlytiXxC1RyfbdsZkS5pvKAzCCr031xHixZwpnsYNT5xysdFHQaYsA== }
    peerDependencies:
      react: '>=15'

  react-sortable-hoc@1.11.0:
    resolution:
      { integrity: sha512-v1CDCvdfoR3zLGNp6qsBa4J1BWMEVH25+UKxF/RvQRh+mrB+emqtVHMgZ+WreUiKJoEaiwYoScaueIKhMVBHUg== }
    peerDependencies:
      prop-types: ^15.5.7
      react: ^0.14.0 || ^15.0.0 || ^16.0.0
      react-dom: ^0.14.0 || ^15.0.0 || ^16.0.0

  react-sticky-box@0.9.3:
    resolution:
      { integrity: sha512-Y/qO7vTqAvXuRR6G6ZCW4fX2Bz0GZRwiiLTVeZN5CVz9wzs37ev0Xj3KSKF/PzF0jifwATivI4t24qXG8rSz4Q== }
    peerDependencies:
      prop-types: ^15.6.2
      react: ^0.14.0 || ^15.0.0 || ^16.0.0

  react-string-replace@1.1.1:
    resolution:
      { integrity: sha512-26TUbLzLfHQ5jO5N7y3Mx88eeKo0Ml0UjCQuX4BMfOd/JX+enQqlKpL1CZnmjeBRvQE8TR+ds9j1rqx9CxhKHQ== }
    engines: { node: '>=0.12.0' }

  react-transition-group@2.9.0:
    resolution:
      { integrity: sha512-+HzNTCHpeQyl4MJ/bdE0u6XRMe9+XG/+aL4mCxVN4DnPBQ0/5bfHWPDuOZUzYdMj94daZaZdCCc1Dzt9R/xSSg== }
    peerDependencies:
      react: '>=15.0.0'
      react-dom: '>=15.0.0'

  react@16.14.0:
    resolution:
      { integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g== }
    engines: { node: '>=0.10.0' }

  react@18.3.1:
    resolution:
      { integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ== }
    engines: { node: '>=0.10.0' }

  readable-stream@2.3.8:
    resolution:
      { integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA== }

  readdirp@3.6.0:
    resolution:
      { integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA== }
    engines: { node: '>=8.10.0' }

  reflect.getprototypeof@1.0.6:
    resolution:
      { integrity: sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg== }
    engines: { node: '>= 0.4' }

  regenerator-runtime@0.11.1:
    resolution:
      { integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg== }

  regenerator-runtime@0.14.1:
    resolution:
      { integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw== }

  regexp.prototype.flags@1.5.2:
    resolution:
      { integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw== }
    engines: { node: '>= 0.4' }

  regexpp@2.0.1:
    resolution:
      { integrity: sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw== }
    engines: { node: '>=6.5.0' }

  repeat-string@1.6.1:
    resolution:
      { integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w== }
    engines: { node: '>=0.10' }

  requireindex@1.1.0:
    resolution:
      { integrity: sha512-LBnkqsDE7BZKvqylbmn7lTIVdpx4K/QCduRATpO5R+wtPmky/a8pN1bO2D6wXppn1497AJF9mNjqAXr6bdl9jg== }
    engines: { node: '>=0.10.5' }

  resize-observer-polyfill@1.5.1:
    resolution:
      { integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg== }

  resolve-from@4.0.0:
    resolution:
      { integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g== }
    engines: { node: '>=4' }

  resolve-pathname@3.0.0:
    resolution:
      { integrity: sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng== }

  resolve@1.22.8:
    resolution:
      { integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw== }
    hasBin: true

  resolve@2.0.0-next.5:
    resolution:
      { integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA== }
    hasBin: true

  restore-cursor@2.0.0:
    resolution:
      { integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q== }
    engines: { node: '>=4' }

  right-align@0.1.3:
    resolution:
      { integrity: sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg== }
    engines: { node: '>=0.10.0' }

  rimraf@2.6.3:
    resolution:
      { integrity: sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA== }
    hasBin: true

  rollup@0.25.8:
    resolution:
      { integrity: sha512-a2S4Bh3bgrdO4BhKr2E4nZkjTvrJ2m2bWjMTzVYtoqSCn0HnuxosXnaJUHrMEziOWr3CzL9GjilQQKcyCQpJoA== }
    hasBin: true

  run-async@2.4.1:
    resolution:
      { integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ== }
    engines: { node: '>=0.12.0' }

  rw@1.3.3:
    resolution:
      { integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ== }

  rxjs@6.6.7:
    resolution:
      { integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ== }
    engines: { npm: '>=2.0.0' }

  safari-14-idb-fix@1.0.6:
    resolution:
      { integrity: sha512-oTEQOdMwRX+uCtWCKT1nx2gAeSdpr8elg/2gcaKUH00SJU2xWESfkx11nmXwTRHy7xfQoj1o4TTQvdmuBosTnA== }

  safe-array-concat@1.1.2:
    resolution:
      { integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q== }
    engines: { node: '>=0.4' }

  safe-buffer@5.1.2:
    resolution:
      { integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g== }

  safe-regex-test@1.0.3:
    resolution:
      { integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw== }
    engines: { node: '>= 0.4' }

  safer-buffer@2.1.2:
    resolution:
      { integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg== }

  sass@1.77.1:
    resolution:
      { integrity: sha512-OMEyfirt9XEfyvocduUIOlUSkWOXS/LAt6oblR/ISXCTukyavjex+zQNm51pPCOiFKY1QpWvEH1EeCkgyV3I6w== }
    engines: { node: '>=14.0.0' }
    hasBin: true

  sax@1.3.0:
    resolution:
      { integrity: sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA== }

  scheduler@0.19.1:
    resolution:
      { integrity: sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA== }

  scheduler@0.23.2:
    resolution:
      { integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ== }

  screenfull@5.2.0:
    resolution:
      { integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA== }
    engines: { node: '>=0.10.0' }

  sdk-base@2.0.1:
    resolution:
      { integrity: sha512-eeG26wRwhtwYuKGCDM3LixCaxY27Pa/5lK4rLKhQa7HBjJ3U3Y+f81MMZQRsDw/8SC2Dao/83yJTXJ8aULuN8Q== }

  semver@5.7.2:
    resolution:
      { integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g== }
    hasBin: true

  semver@6.3.1:
    resolution:
      { integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA== }
    hasBin: true

  set-function-length@1.2.2:
    resolution:
      { integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg== }
    engines: { node: '>= 0.4' }

  set-function-name@2.0.2:
    resolution:
      { integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ== }
    engines: { node: '>= 0.4' }

  shallow-element-equals@1.0.1:
    resolution:
      { integrity: sha512-TwyvU5ZIISuZAmX7juTupVggTW9avkp+Swz0amKicADbQrnhP5kAPkPbL8gKSFv9QkkzhTg2u3Se6TjGhn1xlQ== }

  shallowequal@1.1.0:
    resolution:
      { integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ== }

  shebang-command@1.2.0:
    resolution:
      { integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg== }
    engines: { node: '>=0.10.0' }

  shebang-regex@1.0.0:
    resolution:
      { integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ== }
    engines: { node: '>=0.10.0' }

  side-channel@1.0.6:
    resolution:
      { integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA== }
    engines: { node: '>= 0.4' }

  signal-exit@3.0.7:
    resolution:
      { integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ== }

  size-sensor@1.0.2:
    resolution:
      { integrity: sha512-2NCmWxY7A9pYKGXNBfteo4hy14gWu47rg5692peVMst6lQLPKrVjhY+UTEsPI5ceFRJSl3gVgMYaUi/hKuaiKw== }

  slice-ansi@2.1.0:
    resolution:
      { integrity: sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ== }
    engines: { node: '>=6' }

  source-map-js@1.2.0:
    resolution:
      { integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg== }
    engines: { node: '>=0.10.0' }

  source-map-support@0.3.3:
    resolution:
      { integrity: sha512-9O4+y9n64RewmFoKUZ/5Tx9IHIcXM6Q+RTSw6ehnqybUz4a7iwR3Eaw80uLtqqQ5D0C+5H03D4KKGo9PdP33Gg== }

  source-map@0.1.32:
    resolution:
      { integrity: sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ== }
    engines: { node: '>=0.8.0' }

  source-map@0.5.7:
    resolution:
      { integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ== }
    engines: { node: '>=0.10.0' }

  spawn-sync@1.0.15:
    resolution:
      { integrity: sha512-9DWBgrgYZzNghseho0JOuh+5fg9u6QWhAWa51QC7+U5rCheZ/j1DrEZnyE0RBBRqZ9uEXGPgSSM0nky6burpVw== }

  split-on-first@1.1.0:
    resolution:
      { integrity: sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw== }
    engines: { node: '>=6' }

  sprintf-js@1.0.3:
    resolution:
      { integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g== }

  ssr-window@3.0.0:
    resolution:
      { integrity: sha512-q+8UfWDg9Itrg0yWK7oe5p/XRCJpJF9OBtXfOPgSJl+u3Xd5KI328RUEvUqSMVM9CiQUEf1QdBzJMkYGErj9QA== }

  statuses@1.5.0:
    resolution:
      { integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA== }
    engines: { node: '>= 0.6' }

  stream-http@2.8.2:
    resolution:
      { integrity: sha512-QllfrBhqF1DPcz46WxKTs6Mz1Bpc+8Qm6vbqOpVav5odAXwbyzwnEczoWqtxrsmlO+cJqtPrp/8gWKWjaKLLlA== }

  stream-wormhole@1.1.0:
    resolution:
      { integrity: sha512-gHFfL3px0Kctd6Po0M8TzEvt3De/xu6cnRrjlfYNhwbhLPLwigI2t1nc6jrzNuaYg5C4YF78PPFuQPzRiqn9ew== }
    engines: { node: '>=4.0.0' }

  strict-uri-encode@2.0.0:
    resolution:
      { integrity: sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ== }
    engines: { node: '>=4' }

  string-width@2.1.1:
    resolution:
      { integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw== }
    engines: { node: '>=4' }

  string-width@3.1.0:
    resolution:
      { integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w== }
    engines: { node: '>=6' }

  string.prototype.matchall@4.0.11:
    resolution:
      { integrity: sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg== }
    engines: { node: '>= 0.4' }

  string.prototype.trim@1.2.9:
    resolution:
      { integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw== }
    engines: { node: '>= 0.4' }

  string.prototype.trimend@1.0.8:
    resolution:
      { integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ== }

  string.prototype.trimstart@1.0.8:
    resolution:
      { integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg== }
    engines: { node: '>= 0.4' }

  string_decoder@1.1.1:
    resolution:
      { integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg== }

  strip-ansi@3.0.1:
    resolution:
      { integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg== }
    engines: { node: '>=0.10.0' }

  strip-ansi@4.0.0:
    resolution:
      { integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow== }
    engines: { node: '>=4' }

  strip-ansi@5.2.0:
    resolution:
      { integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA== }
    engines: { node: '>=6' }

  strip-bom@3.0.0:
    resolution:
      { integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA== }
    engines: { node: '>=4' }

  strip-eof@1.0.0:
    resolution:
      { integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q== }
    engines: { node: '>=0.10.0' }

  strip-json-comments@2.0.1:
    resolution:
      { integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ== }
    engines: { node: '>=0.10.0' }

  style-equal@1.0.0:
    resolution:
      { integrity: sha512-gf20kfwh7eXsgPcwvYqViCBHr+GXIlpXOZR1wQftNH4/ee2P/yolWUVA/MdMdmMp+0BMfvaMKSIR1DQlY64Btw== }

  style-unit@2.0.1:
    resolution:
      { integrity: sha512-1OHU+0lWHrK22W3DDfLKFl5cOIwTxghbVRgtzgue+/9m5dqsYQhVBObQupMdtN6FIhpM375l18h8nLqPKgHfPQ== }

  style-unit@3.0.5:
    resolution:
      { integrity: sha512-xL+kev1W1dPthdhpQqZs9Qk1zenQiHKyy9oy2/VasW4z6wi7m7qQvMe67foPsr99JSs0115X0TCN1ch1n0XqSw== }

  styled-components@5.3.11:
    resolution:
      { integrity: sha512-uuzIIfnVkagcVHv9nE0VPlHPSCmXIUGKfJ42LNjxCCTDTL5sgnJ8Z7GZBq0EnLYGln77tPpEpExt2+qa+cZqSw== }
    engines: { node: '>=10' }
    peerDependencies:
      react: '>= 16.8.0'
      react-dom: '>= 16.8.0'
      react-is: '>= 16.8.0'

  supports-color@2.0.0:
    resolution:
      { integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g== }
    engines: { node: '>=0.8.0' }

  supports-color@5.5.0:
    resolution:
      { integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow== }
    engines: { node: '>=4' }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      { integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w== }
    engines: { node: '>= 0.4' }

  swiper@6.5.0:
    resolution:
      { integrity: sha512-cSx1SpfgrHlgwku++3Ce3cjPBpXgB7P+bGik5S3+F+j6ID0NUeV6qtmedFdr3C8jXR/W+TJPVNIT9fH/cwVAiA== }
    engines: { node: '>= 4.7.0' }

  table@5.4.6:
    resolution:
      { integrity: sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug== }
    engines: { node: '>=6.0.0' }

  tape@4.17.0:
    resolution:
      { integrity: sha512-KCuXjYxCZ3ru40dmND+oCLsXyuA8hoseu2SS404Px5ouyS0A99v8X/mdiLqsR5MTAyamMBN7PRwt2Dv3+xGIxw== }
    hasBin: true

  text-segmentation@1.0.3:
    resolution:
      { integrity: sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw== }

  text-table@0.2.0:
    resolution:
      { integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw== }

  thenify-all@1.6.0:
    resolution:
      { integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA== }
    engines: { node: '>=0.8' }

  thenify@3.3.1:
    resolution:
      { integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw== }

  through@2.3.8:
    resolution:
      { integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg== }

  tiny-invariant@1.3.3:
    resolution:
      { integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg== }

  tiny-warning@1.0.3:
    resolution:
      { integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA== }

  tmp@0.0.33:
    resolution:
      { integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw== }
    engines: { node: '>=0.6.0' }

  to-arraybuffer@1.0.1:
    resolution:
      { integrity: sha512-okFlQcoGTi4LQBG/PgSYblw9VOyptsz2KJZqc6qtgGdes8VktzUQkj4BI2blit072iS8VODNcMA+tvnS9dnuMA== }

  to-fast-properties@2.0.0:
    resolution:
      { integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog== }
    engines: { node: '>=4' }

  to-regex-range@5.0.1:
    resolution:
      { integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ== }
    engines: { node: '>=8.0' }

  trim-right@1.0.1:
    resolution:
      { integrity: sha512-WZGXGstmCWgeevgTL54hrCuw1dyMQIzWy7ZfqRJfSmJZBwklI15egmQytFP6bPidmw3M8d5yEowl1niq4vmqZw== }
    engines: { node: '>=0.10.0' }

  tsconfig-paths@3.15.0:
    resolution:
      { integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg== }

  tslib@1.14.1:
    resolution:
      { integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg== }

  tslib@2.6.2:
    resolution:
      { integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q== }

  type-check@0.3.2:
    resolution:
      { integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg== }
    engines: { node: '>= 0.8.0' }

  typed-array-buffer@1.0.2:
    resolution:
      { integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ== }
    engines: { node: '>= 0.4' }

  typed-array-byte-length@1.0.1:
    resolution:
      { integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw== }
    engines: { node: '>= 0.4' }

  typed-array-byte-offset@1.0.2:
    resolution:
      { integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA== }
    engines: { node: '>= 0.4' }

  typed-array-length@1.0.6:
    resolution:
      { integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g== }
    engines: { node: '>= 0.4' }

  typedarray@0.0.6:
    resolution:
      { integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA== }

  typescript@5.4.5:
    resolution:
      { integrity: sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ== }
    engines: { node: '>=14.17' }
    hasBin: true

  uglify-js@2.8.29:
    resolution:
      { integrity: sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w== }
    engines: { node: '>=0.8.0' }
    hasBin: true

  uglify-to-browserify@1.0.2:
    resolution:
      { integrity: sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q== }

  unbox-primitive@1.0.2:
    resolution:
      { integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw== }

  unescape@1.0.1:
    resolution:
      { integrity: sha512-O0+af1Gs50lyH1nUu3ZyYS1cRh01Q/kUKatTOkSs7jukXE6/NebucDVxyiDsA9AQ4JC1V1jUH9EO8JX2nMDgGQ== }
    engines: { node: '>=0.10.0' }

  universal-canvas-context@1.0.0:
    resolution:
      { integrity: sha512-ZjdoQyp7sDNB6o1xj93ulD2u1KYjEmMqZMGvIABREjREFlix70O4BTWFrADpMRI3uW6A2N7L45y4xksKqaIvwQ== }
    engines: { npm: '>=3.0.0' }

  universal-choose-image@1.3.0:
    resolution:
      { integrity: sha512-j7L7Qk4X4rvBb8nsG5cqYPMahj1+/jkGsPUN1+1hvbX+H1aOs4gYcbnYCnQvmIZv1r4ghggvoS8Wgh95RcgfAA== }
    engines: { npm: '>=3.0.0' }
    peerDependencies:
      rax: ^1.1.0

  universal-device@1.0.3:
    resolution:
      { integrity: sha512-27VnA0IXJ70JRLwxtmCYRxXJSe+r6KwgiPu0dx9e+WD3Y0Gn5nKbK083KG5FK1Nbtxjyl0kRgvfG6MpK3YKXrg== }
    engines: { npm: '>=3.0.0' }

  universal-device@2.3.1:
    resolution:
      { integrity: sha512-Z7aMODPW3CdhU4IicZ41l1PUZJLxENtEBZsks6fOKD4pyRkip1Z5EFdaDv/NdC35ccs7uR9SbkVF6QL3qwPWsw== }
    engines: { npm: '>=3.0.0' }

  universal-element@0.0.6:
    resolution:
      { integrity: sha512-J1olYldUlj35w4pBt1LljiRuRjohATGHCIiiOHOepXtELB8zESOdYDlrFtXfxGtb6TUZm8oAaOPbTwhb+6A4BA== }
    engines: { npm: '>=3.0.0' }

  universal-env@0.6.6:
    resolution:
      { integrity: sha512-CqBdTKFStTEV7wETHjWm7CDIbSdUxIlN3zQ5xraTG6Wb1XEmBgyW1pu8lJW0buRXjACgjrD+pr97akumbQ0Y6A== }

  universal-env@1.0.7:
    resolution:
      { integrity: sha512-N7n9nZSpVSKwjPDO0QsN+3/5pPPum9u5i7Km3xxUi0ykA8UD9nKt8foMYdVFB8HIRXx/Sh7HVu3pC5CJX/t9Tw== }
    engines: { npm: '>=3.0.0' }

  universal-env@2.0.0:
    resolution:
      { integrity: sha512-jfPJvPXFdhJHsDhuCHj3Njc3nxF+dmj6LeqKE9R41EdKKOJ1d5GGpFu3DrT+Ff+pxS9jsnbtj7BZYFAcLlxdPg== }
    engines: { npm: '>=3.0.0' }

  universal-env@3.3.3:
    resolution:
      { integrity: sha512-4ZyITvWhtcurCEA66Cb7jcd4zpEiAAo91wSwbEscbiu033pIsC2yjgT8LYyasFgsst6jZHD1gtVoSyYcL8oH1Q== }
    engines: { npm: '>=3.0.0' }

  universal-panresponder@0.6.5:
    resolution:
      { integrity: sha512-7N9xSPgILxBr12krtyTl2KjN7wWxirtdH2/NsQj234KHrHt8yQ8hIgi6sjW4eyP3/5QtAn2JWwPSYdFmgHvg4w== }

  universal-transition@1.1.1:
    resolution:
      { integrity: sha512-TeYwWDhoYSYeGwX2L80gAQx7wByGvQ1WsPxqp+c6yYzqrc6BUuqpohtWY5Gh4ZPo0nToSNeadhly9sjeGLlV6Q== }
    engines: { npm: '>=3.0.0' }

  universal-unit-tool@1.0.0:
    resolution:
      { integrity: sha512-YTKN4pUqgAQqP5duZQSTxv2zswkUdZ4z3KtRgpXOxlo3huJm7xbiwhxeX8RM675Tjfo4entn8yQHclFJy9iaQQ== }

  update-browserslist-db@1.0.16:
    resolution:
      { integrity: sha512-KVbTxlBYlckhF5wgfyZXTWnMn7MMZjMu9XG8bPlliUOP9ThaF4QnhP8qrjrH7DRzHfSk0oQv1wToW+iA5GajEQ== }
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  upper-case@2.0.2:
    resolution:
      { integrity: sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg== }

  uri-js@4.4.1:
    resolution:
      { integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg== }

  url@0.11.3:
    resolution:
      { integrity: sha512-6hxOLGfZASQK/cijlZnZJTq8OXAkt/3YGfQX45vvMYXpZoo8NdWZcY73K108Jf759lS1Bv/8wXnHDTSz17dSRw== }

  urlencode@1.1.0:
    resolution:
      { integrity: sha512-OOAOh9owHXr/rCN1tteSnYwIvsrGHamSz0hafMhmQa7RcS4+Ets6/2iVClVGjt9jkDW84UqoMw/Gmpc7QolX6A== }

  urllib@2.41.0:
    resolution:
      { integrity: sha512-pNXdxEv52L67jahLT+/7QE+Fup1y2Gc6EdmrAhQ6OpQIC2rl14oWwv9hvk1GXOZqEnJNwRXHABuwgPOs1CtL7g== }
    engines: { node: '>= 0.10.0' }
    peerDependencies:
      proxy-agent: ^5.0.0
    peerDependenciesMeta:
      proxy-agent:
        optional: true

  util-deprecate@1.0.2:
    resolution:
      { integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw== }

  utility@1.18.0:
    resolution:
      { integrity: sha512-PYxZDA+6QtvRvm//++aGdmKG/cI07jNwbROz0Ql+VzFV1+Z0Dy55NI4zZ7RHc9KKpBePNFwoErqIuqQv/cjiTA== }
    engines: { node: '>= 0.12.0' }

  utrie@1.0.2:
    resolution:
      { integrity: sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw== }

  value-equal@1.0.1:
    resolution:
      { integrity: sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw== }

  warning@4.0.3:
    resolution:
      { integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w== }

  which-boxed-primitive@1.0.2:
    resolution:
      { integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg== }

  which-builtin-type@1.1.3:
    resolution:
      { integrity: sha512-YmjsSMDBYsM1CaFiayOVT06+KJeXf0o5M/CAd4o1lTadFAtacTUM49zoYxr/oroopFDfhvN6iEcBxUyc3gvKmw== }
    engines: { node: '>= 0.4' }

  which-collection@1.0.2:
    resolution:
      { integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw== }
    engines: { node: '>= 0.4' }

  which-typed-array@1.1.15:
    resolution:
      { integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA== }
    engines: { node: '>= 0.4' }

  which@1.2.14:
    resolution:
      { integrity: sha512-16uPglFkRPzgiUXYMi1Jf8Z5EzN1iB4V0ZtMXcHZnwsBtQhhHeCqoWw7tsUY42hJGNDWtUsVLTjakIa5BgAxCw== }
    hasBin: true

  which@1.3.1:
    resolution:
      { integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ== }
    hasBin: true

  win-release@1.1.1:
    resolution:
      { integrity: sha512-iCRnKVvGxOQdsKhcQId2PXV1vV3J/sDPXKA4Oe9+Eti2nb2ESEsYHRYls/UjoUW3bIc5ZDO8dTH50A/5iVN+bw== }
    engines: { node: '>=0.10.0' }

  window-size@0.1.0:
    resolution:
      { integrity: sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg== }
    engines: { node: '>= 0.8.0' }

  word-wrap@1.2.5:
    resolution:
      { integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA== }
    engines: { node: '>=0.10.0' }

  wordwrap@0.0.2:
    resolution:
      { integrity: sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q== }
    engines: { node: '>=0.4.0' }

  wrappy@1.0.2:
    resolution:
      { integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ== }

  write@1.0.3:
    resolution:
      { integrity: sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig== }
    engines: { node: '>=4' }

  xml2js@0.6.2:
    resolution:
      { integrity: sha512-T4rieHaC1EXcES0Kxxj4JWgaUQHDk+qwHcYOCFHfiwKz7tOVPLq7Hjq9dM1WCMhylqMEfP7hMcOIChvotiZegA== }
    engines: { node: '>=4.0.0' }

  xmlbuilder@11.0.1:
    resolution:
      { integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA== }
    engines: { node: '>=4.0' }

  xtend@4.0.2:
    resolution:
      { integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ== }
    engines: { node: '>=0.4' }

  yallist@2.1.2:
    resolution:
      { integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A== }

  yallist@3.1.1:
    resolution:
      { integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g== }

  yargs@3.10.0:
    resolution:
      { integrity: sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A== }

snapshots:
  '@ahooksjs/use-request@2.8.15(react@16.14.0)':
    dependencies:
      lodash.debounce: 4.0.8
      lodash.throttle: 4.1.1
      react: 16.14.0

  '@ahooksjs/use-url-state@2.5.12(react-router@5.3.4(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@types/query-string': 6.3.0
      query-string: 6.14.1
      react: 16.14.0
      react-router: 5.3.4(react@16.14.0)

  '@ali/eslint-plugin-comments@1.0.0':
    dependencies:
      requireindex: 1.1.0

  '@ali/filebroker@0.2.6(@ali/trace-sdk@1.3.25)':
    dependencies:
      '@ali/trace-sdk': 1.3.25

  '@ali/form-log@0.1.10':
    dependencies:
      lodash: 4.17.21
      universal-env: 3.3.3
      urlencode: 1.1.0

  '@ali/gcom-lzd-cookie@1.3.0':
    dependencies:
      '@swc/helpers': 0.5.11

  '@ali/gcom-lzd-env@1.2.7':
    dependencies:
      '@ali/gcom-lzd-cookie': 1.3.0
      '@ali/gcom-lzd-sites': 1.5.0
      '@swc/helpers': 0.5.11

  '@ali/gcom-lzd-resize@1.3.1':
    dependencies:
      '@ali/gcom-lzd-env': 1.2.7

  '@ali/gcom-lzd-sites@1.5.0':
    dependencies:
      '@swc/helpers': 0.5.11

  '@ali/global-locale@1.0.5': {}

  '@ali/global-string-format@1.0.7':
    dependencies:
      intl-messageformat: 2.2.0
      universal-env: 1.0.7

  '@ali/i18n-utils@1.1.5':
    dependencies:
      lodash: 4.17.21
      prop-types: 15.8.1
      react-string-replace: 1.1.1

  '@ali/locale-common-asc@1.2.1':
    dependencies:
      '@alife/lazada-i18n': 2.0.2

  '@ali/lzd-h5-resize@2.1.30': {}

  ? '@ali/merlion-ui-v1@1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)'
  : dependencies:
      '@ali/filebroker': 0.2.6(@ali/trace-sdk@1.3.25)
      '@ali/gcom-lzd-resize': 1.3.1
      '@ali/i18n-utils': 1.1.5
      '@alifd/next': 1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/cndzk-entrance-api': 0.2.6(moment@2.30.1)(react@16.14.0)
      '@alife/lazada-asc-theme': 3.26.9(@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/workstation-upload': 2.1.0(debug@4.3.4)
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      '@babel/runtime': 7.24.5
      ahooks: 3.7.11(react@16.14.0)
      classnames: 2.5.1
      debug: 4.3.4(supports-color@5.5.0)
      dompurify: 2.5.3
      eventemitter3: 4.0.7
      idb-keyval: 5.1.5
      imask: 6.6.3
      invariant: 2.2.4
      lodash: 4.17.21
      nanoid: 4.0.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-error-boundary: 3.1.4(react@16.14.0)
      react-imask: 6.6.3(react@16.14.0)
      react-string-replace: 1.1.1
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - moment
      - proxy-agent
      - supports-color

  ? '@ali/merlion-ui@1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)'
  : dependencies:
      '@ali/filebroker': 0.2.6(@ali/trace-sdk@1.3.25)
      '@ali/i18n-utils': 1.1.5
      '@ali/lzd-h5-resize': 2.1.30
      '@ali/merlion-ui-v1': 1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alifd/next': 1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/cndzk-entrance-api': 0.2.6(moment@2.30.1)(react@16.14.0)
      '@alife/lazada-asc-theme': 3.26.9(@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/workstation-upload': 2.1.0(debug@4.3.4)
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      '@babel/runtime': 7.24.5
      ahooks: 3.7.11(react@16.14.0)
      classnames: 2.5.1
      debug: 4.3.4(supports-color@5.5.0)
      dompurify: 2.5.3
      eventemitter3: 4.0.7
      idb-keyval: 5.1.5
      imask: 6.6.3
      invariant: 2.2.4
      lodash: 4.17.21
      nanoid: 4.0.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-error-boundary: 3.1.4(react@16.14.0)
      react-imask: 6.6.3(react@16.14.0)
      react-string-replace: 1.1.1
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - moment
      - proxy-agent
      - supports-color

  '@ali/retcode-util@1.0.5': {}

  '@ali/trace-core@1.3.25':
    dependencies:
      '@ali/trace-log-map': 1.3.25
      '@ali/trace-util': 1.3.25

  '@ali/trace-log-map@1.3.25': {}

  '@ali/trace-plugin-error@1.3.25':
    dependencies:
      '@ali/trace-util': 1.3.25

  '@ali/trace-plugin-form@1.3.25':
    dependencies:
      '@ali/form-log': 0.1.10
      '@ali/trace-util': 1.3.25

  '@ali/trace-sdk@1.3.25':
    dependencies:
      '@ali/trace-core': 1.3.25
      '@ali/trace-plugin-error': 1.3.25
      '@ali/trace-util': 1.3.25
      '@uni/env': 1.1.1

  '@ali/trace-util@1.3.25': {}

  '@alifd/babel-runtime-jsx-style-transform@1.0.0': {}

  '@alifd/field@1.7.0':
    dependencies:
      '@alifd/validate': 1.2.3
      prop-types: 15.8.1

  '@alifd/field@2.0.0':
    dependencies:
      '@alifd/validate': 2.0.3
      tslib: 2.6.2

  '@alifd/meet-react-component-one@1.3.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@gcanvas/core': 1.0.0
      classnames: 2.5.1
      omit.js: 2.0.2
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      style-unit: 2.0.1
      swiper: 6.5.0
      tslib: 2.6.2
      universal-env: 3.3.3
      universal-panresponder: 0.6.5
      universal-transition: 1.1.1

  '@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)':
    dependencies:
      '@alifd/babel-runtime-jsx-style-transform': 1.0.0
      '@alifd/field': 1.7.0
      '@alifd/meet-react-component-one': 1.3.2(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@uni/clipboard': 1.0.9
      '@uni/env': 1.1.1
      '@uni/file': 1.1.1
      '@uni/image': 1.1.3
      '@uni/navigate': 1.0.11
      '@uni/page-scroll-to': 1.0.0
      '@uni/vibrate': 1.0.1
      classnames: 2.2.6
      dayjs: 1.11.11
      driver-universal: 3.5.0
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      tslib: 2.6.2
      universal-canvas-context: 1.0.0
      universal-choose-image: 1.3.0(rax@1.2.3)
      universal-element: 0.0.6
    transitivePeerDependencies:
      - rax

  ? '@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/field': 1.7.0
      '@alifd/meet-react': 2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alifd/overlay': 0.3.5
      '@alifd/validate': 1.2.3
      '@types/react-transition-group': 4.4.10
      babel-runtime: 6.26.0
      big.js: 6.2.1
      classnames: 2.5.1
      dayjs: 1.11.11
      hoist-non-react-statics: 3.3.2
      lodash.clonedeep: 4.5.0
      moment: 2.30.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-lifecycles-compat: 3.0.4
      react-transition-group: 2.9.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      resize-observer-polyfill: 1.5.1
      shallow-element-equals: 1.0.1

  ? '@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/field': 2.0.0
      '@alifd/meet-react': 2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alifd/overlay': 0.3.5
      '@alifd/validate': 2.0.3
      '@types/react-transition-group': 4.4.10
      babel-runtime: 6.26.0
      big.js: 6.2.1
      classnames: 2.5.1
      dayjs: 1.11.11
      hoist-non-react-statics: 3.3.2
      lodash.clonedeep: 4.5.0
      moment: 2.30.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-lifecycles-compat: 3.0.4
      react-transition-group: 2.9.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      resize-observer-polyfill: 1.5.1
      shallow-element-equals: 1.0.1
      tslib: 2.6.2

  ? '@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)'
  : dependencies:
      '@alifd/field': 2.0.0
      '@alifd/meet-react': 2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alifd/overlay': 0.3.5
      '@alifd/validate': 2.0.3
      '@types/react-transition-group': 4.4.10
      babel-runtime: 6.26.0
      big.js: 6.2.1
      classnames: 2.5.1
      dayjs: 1.11.11
      hoist-non-react-statics: 3.3.2
      lodash.clonedeep: 4.5.0
      moment: 2.30.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-lifecycles-compat: 3.0.4
      react-transition-group: 2.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      resize-observer-polyfill: 1.5.1
      shallow-element-equals: 1.0.1
      tslib: 2.6.2

  '@alifd/overlay@0.3.5':
    dependencies:
      resize-observer-polyfill: 1.5.1

  '@alifd/validate@1.2.3': {}

  '@alifd/validate@2.0.3':
    dependencies:
      tslib: 2.6.2

  ? '@alife/asc-afeedback@0.0.6(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(typescript@5.4.5)'
  : dependencies:
      '@ali/merlion-ui': 1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/cco-general': 0.1.7
      '@alife/lazada-asc-theme': 3.26.9(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/lazada-i18n': 1.0.12
      '@alife/mcms_dada_common': 0.0.8
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      ahooks: 3.7.11(react@16.14.0)
      classnames: 2.5.1
      debug: 4.3.4(supports-color@5.5.0)
      html2canvas: 1.4.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-intl: 5.25.1(react@16.14.0)(typescript@5.4.5)
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - moment
      - proxy-agent
      - supports-color
      - typescript

  ? '@alife/asc-campaign-components@1.0.6(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/asc-components': 1.39.4(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alife/lazada-i18n': 1.0.12
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      ahooks: 2.10.14(react@16.14.0)
      lodash: 4.17.21
      moment: 2.30.1
      moment-timezone: 0.5.45
      qrcode.react: 1.0.1(react@16.14.0)
      react-display-name-export: 1.0.0
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - debug
      - proxy-agent
      - react
      - react-dom
      - supports-color

  ? '@alife/asc-components@1.39.4(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)'
  : dependencies:
      '@ali/filebroker': 0.2.6(@ali/trace-sdk@1.3.25)
      '@ali/lzd-h5-resize': 2.1.30
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/cndzk-entrance-api': 0.2.6(moment@2.30.1)(react@16.14.0)
      '@alife/lazada-asc-theme': 3.26.9(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      ahooks: 3.7.11(react@16.14.0)
      classnames: 2.5.1
      dompurify: 2.5.3
      eventemitter3: 4.0.7
      idb-keyval: 5.1.5
      invariant: 2.2.4
      lodash: 4.17.21
      nanoid: 4.0.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-error-boundary: 3.1.4(react@16.14.0)
      react-string-replace: 1.1.1
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - debug
      - moment
      - proxy-agent
      - supports-color

  ? '@alife/asc-logistics-components@1.1.14(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(typescript@5.4.5)'
  : dependencies:
      '@ali/locale-common-asc': 1.2.1
      '@ali/merlion-ui': 1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@alife/asc-afeedback': 0.0.6(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(typescript@5.4.5)
      '@alife/lazada-i18n': 2.0.2
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      '@amap/amap-jsapi-loader': 1.0.1
      ahooks: 3.7.11(react@18.3.1)
      classnames: 2.5.1
      lodash: 4.17.21
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - debug
      - moment
      - proxy-agent
      - supports-color
      - typescript

  '@alife/cco-general@0.1.7': {}

  '@alife/cndzk-entrance-api@0.2.6(moment@2.30.1)(react@16.14.0)':
    dependencies:
      axios: 0.19.2
      moment: 2.30.1
      react: 16.14.0
    transitivePeerDependencies:
      - supports-color

  ? '@alife/form-track-plugin@1.1.0(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(@babel/core@7.24.5)(@formily/core@2.3.1)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)'
  : dependencies:
      '@ali/trace-plugin-form': 1.3.25
      '@ali/trace-sdk': 1.3.25
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@formily/core': 2.3.1
      styled-components: 5.3.11(@babel/core@7.24.5)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
    transitivePeerDependencies:
      - '@babel/core'
      - react
      - react-dom
      - react-is

  '@alife/intl-request@2.4.0(debug@4.3.4)':
    dependencies:
      axios: 1.6.8(debug@4.3.4)
      lodash: 4.17.21
    transitivePeerDependencies:
      - debug

  '@alife/intl-util@1.0.131':
    dependencies:
      '@ali/retcode-util': 1.0.5
      lodash.get: 4.4.2
      lodash.set: 4.3.2
      react-is: 16.13.1
      url: 0.11.3

  ? '@alife/lago-utils@1.0.2(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)'
  : dependencies:
      '@ali/merlion-ui': 1.48.1(@ali/trace-sdk@1.3.25)(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)
      '@alife/workstation-utils': 1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)
      ahooks: 3.7.11(react@16.14.0)
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - '@alifd/meet-react'
      - debug
      - moment
      - proxy-agent
      - supports-color

  ? '@alife/lazada-asc-theme@3.26.9(@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/next': 1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/theme-27318': 0.3.2(@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      moment: 2.30.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  ? '@alife/lazada-asc-theme@3.26.9(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@alife/theme-27318': 0.3.2(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      moment: 2.30.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  '@alife/lazada-i18n@1.0.12':
    dependencies:
      '@ali/global-string-format': 1.0.7

  '@alife/lazada-i18n@2.0.2':
    dependencies:
      lodash: 4.17.21

  '@alife/mcms_dada_common@0.0.8': {}

  ? '@alife/theme-27318@0.3.2(@alifd/next@1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/next': 1.26.37(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      moment: 2.30.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  ? '@alife/theme-27318@0.3.2(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)'
  : dependencies:
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      moment: 2.30.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  '@alife/workstation-upload@2.1.0(debug@4.3.4)':
    dependencies:
      ali-oss: 6.20.0
      axios: 1.6.8(debug@4.3.4)
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - debug
      - proxy-agent
      - supports-color

  '@alife/workstation-utils@1.14.2(@ali/trace-sdk@1.3.25)(debug@4.3.4)':
    dependencies:
      '@ali/filebroker': 0.2.6(@ali/trace-sdk@1.3.25)
      '@alife/intl-request': 2.4.0(debug@4.3.4)
      '@alife/intl-util': 1.0.131
      '@alife/workstation-upload': 2.1.0(debug@4.3.4)
      idb-keyval: 6.2.1
      lodash: 4.17.21
      react: 16.14.0
      react-string-replace: 1.1.1
    transitivePeerDependencies:
      - '@ali/trace-sdk'
      - debug
      - proxy-agent
      - supports-color

  '@amap/amap-jsapi-loader@1.0.1': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25

  '@antv/adjust@0.2.5':
    dependencies:
      '@antv/util': 2.0.17
      tslib: 1.14.1

  '@antv/attr@0.3.5':
    dependencies:
      '@antv/color-util': 2.0.6
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      tslib: 2.6.2

  '@antv/color-util@2.0.6':
    dependencies:
      '@antv/util': 2.0.17
      tslib: 2.6.2

  '@antv/component@0.8.35':
    dependencies:
      '@antv/color-util': 2.0.6
      '@antv/dom-util': 2.0.4
      '@antv/g-base': 0.5.16
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      fecha: 4.2.3
      tslib: 2.6.2

  '@antv/coord@0.3.1':
    dependencies:
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/util': 2.0.17
      tslib: 2.6.2

  '@antv/dom-util@2.0.4':
    dependencies:
      tslib: 2.6.2

  '@antv/event-emitter@0.1.3': {}

  '@antv/g-base@0.5.16':
    dependencies:
      '@antv/event-emitter': 0.1.3
      '@antv/g-math': 0.1.9
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      '@types/d3-timer': 2.0.3
      d3-ease: 1.0.7
      d3-interpolate: 3.0.1
      d3-timer: 1.0.10
      detect-browser: 5.3.0
      tslib: 2.6.2

  '@antv/g-canvas@0.5.17':
    dependencies:
      '@antv/g-base': 0.5.16
      '@antv/g-math': 0.1.9
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.6.2

  '@antv/g-math@0.1.9':
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3

  '@antv/g-svg@0.5.7':
    dependencies:
      '@antv/g-base': 0.5.16
      '@antv/g-math': 0.1.9
      '@antv/util': 2.0.17
      detect-browser: 5.3.0
      tslib: 2.6.2

  '@antv/g2@4.1.32':
    dependencies:
      '@antv/adjust': 0.2.5
      '@antv/attr': 0.3.5
      '@antv/color-util': 2.0.6
      '@antv/component': 0.8.35
      '@antv/coord': 0.3.1
      '@antv/dom-util': 2.0.4
      '@antv/event-emitter': 0.1.3
      '@antv/g-base': 0.5.16
      '@antv/g-canvas': 0.5.17
      '@antv/g-svg': 0.5.7
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      tslib: 2.6.2

  '@antv/g2plot@2.3.39':
    dependencies:
      '@antv/event-emitter': 0.1.3
      '@antv/g2': 4.1.32
      d3-hierarchy: 2.0.0
      d3-regression: 1.3.10
      fmin: 0.0.2
      pdfast: 0.2.0
      size-sensor: 1.0.2
      tslib: 2.6.2

  '@antv/matrix-util@3.0.4':
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.6.2

  '@antv/matrix-util@3.1.0-beta.3':
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.6.2

  '@antv/path-util@2.0.15':
    dependencies:
      '@antv/matrix-util': 3.0.4
      '@antv/util': 2.0.17
      tslib: 2.6.2

  '@antv/scale@0.3.18':
    dependencies:
      '@antv/util': 2.0.17
      fecha: 4.2.3
      tslib: 2.6.2

  '@antv/util@2.0.17':
    dependencies:
      csstype: 3.1.3
      tslib: 2.6.2

  '@babel/code-frame@7.0.0-beta.44':
    dependencies:
      '@babel/highlight': 7.0.0-beta.44

  '@babel/code-frame@7.24.2':
    dependencies:
      '@babel/highlight': 7.24.5
      picocolors: 1.0.1

  '@babel/compat-data@7.24.4': {}

  '@babel/core@7.24.5':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.5
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.24.5(@babel/core@7.24.5)
      '@babel/helpers': 7.24.5
      '@babel/parser': 7.24.5
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.5(supports-color@5.5.0)
      '@babel/types': 7.24.5
      convert-source-map: 2.0.0
      debug: 4.3.4(supports-color@5.5.0)
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.0.0-beta.44':
    dependencies:
      '@babel/types': 7.0.0-beta.44
      jsesc: 2.5.2
      lodash: 4.17.21
      source-map: 0.5.7
      trim-right: 1.0.1

  '@babel/generator@7.24.5':
    dependencies:
      '@babel/types': 7.24.5
      '@jridgewell/gen-mapping': 0.3.5
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.22.5':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/helper-compilation-targets@7.23.6':
    dependencies:
      '@babel/compat-data': 7.24.4
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.23.0
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.2(@babel/core@7.24.5)':
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.24.5
      debug: 4.3.4(supports-color@5.5.0)
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.22.20': {}

  '@babel/helper-function-name@7.0.0-beta.44':
    dependencies:
      '@babel/helper-get-function-arity': 7.0.0-beta.44
      '@babel/template': 7.0.0-beta.44
      '@babel/types': 7.0.0-beta.44

  '@babel/helper-function-name@7.23.0':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/types': 7.24.5

  '@babel/helper-get-function-arity@7.0.0-beta.44':
    dependencies:
      '@babel/types': 7.0.0-beta.44

  '@babel/helper-hoist-variables@7.22.5':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/helper-module-imports@7.24.3':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/helper-module-transforms@7.24.5(@babel/core@7.24.5)':
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-simple-access': 7.24.5
      '@babel/helper-split-export-declaration': 7.24.5
      '@babel/helper-validator-identifier': 7.24.5

  '@babel/helper-plugin-utils@7.24.5': {}

  '@babel/helper-simple-access@7.24.5':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/helper-split-export-declaration@7.0.0-beta.44':
    dependencies:
      '@babel/types': 7.0.0-beta.44

  '@babel/helper-split-export-declaration@7.24.5':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/helper-string-parser@7.24.1': {}

  '@babel/helper-validator-identifier@7.24.5': {}

  '@babel/helper-validator-option@7.23.5': {}

  '@babel/helpers@7.24.5':
    dependencies:
      '@babel/template': 7.24.0
      '@babel/traverse': 7.24.5(supports-color@5.5.0)
      '@babel/types': 7.24.5
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.0.0-beta.44':
    dependencies:
      chalk: 2.4.2
      esutils: 2.0.3
      js-tokens: 3.0.2

  '@babel/highlight@7.24.5':
    dependencies:
      '@babel/helper-validator-identifier': 7.24.5
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.1

  '@babel/parser@7.24.5':
    dependencies:
      '@babel/types': 7.24.5

  '@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.5)':
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-plugin-utils': 7.24.5

  '@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.5)':
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-module-transforms': 7.24.5(@babel/core@7.24.5)
      '@babel/helper-plugin-utils': 7.24.5
      '@babel/helper-simple-access': 7.24.5

  '@babel/plugin-transform-runtime@7.24.3(@babel/core@7.24.5)':
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-module-imports': 7.24.3
      '@babel/helper-plugin-utils': 7.24.5
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.24.5)
      babel-plugin-polyfill-corejs3: 0.10.4(@babel/core@7.24.5)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.24.5)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.24.5':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.0.0-beta.44':
    dependencies:
      '@babel/code-frame': 7.0.0-beta.44
      '@babel/types': 7.0.0-beta.44
      babylon: 7.0.0-beta.44
      lodash: 4.17.21

  '@babel/template@7.24.0':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/parser': 7.24.5
      '@babel/types': 7.24.5

  '@babel/traverse@7.0.0-beta.44':
    dependencies:
      '@babel/code-frame': 7.0.0-beta.44
      '@babel/generator': 7.0.0-beta.44
      '@babel/helper-function-name': 7.0.0-beta.44
      '@babel/helper-split-export-declaration': 7.0.0-beta.44
      '@babel/types': 7.0.0-beta.44
      babylon: 7.0.0-beta.44
      debug: 3.2.7
      globals: 11.12.0
      invariant: 2.2.4
      lodash: 4.17.21
    transitivePeerDependencies:
      - supports-color

  '@babel/traverse@7.24.5(supports-color@5.5.0)':
    dependencies:
      '@babel/code-frame': 7.24.2
      '@babel/generator': 7.24.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.24.5
      '@babel/parser': 7.24.5
      '@babel/types': 7.24.5
      debug: 4.3.4(supports-color@5.5.0)
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.0.0-beta.44':
    dependencies:
      esutils: 2.0.3
      lodash: 4.17.21
      to-fast-properties: 2.0.0

  '@babel/types@7.24.5':
    dependencies:
      '@babel/helper-string-parser': 7.24.1
      '@babel/helper-validator-identifier': 7.24.5
      to-fast-properties: 2.0.0

  '@emotion/is-prop-valid@1.2.2':
    dependencies:
      '@emotion/memoize': 0.8.1

  '@emotion/memoize@0.8.1': {}

  '@emotion/stylis@0.8.5': {}

  '@emotion/unitless@0.7.5': {}

  '@formatjs/ecma402-abstract@1.11.4':
    dependencies:
      '@formatjs/intl-localematcher': 0.2.25
      tslib: 2.6.2

  '@formatjs/fast-memoize@1.2.1':
    dependencies:
      tslib: 2.6.2

  '@formatjs/icu-messageformat-parser@2.1.0':
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/icu-skeleton-parser': 1.3.6
      tslib: 2.6.2

  '@formatjs/icu-skeleton-parser@1.3.6':
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      tslib: 2.6.2

  '@formatjs/intl-displaynames@5.4.3':
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/intl-localematcher': 0.2.25
      tslib: 2.6.2

  '@formatjs/intl-listformat@6.5.3':
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/intl-localematcher': 0.2.25
      tslib: 2.6.2

  '@formatjs/intl-localematcher@0.2.25':
    dependencies:
      tslib: 2.6.2

  '@formatjs/intl@2.2.1(typescript@5.4.5)':
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/fast-memoize': 1.2.1
      '@formatjs/icu-messageformat-parser': 2.1.0
      '@formatjs/intl-displaynames': 5.4.3
      '@formatjs/intl-listformat': 6.5.3
      intl-messageformat: 9.13.0
      tslib: 2.6.2
    optionalDependencies:
      typescript: 5.4.5

  '@formily/core@2.3.1':
    dependencies:
      '@formily/reactive': 2.3.1
      '@formily/shared': 2.3.1
      '@formily/validator': 2.3.1

  '@formily/grid@2.3.1(typescript@5.4.5)':
    dependencies:
      '@formily/reactive': 2.3.1
      '@juggle/resize-observer': 3.4.0
      typescript: 5.4.5

  '@formily/json-schema@2.3.1(typescript@5.4.5)':
    dependencies:
      '@formily/core': 2.3.1
      '@formily/reactive': 2.3.1
      '@formily/shared': 2.3.1
      typescript: 5.4.5

  ? '@formily/next@2.3.1(@alifd/next@1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(@types/react-dom@16.9.24)(@types/react@16.14.60)(prop-types@15.8.1)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)(typescript@5.4.5)'
  : dependencies:
      '@alifd/next': 1.27.11(@alifd/meet-react@2.9.9(rax@1.2.3)(react-dom@16.14.0(react@16.14.0))(react@16.14.0))(moment@2.30.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      '@formily/core': 2.3.1
      '@formily/grid': 2.3.1(typescript@5.4.5)
      '@formily/json-schema': 2.3.1(typescript@5.4.5)
      '@formily/react': 2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)(typescript@5.4.5)
      '@formily/reactive': 2.3.1
      '@formily/reactive-react': 2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      '@formily/shared': 2.3.1
      classnames: 2.5.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.3.1
      react-sortable-hoc: 1.11.0(prop-types@15.8.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0)
      react-sticky-box: 0.9.3(prop-types@15.8.1)(react@16.14.0)
    optionalDependencies:
      '@types/react': 16.14.60
      '@types/react-dom': 16.9.24
    transitivePeerDependencies:
      - prop-types
      - typescript

  '@formily/path@2.3.1': {}

  ? '@formily/react@2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)(typescript@5.4.5)'
  : dependencies:
      '@formily/core': 2.3.1
      '@formily/json-schema': 2.3.1(typescript@5.4.5)
      '@formily/reactive': 2.3.1
      '@formily/reactive-react': 2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
      '@formily/shared': 2.3.1
      '@formily/validator': 2.3.1
      hoist-non-react-statics: 3.3.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.3.1
    optionalDependencies:
      '@types/react': 16.14.60
      '@types/react-dom': 16.9.24
    transitivePeerDependencies:
      - typescript

  ? '@formily/reactive-react@2.3.1(@types/react-dom@16.9.24)(@types/react@16.14.60)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)'
  : dependencies:
      '@formily/reactive': 2.3.1
      hoist-non-react-statics: 3.3.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.3.1
    optionalDependencies:
      '@types/react': 16.14.60
      '@types/react-dom': 16.9.24

  '@formily/reactive@2.3.1': {}

  '@formily/shared@2.3.1':
    dependencies:
      '@formily/path': 2.3.1
      camel-case: 4.1.2
      lower-case: 2.0.2
      no-case: 3.0.4
      param-case: 3.0.4
      pascal-case: 3.1.2
      upper-case: 2.0.2

  '@formily/validator@2.3.1':
    dependencies:
      '@formily/shared': 2.3.1

  '@gcanvas/core@1.0.0': {}

  '@jridgewell/gen-mapping@0.3.5':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.4.15

  '@juggle/resize-observer@3.4.0': {}

  '@ljharb/resumer@0.0.1':
    dependencies:
      '@ljharb/through': 2.3.13

  '@ljharb/through@2.3.13':
    dependencies:
      call-bind: 1.0.7

  '@swc/helpers@0.5.11':
    dependencies:
      tslib: 2.6.2

  '@types/d3-timer@2.0.3': {}

  '@types/hoist-non-react-statics@3.3.5':
    dependencies:
      '@types/react': 16.14.60
      hoist-non-react-statics: 3.3.2

  '@types/js-cookie@2.2.7': {}

  '@types/json5@0.0.29': {}

  '@types/lodash-es@4.17.12':
    dependencies:
      '@types/lodash': 4.17.1

  '@types/lodash@4.17.1': {}

  '@types/prop-types@15.7.12': {}

  '@types/query-string@6.3.0':
    dependencies:
      query-string: 6.14.1

  '@types/react-dom@16.9.24':
    dependencies:
      '@types/react': 16.14.60

  '@types/react-transition-group@4.4.10':
    dependencies:
      '@types/react': 16.14.60

  '@types/react@16.14.60':
    dependencies:
      '@types/prop-types': 15.7.12
      '@types/scheduler': 0.16.8
      csstype: 3.1.3

  '@types/scheduler@0.16.8': {}

  '@uni/action-sheet@1.0.8':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/clipboard@1.0.9':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/env@1.1.1': {}

  '@uni/file@1.1.1':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/image@1.1.3':
    dependencies:
      '@uni/video': 1.0.8

  '@uni/navigate@1.0.11':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/page-scroll-to@1.0.0':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/vibrate@1.0.1':
    dependencies:
      '@uni/env': 1.1.1

  '@uni/video@1.0.8':
    dependencies:
      '@uni/action-sheet': 1.0.8

  '@vmojs/decorator@1.0.17':
    dependencies:
      lodash: 4.17.21

  acorn-jsx@5.3.2(acorn@6.4.2):
    dependencies:
      acorn: 6.4.2

  acorn@6.4.2: {}

  address@1.2.2: {}

  agentkeepalive@3.5.3:
    dependencies:
      humanize-ms: 1.2.1

  ahooks@2.10.14(react@16.14.0):
    dependencies:
      '@ahooksjs/use-request': 2.8.15(react@16.14.0)
      '@types/js-cookie': 2.2.7
      dayjs: 1.11.11
      intersection-observer: 0.7.0
      js-cookie: 2.2.1
      lodash.debounce: 4.0.8
      lodash.isequal: 4.5.0
      lodash.throttle: 4.1.1
      react: 16.14.0
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0

  ahooks@3.7.11(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      dayjs: 1.11.11
      intersection-observer: 0.12.2
      js-cookie: 2.2.1
      lodash: 4.17.21
      react: 16.14.0
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.6.2

  ahooks@3.7.11(react@18.3.1):
    dependencies:
      '@babel/runtime': 7.24.5
      dayjs: 1.11.11
      intersection-observer: 0.12.2
      js-cookie: 2.2.1
      lodash: 4.17.21
      react: 18.3.1
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.6.2

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ali-oss@6.20.0:
    dependencies:
      address: 1.2.2
      agentkeepalive: 3.5.3
      bowser: 1.9.4
      copy-to: 2.0.1
      dateformat: 2.2.0
      debug: 4.3.4(supports-color@5.5.0)
      destroy: 1.2.0
      end-or-error: 1.0.1
      get-ready: 1.0.0
      humanize-ms: 1.2.1
      is-type-of: 1.4.0
      js-base64: 2.6.4
      jstoxml: 2.2.9
      lodash: 4.17.21
      merge-descriptors: 1.0.3
      mime: 2.6.0
      platform: 1.3.6
      pump: 3.0.0
      qs: 6.12.1
      sdk-base: 2.0.1
      stream-http: 2.8.2
      stream-wormhole: 1.1.0
      urllib: 2.41.0
      utility: 1.18.0
      xml2js: 0.6.2
    transitivePeerDependencies:
      - proxy-agent
      - supports-color

  align-text@0.1.4:
    dependencies:
      kind-of: 3.2.2
      longest: 1.0.1
      repeat-string: 1.6.1

  amdefine@1.0.1: {}

  ansi-escapes@3.2.0: {}

  ansi-regex@2.1.1: {}

  ansi-regex@3.0.1: {}

  ansi-regex@4.1.1: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-differ@2.1.0: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      is-string: 1.0.7

  array-union@1.0.2:
    dependencies:
      array-uniq: 1.0.3

  array-uniq@1.0.3: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.toreversed@1.1.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  arrify@1.0.1: {}

  astral-regex@1.0.0: {}

  asynckit@0.4.0: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axios@0.19.2:
    dependencies:
      follow-redirects: 1.5.10
    transitivePeerDependencies:
      - supports-color

  axios@1.6.8(debug@4.3.4):
    dependencies:
      follow-redirects: 1.15.6(debug@4.3.4)
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-eslint@8.2.6:
    dependencies:
      '@babel/code-frame': 7.0.0-beta.44
      '@babel/traverse': 7.0.0-beta.44
      '@babel/types': 7.0.0-beta.44
      babylon: 7.0.0-beta.44
      eslint-scope: 3.7.1
      eslint-visitor-keys: 1.3.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs2@0.4.11(@babel/core@7.24.5):
    dependencies:
      '@babel/compat-data': 7.24.4
      '@babel/core': 7.24.5
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.24.5)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.5):
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.24.5)
      core-js-compat: 3.37.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.2(@babel/core@7.24.5):
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-define-polyfill-provider': 0.6.2(@babel/core@7.24.5)
    transitivePeerDependencies:
      - supports-color

  ? babel-plugin-styled-components@2.1.4(@babel/core@7.24.5)(styled-components@5.3.11(@babel/core@7.24.5)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
  : dependencies:
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-module-imports': 7.24.3
      '@babel/plugin-syntax-jsx': 7.24.1(@babel/core@7.24.5)
      lodash: 4.17.21
      picomatch: 2.3.1
      styled-components: 5.3.11(@babel/core@7.24.5)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0)
    transitivePeerDependencies:
      - '@babel/core'

  babel-plugin-transform-replace-object-assign@2.0.0(@babel/core@7.24.5):
    dependencies:
      '@babel/core': 7.24.5
      '@babel/helper-module-imports': 7.24.3

  babel-runtime@6.26.0:
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1

  babylon@7.0.0-beta.44: {}

  balanced-match@1.0.2: {}

  base64-arraybuffer@1.0.2: {}

  big.js@6.2.1: {}

  binary-extensions@2.3.0: {}

  bizcharts@4.1.23(@babel/core@7.24.5)(react@16.14.0):
    dependencies:
      '@antv/component': 0.8.35
      '@antv/g2': 4.1.32
      '@antv/g2plot': 2.3.39
      '@antv/util': 2.0.17
      '@babel/plugin-transform-modules-commonjs': 7.24.1(@babel/core@7.24.5)
      '@babel/plugin-transform-runtime': 7.24.3(@babel/core@7.24.5)
      '@juggle/resize-observer': 3.4.0
      babel-plugin-transform-replace-object-assign: 2.0.0(@babel/core@7.24.5)
      d3-color: 3.1.0
      react-error-boundary: 3.0.2(react@16.14.0)
      react-reconciler: 0.25.1(react@16.14.0)
      warning: 4.0.3
    transitivePeerDependencies:
      - '@babel/core'
      - react
      - supports-color

  bowser@1.9.4: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist@4.23.0:
    dependencies:
      caniuse-lite: 1.0.30001618
      electron-to-chromium: 1.4.768
      node-releases: 2.0.14
      update-browserslist-db: 1.0.16(browserslist@4.23.0)

  buffer-from@1.1.2: {}

  builtin-status-codes@3.0.0: {}

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.6.2

  camelcase@1.2.1: {}

  camelize@1.0.1: {}

  caniuse-lite@1.0.30001618: {}

  center-align@0.1.3:
    dependencies:
      align-text: 0.1.4
      lazy-cache: 1.0.4

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chardet@0.7.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  classnames@2.2.6: {}

  classnames@2.5.1: {}

  cli-cursor@2.1.0:
    dependencies:
      restore-cursor: 2.0.0

  cli-width@2.2.1: {}

  cliui@2.1.0:
    dependencies:
      center-align: 0.1.3
      right-align: 0.1.3
      wordwrap: 0.0.2

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-name@1.1.3: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  concat-map@0.0.1: {}

  concat-stream@1.6.2:
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 2.3.8
      typedarray: 0.0.6

  content-type@1.0.5: {}

  contour_plot@0.0.1: {}

  convert-source-map@2.0.0: {}

  copy-to@2.0.1: {}

  core-js-compat@3.37.1:
    dependencies:
      browserslist: 4.23.0

  core-js@2.6.12: {}

  core-util-is@1.0.3: {}

  cross-spawn@5.1.0:
    dependencies:
      lru-cache: 4.1.5
      shebang-command: 1.2.0
      which: 1.2.14

  cross-spawn@6.0.5:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  css-color-keywords@1.0.0: {}

  css-line-break@2.1.0:
    dependencies:
      utrie: 1.0.2

  css-to-react-native@3.2.0:
    dependencies:
      camelize: 1.0.1
      css-color-keywords: 1.0.0
      postcss-value-parser: 4.2.0

  csstype@3.1.3: {}

  d3-color@3.1.0: {}

  d3-ease@1.0.7: {}

  d3-hierarchy@2.0.0: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-regression@1.3.10: {}

  d3-timer@1.0.10: {}

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  dateformat@2.2.0: {}

  dayjs@1.11.11: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.4(supports-color@5.5.0):
    dependencies:
      ms: 2.1.2
    optionalDependencies:
      supports-color: 5.5.0

  decamelize@1.2.0: {}

  decode-uri-component@0.2.2: {}

  deep-equal@1.1.2:
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.0.5
      is-regex: 1.1.4
      object-is: 1.1.6
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.2

  deep-is@0.1.4: {}

  default-user-agent@1.0.0:
    dependencies:
      os-name: 1.0.3

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  defined@1.0.1: {}

  delayed-stream@1.0.0: {}

  destroy@1.2.0: {}

  detect-browser@5.3.0: {}

  digest-header@1.1.0: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-helpers@3.4.0:
    dependencies:
      '@babel/runtime': 7.24.5

  dom7@3.0.0:
    dependencies:
      ssr-window: 3.0.0

  dompurify@2.5.3: {}

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.2

  dotignore@0.1.2:
    dependencies:
      minimatch: 3.1.2

  driver-dom@2.2.2:
    dependencies:
      style-unit: 3.0.5

  driver-miniapp@0.1.5: {}

  driver-universal@3.5.0:
    dependencies:
      driver-dom: 2.2.2
      driver-miniapp: 0.1.5
      driver-weex: 2.1.0
      universal-env: 3.3.3

  driver-weex@2.1.0:
    dependencies:
      driver-dom: 2.2.2
      style-unit: 3.0.5

  ee-first@1.1.1: {}

  electron-to-chromium@1.4.768: {}

  emoji-regex@7.0.3: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  end-or-error@1.0.1: {}

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.1
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-iterator-helpers@1.0.19:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-set-tostringtag: 2.0.3
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      iterator.prototype: 1.1.2
      safe-array-concat: 1.1.2

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  escalade@3.1.2: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  ? eslint-config-ali@4.1.1(eslint-plugin-import@2.29.1(eslint@5.16.0))(eslint-plugin-react@7.34.1(eslint@5.16.0))(eslint@5.16.0)
  : dependencies:
      eslint: 5.16.0
      eslint-plugin-import: 2.29.1(eslint@5.16.0)
      eslint-plugin-react: 7.34.1(eslint@5.16.0)

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.13.1
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.8.1(eslint-import-resolver-node@0.3.9)(eslint@5.16.0):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      eslint: 5.16.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-import@2.29.1(eslint@5.16.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.2
      array.prototype.flatmap: 1.3.2
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 5.16.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.8.1(eslint-import-resolver-node@0.3.9)(eslint@5.16.0)
      hasown: 2.0.2
      is-core-module: 2.13.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.0
      semver: 6.3.1
      tsconfig-paths: 3.15.0
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-react@7.34.1(eslint@5.16.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.2
      array.prototype.toreversed: 1.1.2
      array.prototype.tosorted: 1.1.3
      doctrine: 2.1.0
      es-iterator-helpers: 1.0.19
      eslint: 5.16.0
      estraverse: 5.3.0
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.hasown: 1.1.4
      object.values: 1.2.0
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.11

  eslint-scope@3.7.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@4.0.3:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-utils@1.4.3:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-visitor-keys@1.3.0: {}

  eslint@5.16.0:
    dependencies:
      '@babel/code-frame': 7.24.2
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.4(supports-color@5.5.0)
      doctrine: 3.0.0
      eslint-scope: 4.0.3
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 5.0.1
      esquery: 1.5.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob: 7.2.3
      globals: 11.12.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 6.5.2
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      path-is-inside: 1.0.2
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 5.7.2
      strip-ansi: 4.0.0
      strip-json-comments: 2.0.1
      table: 5.4.6
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@5.0.1:
    dependencies:
      acorn: 6.4.2
      acorn-jsx: 5.3.2(acorn@6.4.2)
      eslint-visitor-keys: 1.3.0

  esprima@4.0.1: {}

  esquery@1.5.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  eventemitter3@4.0.7: {}

  execa@0.8.0:
    dependencies:
      cross-spawn: 5.1.0
      get-stream: 3.0.0
      is-stream: 1.1.0
      npm-run-path: 2.0.2
      p-finally: 1.0.0
      signal-exit: 3.0.7
      strip-eof: 1.0.0

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  fast-deep-equal@3.1.3: {}

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fecha@4.2.3: {}

  figures@2.0.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@5.0.1:
    dependencies:
      flat-cache: 2.0.1

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  filter-obj@1.1.0: {}

  find-up@2.1.0:
    dependencies:
      locate-path: 2.0.0

  flat-cache@2.0.1:
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3

  flatted@2.0.2: {}

  fmin@0.0.2:
    dependencies:
      contour_plot: 0.0.1
      json2module: 0.0.3
      rollup: 0.25.8
      tape: 4.17.0
      uglify-js: 2.8.29

  follow-redirects@1.15.6(debug@4.3.4):
    optionalDependencies:
      debug: 4.3.4(supports-color@5.5.0)

  follow-redirects@1.5.10:
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formstream@1.4.0:
    dependencies:
      destroy: 1.2.0
      mime: 2.6.0
      pause-stream: 0.0.11

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-ready@1.0.0: {}

  get-stream@3.0.0: {}

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  gl-matrix@3.4.3: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  has@1.0.4: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  history@4.10.1:
    dependencies:
      '@babel/runtime': 7.24.5
      loose-envify: 1.4.0
      resolve-pathname: 3.0.0
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3
      value-equal: 1.0.1

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  html2canvas@1.4.1:
    dependencies:
      css-line-break: 2.1.0
      text-segmentation: 1.0.3

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  idb-keyval@5.1.5:
    dependencies:
      safari-14-idb-fix: 1.0.6

  idb-keyval@6.2.1: {}

  ignore@3.3.10: {}

  ignore@4.0.6: {}

  imask@6.6.3: {}

  immutable@4.3.6: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  inquirer@6.5.2:
    dependencies:
      ansi-escapes: 3.2.0
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-width: 2.2.1
      external-editor: 3.1.0
      figures: 2.0.0
      lodash: 4.17.21
      mute-stream: 0.0.7
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 2.1.1
      strip-ansi: 5.2.0
      through: 2.3.8

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  intersection-observer@0.12.2: {}

  intersection-observer@0.7.0: {}

  intl-messageformat-parser@1.4.0: {}

  intl-messageformat@2.2.0:
    dependencies:
      intl-messageformat-parser: 1.4.0

  intl-messageformat@9.13.0:
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/fast-memoize': 1.2.1
      '@formatjs/icu-messageformat-parser': 2.1.0
      tslib: 2.6.2

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  ip@1.1.9: {}

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-async-function@2.0.0:
    dependencies:
      has-tostringtag: 1.0.2

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-buffer@1.1.6: {}

  is-callable@1.2.7: {}

  is-class-hotfix@0.0.6: {}

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-extendable@0.1.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-fullwidth-code-point@2.0.0: {}

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.2

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-stream@1.1.0: {}

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-type-of@1.4.0:
    dependencies:
      core-util-is: 1.0.3
      is-class-hotfix: 0.0.6
      isstream: 0.1.2

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-weakmap@2.0.2: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-weakset@2.0.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  isarray@0.0.1: {}

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isstream@0.1.2: {}

  iterator.prototype@1.1.2:
    dependencies:
      define-properties: 1.2.1
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      reflect.getprototypeof: 1.0.6
      set-function-name: 2.0.2

  js-base64@2.6.4: {}

  js-cookie@2.2.1: {}

  js-tokens@3.0.2: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  jsesc@2.5.2: {}

  json-schema-traverse@0.4.1: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json2module@0.0.3:
    dependencies:
      rw: 1.3.3

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jstoxml@2.2.9: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.2
      object.assign: 4.1.5
      object.values: 1.2.0

  kind-of@3.2.2:
    dependencies:
      is-buffer: 1.1.6

  lazy-cache@1.0.4: {}

  levn@0.3.0:
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2

  locate-path@2.0.0:
    dependencies:
      p-locate: 2.0.0
      path-exists: 3.0.0

  lodash-es@4.17.21: {}

  lodash.clonedeep@4.5.0: {}

  lodash.debounce@4.0.8: {}

  lodash.get@4.4.2: {}

  lodash.isequal@4.5.0: {}

  lodash.set@4.3.2: {}

  lodash.throttle@4.1.1: {}

  lodash@4.17.21: {}

  longest@1.0.1: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.6.2

  lru-cache@4.1.5:
    dependencies:
      pseudomap: 1.0.2
      yallist: 2.1.2

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  merge-descriptors@1.0.3: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@2.6.0: {}

  mimic-fn@1.2.0: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimist@1.2.8: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mock-property@1.0.3:
    dependencies:
      define-data-property: 1.1.4
      functions-have-names: 1.2.3
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      hasown: 2.0.2
      isarray: 2.0.5

  moment-timezone@0.5.45:
    dependencies:
      moment: 2.30.1

  moment@2.30.1: {}

  mri@1.2.0: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  multimatch@3.0.0:
    dependencies:
      array-differ: 2.1.0
      array-union: 1.0.2
      arrify: 1.0.1
      minimatch: 3.1.2

  mute-stream@0.0.7: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@4.0.2: {}

  natural-compare@1.4.0: {}

  nice-try@1.0.5: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.6.2

  node-releases@2.0.14: {}

  normalize-path@3.0.0: {}

  npm-run-path@2.0.2:
    dependencies:
      path-key: 2.0.1

  object-assign@4.1.1: {}

  object-inspect@1.12.3: {}

  object-inspect@1.13.1: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3

  object.hasown@1.1.4:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.values@1.2.0:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  omit.js@2.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@2.0.1:
    dependencies:
      mimic-fn: 1.2.0

  optionator@0.8.3:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5

  os-name@1.0.3:
    dependencies:
      osx-release: 1.1.0
      win-release: 1.1.1

  os-shim@0.1.3: {}

  os-tmpdir@1.0.2: {}

  osx-release@1.1.0:
    dependencies:
      minimist: 1.2.8

  p-finally@1.0.0: {}

  p-limit@1.3.0:
    dependencies:
      p-try: 1.0.0

  p-locate@2.0.0:
    dependencies:
      p-limit: 1.3.0

  p-try@1.0.0: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.2

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.2

  path-exists@3.0.0: {}

  path-is-absolute@1.0.1: {}

  path-is-inside@1.0.2: {}

  path-key@2.0.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@1.8.0:
    dependencies:
      isarray: 0.0.1

  pause-stream@0.0.11:
    dependencies:
      through: 2.3.8

  pdfast@0.2.0: {}

  picocolors@1.0.1: {}

  picomatch@2.3.1: {}

  platform@1.3.6: {}

  possible-typed-array-names@1.0.0: {}

  postcss-value-parser@4.2.0: {}

  pre-commit@1.2.2:
    dependencies:
      cross-spawn: 5.1.0
      spawn-sync: 1.0.15
      which: 1.2.14

  prelude-ls@1.1.2: {}

  prettier@1.19.1: {}

  pretty-quick@1.11.1(prettier@1.19.1):
    dependencies:
      chalk: 2.4.2
      execa: 0.8.0
      find-up: 2.1.0
      ignore: 3.3.10
      mri: 1.2.0
      multimatch: 3.0.0
      prettier: 1.19.1

  process-nextick-args@2.0.1: {}

  progress@2.0.3: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  pseudomap@1.0.2: {}

  pump@3.0.0:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  qr.js@0.0.0: {}

  qrcode.react@1.0.1(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      prop-types: 15.8.1
      qr.js: 0.0.0
      react: 16.14.0

  qs@6.12.1:
    dependencies:
      side-channel: 1.0.6

  query-string@6.14.1:
    dependencies:
      decode-uri-component: 0.2.2
      filter-obj: 1.1.0
      split-on-first: 1.1.0
      strict-uri-encode: 2.0.0

  rax-children@1.0.0(rax@1.2.3):
    dependencies:
      rax: 1.2.3

  rax-clone-element@1.0.0(rax@1.2.3):
    dependencies:
      rax: 1.2.3
      rax-is-valid-element: 1.0.1

  rax-create-factory@1.0.0(rax@1.2.3):
    dependencies:
      rax: 1.2.3

  rax-is-valid-element@1.0.1: {}

  rax@1.2.3:
    dependencies:
      '@babel/runtime': 7.24.5
      prop-types: 15.8.1
      rax-children: 1.0.0(rax@1.2.3)
      rax-clone-element: 1.0.0(rax@1.2.3)
      rax-create-factory: 1.0.0(rax@1.2.3)
      rax-is-valid-element: 1.0.1

  react-display-name-export@1.0.0: {}

  react-dom@16.14.0(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-error-boundary@3.0.2(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      react: 16.14.0

  react-error-boundary@3.1.4(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      react: 16.14.0

  react-fast-compare@3.2.2: {}

  react-imask@6.6.3(react@16.14.0):
    dependencies:
      imask: 6.6.3
      prop-types: 15.8.1
      react: 16.14.0

  react-intl@5.25.1(react@16.14.0)(typescript@5.4.5):
    dependencies:
      '@formatjs/ecma402-abstract': 1.11.4
      '@formatjs/icu-messageformat-parser': 2.1.0
      '@formatjs/intl': 2.2.1(typescript@5.4.5)
      '@formatjs/intl-displaynames': 5.4.3
      '@formatjs/intl-listformat': 6.5.3
      '@types/hoist-non-react-statics': 3.3.5
      '@types/react': 16.14.60
      hoist-non-react-statics: 3.3.2
      intl-messageformat: 9.13.0
      react: 16.14.0
      tslib: 2.6.2
    optionalDependencies:
      typescript: 5.4.5

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-lifecycles-compat@3.0.4: {}

  react-reconciler@0.25.1(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1

  react-router-dom@5.3.4(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      history: 4.10.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-router: 5.3.4(react@16.14.0)
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3

  react-router@5.3.4(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      history: 4.10.1
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      path-to-regexp: 1.8.0
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 16.13.1
      tiny-invariant: 1.3.3
      tiny-warning: 1.0.3

  react-sortable-hoc@1.11.0(prop-types@15.8.1)(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      invariant: 2.2.4
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)

  react-sticky-box@0.9.3(prop-types@15.8.1)(react@16.14.0):
    dependencies:
      '@babel/runtime': 7.24.5
      prop-types: 15.8.1
      react: 16.14.0
      resize-observer-polyfill: 1.5.1

  react-string-replace@1.1.1: {}

  react-transition-group@2.9.0(react-dom@16.14.0(react@16.14.0))(react@16.14.0):
    dependencies:
      dom-helpers: 3.4.0
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-lifecycles-compat: 3.0.4

  react-transition-group@2.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      dom-helpers: 3.4.0
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-lifecycles-compat: 3.0.4

  react@16.14.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect.getprototypeof@1.0.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      which-builtin-type: 1.1.3

  regenerator-runtime@0.11.1: {}

  regenerator-runtime@0.14.1: {}

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  regexpp@2.0.1: {}

  repeat-string@1.6.1: {}

  requireindex@1.1.0: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-pathname@3.0.0: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@2.0.0:
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  right-align@0.1.3:
    dependencies:
      align-text: 0.1.4

  rimraf@2.6.3:
    dependencies:
      glob: 7.2.3

  rollup@0.25.8:
    dependencies:
      chalk: 1.1.3
      minimist: 1.2.8
      source-map-support: 0.3.3

  run-async@2.4.1: {}

  rw@1.3.3: {}

  rxjs@6.6.7:
    dependencies:
      tslib: 1.14.1

  safari-14-idb-fix@1.0.6: {}

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  safer-buffer@2.1.2: {}

  sass@1.77.1:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.6
      source-map-js: 1.2.0

  sax@1.3.0: {}

  scheduler@0.19.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  screenfull@5.2.0: {}

  sdk-base@2.0.1:
    dependencies:
      get-ready: 1.0.0

  semver@5.7.2: {}

  semver@6.3.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  shallow-element-equals@1.0.1:
    dependencies:
      style-equal: 1.0.0

  shallowequal@1.1.0: {}

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-regex@1.0.0: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1

  signal-exit@3.0.7: {}

  size-sensor@1.0.2: {}

  slice-ansi@2.1.0:
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0

  source-map-js@1.2.0: {}

  source-map-support@0.3.3:
    dependencies:
      source-map: 0.1.32

  source-map@0.1.32:
    dependencies:
      amdefine: 1.0.1

  source-map@0.5.7: {}

  spawn-sync@1.0.15:
    dependencies:
      concat-stream: 1.6.2
      os-shim: 0.1.3

  split-on-first@1.1.0: {}

  sprintf-js@1.0.3: {}

  ssr-window@3.0.0: {}

  statuses@1.5.0: {}

  stream-http@2.8.2:
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2

  stream-wormhole@1.1.0: {}

  strict-uri-encode@2.0.0: {}

  string-width@2.1.1:
    dependencies:
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 4.0.0

  string-width@3.1.0:
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0

  string.prototype.matchall@4.0.11:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      regexp.prototype.flags: 1.5.2
      set-function-name: 2.0.2
      side-channel: 1.0.6

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@4.0.0:
    dependencies:
      ansi-regex: 3.0.1

  strip-ansi@5.2.0:
    dependencies:
      ansi-regex: 4.1.1

  strip-bom@3.0.0: {}

  strip-eof@1.0.0: {}

  strip-json-comments@2.0.1: {}

  style-equal@1.0.0: {}

  style-unit@2.0.1:
    dependencies:
      universal-env: 2.0.0

  style-unit@3.0.5:
    dependencies:
      '@babel/runtime': 7.24.5
      universal-env: 3.3.3

  styled-components@5.3.11(@babel/core@7.24.5)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0):
    dependencies:
      '@babel/helper-module-imports': 7.24.3
      '@babel/traverse': 7.24.5(supports-color@5.5.0)
      '@emotion/is-prop-valid': 1.2.2
      '@emotion/stylis': 0.8.5
      '@emotion/unitless': 0.7.5
      babel-plugin-styled-components: 2.1.4(@babel/core@7.24.5)(styled-components@5.3.11(@babel/core@7.24.5)(react-dom@16.14.0(react@16.14.0))(react-is@18.3.1)(react@16.14.0))
      css-to-react-native: 3.2.0
      hoist-non-react-statics: 3.3.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.3.1
      shallowequal: 1.1.0
      supports-color: 5.5.0
    transitivePeerDependencies:
      - '@babel/core'

  supports-color@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  swiper@6.5.0:
    dependencies:
      dom7: 3.0.0
      ssr-window: 3.0.0

  table@5.4.6:
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0

  tape@4.17.0:
    dependencies:
      '@ljharb/resumer': 0.0.1
      '@ljharb/through': 2.3.13
      call-bind: 1.0.7
      deep-equal: 1.1.2
      defined: 1.0.1
      dotignore: 0.1.2
      for-each: 0.3.3
      glob: 7.2.3
      has: 1.0.4
      inherits: 2.0.4
      is-regex: 1.1.4
      minimist: 1.2.8
      mock-property: 1.0.3
      object-inspect: 1.12.3
      resolve: 1.22.8
      string.prototype.trim: 1.2.9

  text-segmentation@1.0.3:
    dependencies:
      utrie: 1.0.2

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  through@2.3.8: {}

  tiny-invariant@1.3.3: {}

  tiny-warning@1.0.3: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-arraybuffer@1.0.1: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  trim-right@1.0.1: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.6.2: {}

  type-check@0.3.2:
    dependencies:
      prelude-ls: 1.1.2

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typedarray@0.0.6: {}

  typescript@5.4.5: {}

  uglify-js@2.8.29:
    dependencies:
      source-map: 0.5.7
      yargs: 3.10.0
    optionalDependencies:
      uglify-to-browserify: 1.0.2

  uglify-to-browserify@1.0.2:
    optional: true

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  unescape@1.0.1:
    dependencies:
      extend-shallow: 2.0.1

  universal-canvas-context@1.0.0: {}

  universal-choose-image@1.3.0(rax@1.2.3):
    dependencies:
      rax: 1.2.3
      universal-env: 3.3.3

  universal-device@1.0.3:
    dependencies:
      universal-env: 3.3.3

  universal-device@2.3.1:
    dependencies:
      universal-env: 3.3.3

  universal-element@0.0.6:
    dependencies:
      universal-env: 3.3.3

  universal-env@0.6.6: {}

  universal-env@1.0.7: {}

  universal-env@2.0.0: {}

  universal-env@3.3.3:
    dependencies:
      '@uni/env': 1.1.1

  universal-panresponder@0.6.5:
    dependencies:
      universal-env: 0.6.6

  universal-transition@1.1.1:
    dependencies:
      style-unit: 2.0.1
      universal-device: 1.0.3
      universal-env: 3.3.3
      universal-unit-tool: 1.0.0

  universal-unit-tool@1.0.0:
    dependencies:
      universal-device: 2.3.1

  update-browserslist-db@1.0.16(browserslist@4.23.0):
    dependencies:
      browserslist: 4.23.0
      escalade: 3.1.2
      picocolors: 1.0.1

  upper-case@2.0.2:
    dependencies:
      tslib: 2.6.2

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url@0.11.3:
    dependencies:
      punycode: 1.4.1
      qs: 6.12.1

  urlencode@1.1.0:
    dependencies:
      iconv-lite: 0.4.24

  urllib@2.41.0:
    dependencies:
      any-promise: 1.3.0
      content-type: 1.0.5
      debug: 2.6.9
      default-user-agent: 1.0.0
      digest-header: 1.1.0
      ee-first: 1.1.1
      formstream: 1.4.0
      humanize-ms: 1.2.1
      iconv-lite: 0.4.24
      ip: 1.1.9
      pump: 3.0.0
      qs: 6.12.1
      statuses: 1.5.0
      utility: 1.18.0
    transitivePeerDependencies:
      - supports-color

  util-deprecate@1.0.2: {}

  utility@1.18.0:
    dependencies:
      copy-to: 2.0.1
      escape-html: 1.0.3
      mkdirp: 0.5.6
      mz: 2.7.0
      unescape: 1.0.1

  utrie@1.0.2:
    dependencies:
      base64-arraybuffer: 1.0.2

  value-equal@1.0.1: {}

  warning@4.0.3:
    dependencies:
      loose-envify: 1.4.0

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-builtin-type@1.1.3:
    dependencies:
      function.prototype.name: 1.1.6
      has-tostringtag: 1.0.2
      is-async-function: 2.0.0
      is-date-object: 1.0.5
      is-finalizationregistry: 1.0.2
      is-generator-function: 1.0.10
      is-regex: 1.1.4
      is-weakref: 1.0.2
      isarray: 2.0.5
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.3

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@1.2.14:
    dependencies:
      isexe: 2.0.0

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  win-release@1.1.1:
    dependencies:
      semver: 5.7.2

  window-size@0.1.0: {}

  word-wrap@1.2.5: {}

  wordwrap@0.0.2: {}

  wrappy@1.0.2: {}

  write@1.0.3:
    dependencies:
      mkdirp: 0.5.6

  xml2js@0.6.2:
    dependencies:
      sax: 1.3.0
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xtend@4.0.2: {}

  yallist@2.1.2: {}

  yallist@3.1.1: {}

  yargs@3.10.0:
    dependencies:
      camelcase: 1.2.1
      cliui: 2.1.0
      decamelize: 1.2.0
      window-size: 0.1.0
