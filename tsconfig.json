{"compilerOptions": {"experimentalDecorators": true, "baseUrl": ".", "outDir": "./lib", "module": "esnext", "lib": ["esnext", "es5", "dom"], "sourceMap": true, "allowJs": false, "jsx": "react", "removeComments": false, "declaration": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": false, "noImplicitAny": false, "esModuleInterop": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "traceResolution": true, "typeRoots": ["./@types", "./node_modules/@types"], "allowSyntheticDefaultImports": true, "downlevelIteration": true}}