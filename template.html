<meta name="wt-country" content="SG" />
<meta name="wt-lang" content="en_US" />

<div id="root"></div>

<script src="https://g.alicdn.com/code/npm/@alife/mockajax/1.0.1/mockajax.min.js"></script>
<script type="module" src="/mock/index.js"></script>
<style>
  body {
    background: #efefef;
  }
</style>

<script>
  (function() {
    const { publicPath, page } = WebpackDevServerConfig;
    window.Next = {};
    SchemaResolver.render(
      {
        // micro: true, // if you wanna close micro FE feature, change to false
        // hfType: 'workstation-lzd',
        //hfType: 'lsc', // if wanna use ASC layout change to this config
        externals: [
          '//g.alicdn.com/code/npm/@alife/lazada-asc-theme/3.23.3/next.min.js',
          '//g.alicdn.com/code/npm/@alife/lazada-asc-theme/3.23.3/next.min.css',
        ],
        resource: {
          js: `//${publicPath}/${page}/index.js`,
          css: `//${publicPath}/${page}/index.css`,
        },
      },
      { container: '#root' },
    );
  })();
</script>
