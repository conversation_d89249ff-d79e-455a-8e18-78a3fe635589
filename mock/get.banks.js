export default () => {
  return {
    ret: ['SUCCESS::调用成功'],
    v: '1.0',
    api: 'mtop.global.merchant.subaccount.bank.branches.lazada',
    data: {
      data: [
        ...Object.keys(Array.apply(null, { length: 10 })).map(item => {
          const bankCode = Math.ceil(Math.random() * 1000000);
          return {
            label: `Bank name ${bankCode} label`,
            value: `Bank name ${bankCode} val`,
            bankCode,
            swift: `SWIFT_${bankCode}`,
            isLeaf: Math.random() > 0.5,
          };
        }),
      ],
    },
  };
};
