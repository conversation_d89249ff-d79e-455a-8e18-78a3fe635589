export default {
  api: 'mtop.global.merchant.sellermodifylog.list',
  data: {
    data: {
      dataSource: [
        {
          createTime: 1627031447430,
          id: 27066338,
          newValue: '312321',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1627006985525,
          id: 27032003,
          newValue: '123',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1627006901955,
          id: 27032001,
          newValue: '12',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1625717177799,
          id: 24240021,
          newValue: '1232131231',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1622708201666,
          id: 10208011,
          newValue: '123213123',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1617161933739,
          id: 2056003,
          newValue: '123213',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1617161625624,
          id: 2058013,
          newValue: '212',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1617160601363,
          id: 2058007,
          newValue: 'qwqe',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1615182960849,
          id: 578011,
          newValue: 'TEST1',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
        {
          createTime: 1615182888900,
          id: 580009,
          newValue: 'TEST',
          operatorEmail: '<EMAIL>',
          operatorName: 'Xiaobai IM Test',
          readStatus: 0,
          timeZone: 'GMT+08:00',
        },
      ],
      pageInfo: { current: 1, pageSize: 10, total: 13 },
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
