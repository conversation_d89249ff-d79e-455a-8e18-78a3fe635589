export default {
  api: 'mtop.global.merchant.subaccount.profile.render.lazada',
  data: {
    data: {
      globalDisabled: false,
      fields: [
        {
          disabled: false,
          fieldName: 'individual_bank_account_name',
          history: true,
          label: 'Account Name',
          name: '74',
          needOtp: true,
          needQc: false,
          priority: 1,
          required: true,
          uiType: 'Input',
          value: 'Zhan',
        },
        {
          disabled: false,
          fieldName: 'individual_bank_account_nr',
          history: true,
          label: 'Account Number',
          name: '75',
          needOtp: true,
          needQc: false,
          priority: 2,
          required: true,
          tips: 'This should match the details stated in the bank documents',
          uiType: 'Input',
          value: '*********',
        },
        {
          dataSource: [
            { label: 'Australia New Zealand Banking Group Ltd', value: 'Australia New Zealand Banking Group Ltd' },
            { label: 'Bangkok Bank Public Company Ltd', value: 'Bangkok Bank Public Company Ltd' },
            { label: 'Bank of America, Singapore', value: 'Bank of America, Singapore' },
            { label: 'Bank of China Limited', value: 'Bank of China Limited' },
            { label: 'Bank of India', value: 'Bank of India' },
            { label: 'BNP Paribas', value: 'BNP Paribas' },
            { label: 'China CITIC Bank International Ltd', value: 'China CITIC Bank International Ltd' },
            { label: 'Chinatrust Commercial Bank Co., Ltd', value: 'Chinatrust Commercial Bank Co., Ltd' },
            { label: 'CIMB Bank Berhad', value: 'CIMB Bank Berhad' },
            { label: 'CITIBANK BERHAD', value: 'CITIBANK BERHAD' },
            { label: 'Citibank N.A. Singapore Branch', value: 'Citibank N.A. Singapore Branch' },
            { label: 'Citibank N.A., Hong Kong', value: 'Citibank N.A., Hong Kong' },
            { label: 'Commerzbank AG', value: 'Commerzbank AG' },
            {
              label: 'Credit Agricole Corporate and Investment Bank',
              value: 'Credit Agricole Corporate and Investment Bank',
            },
            { label: 'DBS Bank Ltd', value: 'DBS Bank Ltd' },
            { label: 'Deutsche Bank AG', value: 'Deutsche Bank AG' },
            { label: 'DnB NOR Bank ASA', value: 'DnB NOR Bank ASA' },
            { label: 'Dresdner Bank AG', value: 'Dresdner Bank AG' },
            { label: 'First Commercial Bank', value: 'First Commercial Bank' },
            { label: 'HL Bank', value: 'HL Bank' },
            { label: 'HSBC Bank (Singapore) LTD ', value: 'HSBC Bank (Singapore) LTD ' },
            { label: 'ICICI Bank Limited', value: 'ICICI Bank Limited' },
            { label: 'Indian Bank', value: 'Indian Bank' },
            { label: 'Indian Overseas Bank', value: 'Indian Overseas Bank' },
            { label: 'Industrial & Commercial Bank Of China', value: 'Industrial & Commercial Bank Of China' },
            { label: 'Intesa Sanpaolo S.P.A.', value: 'Intesa Sanpaolo S.P.A.' },
            { label: 'JPMorgan Chase Bank, N.A.', value: 'JPMorgan Chase Bank, N.A.' },
            { label: 'Korea Exchange Bank', value: 'Korea Exchange Bank' },
            { label: 'Landesbank Baden- Wurttemberg', value: 'Landesbank Baden- Wurttemberg' },
            { label: 'Lazada_finance_bank_test01', value: 'Lazada_finance_bank_test01' },
            { label: 'lazada_finance_test_02', value: 'lazada_finance_test_02' },
            { label: 'Maybank Singapore Limited', value: 'Maybank Singapore Limited' },
            { label: 'Mizuho Corporate Bank, Ltd', value: 'Mizuho Corporate Bank, Ltd' },
            { label: 'National Australia Bank Ltd', value: 'National Australia Bank Ltd' },
            { label: 'Nordea Bank Finland Plc', value: 'Nordea Bank Finland Plc' },
            { label: 'Oversea-Chinese Banking Corporation Ltd', value: 'Oversea-Chinese Banking Corporation Ltd' },
            { label: 'P.T. Bank Negara Indonesia (Persero) Tbk', value: 'P.T. Bank Negara Indonesia (Persero) Tbk' },
            { label: 'RHB Bank Berhad', value: 'RHB Bank Berhad' },
            { label: 'Singapore Island Bank Limited', value: 'Singapore Island Bank Limited' },
            { label: 'Skandinaviska Enskilda Banken AB (publ)', value: 'Skandinaviska Enskilda Banken AB (publ)' },
            { label: 'Societe Generale', value: 'Societe Generale' },
            {
              label: 'Standard Chartered Bank (Singapore) Limited',
              value: 'Standard Chartered Bank (Singapore) Limited',
            },
            { label: 'State Bank of India', value: 'State Bank of India' },
            { label: 'Sumitomo Mitsui Banking Corporation', value: 'Sumitomo Mitsui Banking Corporation' },
            { label: 'Svenska Handelsbanken AB (publ)', value: 'Svenska Handelsbanken AB (publ)' },
            { label: 'The Bank of East Asia Ltd', value: 'The Bank of East Asia Ltd' },
            {
              label: 'The Bank of Tokyo-Mitsubishi UFJ, Ltd (MUFG Bank, Ltd)',
              value: 'The Bank of Tokyo-Mitsubishi UFJ, Ltd (MUFG Bank, Ltd)',
            },
            {
              label: 'The Hongkong and Shanghai Banking Corporation Limited ',
              value: 'The Hongkong and Shanghai Banking Corporation Limited ',
            },
            {
              label: 'The Hongkong Shanghai Banking Corporation Ltd (HSBC Corporate Account)',
              value: 'The Hongkong Shanghai Banking Corporation Ltd (HSBC Corporate Account)',
            },
            { label: 'THE ROYAL BANK OF SCOTLAND N.V.', value: 'THE ROYAL BANK OF SCOTLAND N.V.' },
            { label: 'UBS AG', value: 'UBS AG' },
            { label: 'UCO Bank', value: 'UCO Bank' },
            { label: 'United Overseas Bank', value: 'United Overseas Bank' },
          ],
          disabled: false,
          fieldName: 'individual_bank_account_bank',
          history: false,
          label: 'Bank',
          name: '76',
          needOtp: true,
          needQc: false,
          priority: 3,
          required: true,
          uiType: 'BankSelector',
          value: {
            bankCode: '7931057',
            bank: 'Australia New Zealand Banking Group Ltd',
            branch: 'Credit Card Account',
            swift: 'ANZBSGSXXXX',
          },
        },
        {
          accept:
            'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          disabled: false,
          fieldName: 'individual_document_bank_info',
          history: true,
          label: 'Bank Information Document',
          limit: 1,
          maxSize: 5242880,
          name: '80',
          needOtp: true,
          needQc: false,
          priority: 7,
          required: true,
          tips: 'This should match the details stated in the bank documents',
          uiType: 'Upload',
          value: {
            downloadUrl: '',
            mime: 'image/jpeg',
            name: '**************.jpg',
            uploadId: '*********',
          },
        },
      ],
      globalNotice: {
        content: 'Your information has been rejected, please submit again in:',
        id: '1',
        name: 'globalNotice',
        status: 'onboardingUnverified',
        todoPageHref: '/apps/todo/detail/index?taskType=idbank&sellerType=1',
        type: 'warning',
      },
      onboardingStatus: {
        isNewSeller: true,
        locale: {
          cancel: 'Cancel',
          pendingTips: 'For New Sellers, please submit your information in: ',
          gotoTODOPage: 'Homepage new seller tasks card',
          rejectTips: 'Your information has been rejected, please submit again in:',
          approvingTips: 'Your information is under verification, please kindly wait for the result',
        },
        name: 'editMode',
        sellerApproveState: 'Reject',
        toDoPageHref: '/apps/todo/detail/index?taskType=idbank&sellerType=1',
      },
    },
  },
  ret: ['SUCCESS::success'],
  v: '1.0',
};
