export default {
  tableColumns: [
    { text: 'Name', key: 'name', sort: true },
    { text: 'Default', key: 'is_default', sort: false },
    { text: 'Enabled', key: 'enabled', sort: false },
    { text: 'Cash on Delivery', key: 'cashOnDelivery', sort: false },
  ],
  tableRows: [
    { name: 'LGS-FM40', is_default: true, enabled: true, cashOnDelivery: true },
    { name: 'LGS-FM50', is_default: false, enabled: true, cashOnDelivery: true },
    { name: 'LGS-FM42', is_default: false, enabled: true, cashOnDelivery: true },
    { name: 'LGS-FM41', is_default: false, enabled: true, cashOnDelivery: true },
    { name: 'LGS-FM43', is_default: false, enabled: true, cashOnDelivery: true },
    { name: 'LGS-FM48', is_default: false, enabled: true, cashOnDelivery: false },
    { name: 'LGS-FM49', is_default: false, enabled: true, cashOnDelivery: false },
    { name: 'LGS-FM45', is_default: false, enabled: true, cashOnDelivery: false },
  ],
  pagination: { pageIndex: 1, total: 11, pageSize: 25 },
};
