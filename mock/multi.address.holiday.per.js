export default {
  api: 'mtop.global.merchant.subaccount.multi.address.holiday.per',
  data: {
    data: {
      name: 'settingPerWhTable',
      pageInfo: { current: 1, total: 63, pageSize: 10 },
      dataSource: [
        {
          code: 'dropshipping',
          dateFormat: '2021-04-30-2021-05-04',
          end: '2021-05-04 00:00:00',
          isNotOpen: true,
          isOpen: false,
          name: 'dropshipping',
          start: '2021-04-30 00:00:00',
        },
        {
          code: 'SG101BB-WH-10002',
          dateFormat: '2021-09-17-2022-09-17',
          end: '2022-09-17 00:00:00',
          isNotOpen: true,
          isOpen: false,
          name: '乐游测试新增',
          start: '2021-09-17 00:00:00',
        },
        {
          code: 'SG101BB-WH-10003',
          dateFormat: '2021-09-17-2022-09-17',
          end: '2022-09-17 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '乐游新增数据2',
          start: '2021-09-17 00:00:00',
        },
        {
          code: 'SG101BB-WH-10004',
          dateFormat: '2021-09-17-2022-09-17',
          end: '2022-09-17 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '乐游新增3',
          start: '2021-09-17 00:00:00',
        },
        {
          code: 'SG101BB-WH-10005',
          dateFormat: '2021-09-17-2022-09-17',
          end: '2022-09-17 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '乐游1新增444',
          start: '2021-09-17 00:00:00',
        },
        {
          code: 'SG101BB-WH-10006',
          dateFormat: '2021-09-17-2022-09-17',
          end: '2022-09-17 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '乐游新增5555',
          start: '2021-09-17 00:00:00',
        },
        {
          code: 'SG101BB-WH-10007',
          dateFormat: '2021-09-23-2022-09-23',
          end: '2022-09-23 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '森淼测试02',
          start: '2021-09-23 00:00:00',
        },
        {
          code: 'SG101BB-WH-10008',
          dateFormat: '2021-09-23-2022-09-23',
          end: '2022-09-23 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '森淼测试03',
          start: '2021-09-23 00:00:00',
        },
        {
          code: 'SG101BB-WH-10009',
          dateFormat: '2021-09-23-2022-09-23',
          end: '2022-09-23 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '森淼测试04',
          start: '2021-09-23 00:00:00',
        },
        {
          code: 'SG101BB-WH-10010',
          dateFormat: '2021-09-23-2022-09-23',
          end: '2022-09-23 00:00:00',
          isNotOpen: false,
          isOpen: true,
          name: '森淼测试06',
          start: '2021-09-23 00:00:00',
        },
      ],
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
