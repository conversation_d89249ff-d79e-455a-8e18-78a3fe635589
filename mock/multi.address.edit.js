export default {
  api: 'mtop.global.merchant.subaccount.multi.address.edit',
  data: {
    data: [
      {
        elements: [
          {
            disabled: false,
            extData: {},
            label: 'Warehouse name',
            labelPosition: 'left',
            name: 'name',
            required: true,
            subLabel: '*',
            uiType: 'Input',
            value: '乐游测试新增',
          },
          {
            disabled: false,
            extData: {},
            label: 'First and Last name',
            labelPosition: 'left',
            name: 'contactName',
            required: false,
            uiType: 'Input',
            value: '********',
          },
          {
            disabled: false,
            extData: {},
            label: 'Phone number',
            labelPosition: 'left',
            name: 'phone',
            required: true,
            subLabel: '*',
            uiType: 'Input',
            value: '***********',
          },
          {
            disabled: false,
            extData: {},
            label: 'Email',
            labelPosition: 'left',
            name: 'email',
            required: false,
            uiType: 'Input',
            value: '<EMAIL>',
          },
          {
            actions: [
              {
                eventType: 'sendRequest',
                request: {
                  data: { patchName: 'countryRegion', code: '${country.value}', group: 7 },
                  url: 'mtop.global.merchant.subaccount.multi.address.cascade.country',
                },
              },
              {
                eventType: 'sendRequest',
                request: {
                  data: { patchName: 'locationLevel1', code: '${country.value}', group: 7 },
                  url: 'mtop.global.merchant.subaccount.multi.address.cascade.province',
                },
              },
            ],
            autoSearch: true,
            disabled: false,
            hasClear: true,
            label: 'Country/ Region',
            name: 'country',
            options: [{ label: 'Singapore', value: 'SG' }],
            required: true,
            uiType: 'Select',
            value: 'SG',
          },
          {
            autoSearch: true,
            disabled: false,
            hasClear: true,
            label: 'State/Province',
            name: 'locationLevel1',
            options: [{ label: 'Singapore', value: 'R80020282' }],
            required: true,
            uiType: 'Select',
            value: 'R80020282',
          },
          {
            content: '65  Hillview  Dairy  Farm  Bukit  Panjang  Choa  Chu  Kang',
            label: 'Region',
            labelPosition: 'left',
            name: 'locationLevel2',
            uiType: 'Text',
            value: '65  Hillview  Dairy  Farm  Bukit  Panjang  Choa  Chu  Kang',
            visible: true,
          },
          {
            actions: [
              {
                eventType: 'sendRequest',
                request: {
                  data: { patchName: 'locationLevel2', code: '${locationLevel3.value}' },
                  url: 'mtop.global.merchant.subaccount.multi.address.cascade.postcode',
                },
              },
            ],
            disabled: false,
            extData: {},
            label: 'Postal Code',
            labelPosition: 'left',
            name: 'locationLevel3',
            required: true,
            subLabel: '*',
            uiType: 'Input',
            value: 'R2939673',
          },
          {
            disabled: false,
            extData: {},
            label: 'Address',
            labelPosition: 'left',
            name: 'address1',
            required: true,
            subLabel: '*',
            uiType: 'Input',
            value: '123123',
          },
          {
            content: '0',
            label: '',
            labelPosition: 'left',
            name: 'isDefault',
            uiType: 'Text',
            value: '0',
            visible: false,
          },
          {
            content: '3878528',
            label: '',
            labelPosition: 'left',
            name: 'addressId',
            uiType: 'Text',
            value: '3878528',
            visible: false,
          },
          {
            content: 'SG101BB-WH-10002',
            label: '',
            labelPosition: 'left',
            name: 'addressCode',
            uiType: 'Text',
            value: 'SG101BB-WH-10002',
            visible: false,
          },
        ],
        extData: {},
        name: 'editAddress',
        type: 'CommonContainer',
      },
      { name: 'countryTab', uiType: 'Tab', value: '0', visible: false },
      { name: 'multiAddressTab', uiType: 'Tab', value: 'true', visible: true },
      { name: 'inWhiteListTab', uiType: 'Tab', value: 'true', visible: true },
      {
        name: 'secondTab',
        options: [
          {
            content: 'Seller Account',
            href: '/profile/your_profile?activeKey=0&generalKey=1',
            name: 'sellerAcount',
            target: '_self',
            uiType: 'Link',
            value: '1',
          },
          {
            content: 'Business Information',
            href: '/profile/your_profile?activeKey=0&generalKey=2',
            name: 'businessInfomation',
            target: '_self',
            uiType: 'Link',
            value: '2',
          },
          {
            content: 'Bank Account',
            href: '/profile/your_profile?activeKey=0&generalKey=5',
            name: 'bannerAccount',
            target: '_self',
            uiType: 'Link',
            value: '3',
          },
          {
            content: 'Warehouse Address',
            href: '/apps/warehouse/index?activeKey=0&secondTab=4',
            name: 'warehouse',
            target: '_self',
            uiType: 'Link',
            value: '4',
          },
          {
            content: 'Service Area',
            href: '/apps/servicearea/index?activeKey=0&secondTab=5',
            name: 'serviceArea',
            target: '_self',
            uiType: 'Link',
            value: '5',
          },
          {
            content: 'Store List',
            href: '/apps/account/profile/store',
            name: 'storeList',
            target: '_self',
            uiType: 'Link',
            value: '7',
          },
          {
            content: 'Operating Hours',
            href: '/apps/account/profile/schedule',
            name: 'operationHours',
            target: '_self',
            uiType: 'Link',
            value: '6',
          },
        ],
      },
      {
        name: 'groupId',
        options: [
          { label: 'Seller Account', name: 'sellerAccount', target: '_self', uiType: 'Link', value: '1' },
          { label: 'Business Information', name: 'businessInfomation', target: '_self', uiType: 'Link', value: '2' },
          { label: 'Bank Account', name: 'bannerAccount', target: '_self', uiType: 'Link', value: '5' },
          {
            href: '/apps/warehouse/index?activeKey=0&secondTab=4',
            label: 'Warehouse Address',
            name: 'warehouse',
            target: '_self',
            uiType: 'Link',
            value: 'secondTab4',
          },
          {
            href: '/apps/servicearea/index?activeKey=0&secondTab=5',
            label: 'Service Area',
            name: 'serviceArea',
            target: '_self',
            uiType: 'Link',
            value: 'secondTab5',
          },
          {
            href: '/apps/account/profile/store',
            label: 'Store List',
            name: 'storeList',
            target: '_self',
            uiType: 'Link',
            value: '7',
          },
          {
            href: '/apps/account/profile/schedule',
            label: 'Operating Hours',
            name: 'operationHours',
            target: '_self',
            uiType: 'Link',
            value: '6',
          },
        ],
      },
      { extData: {}, name: 'allPerTableContainer', type: 'CommonContainer', visible: true },
      { extData: {}, name: 'newAllWhTableContainer', type: 'CommonContainer', visible: true },
      { href: 'https://www.lazada.sg', name: 'instructionsBtn', target: '_blank', uiType: 'Link' },
    ],
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
