// add global mtop meta info
window.mtopConfig = {
  prefix: 'acs-wapa',
  subDomain: 'xxx',
  mainDomain: 'xxx',
  appKey: 'xxx',
  queryStringParameters: { 'x-i18n-regionID': 'LAZADA_SG' },
};

// get MockAjax from cdn
const MockAjax = window.MockAjax.default;

MockAjax.mtop = ({ mtop, ...restOptions }) => {
  MockAjax.mock([
    {
      url: new RegExp(`/h5/${mtop}`),
      ...restOptions,
      response(payload) {
        const { query, method, body } = payload;
        try {
          const result = JSON.parse(method === 'POST' ? body.data : query.data || '');
          return restOptions.response(result);
        } catch (e) {
          console.error(e);
        }
      },
    },
  ]);
};

// add mocks here
let showedSubBankValue = false;
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.bank.branches.lazada',
  method: 'GET',
  async response(input) {
    // console.log(input)
    const { default: res } = await import('./get.banks.js');
    const resInfo = res();
    if (input.bankName && !showedSubBankValue) {
      showedSubBankValue = true;
      resInfo.data.data.push({
        label: `Bank name *********`,
        value: `Bank name ********* val`,
        bankCode: *********,
        swift: `SWIFT_*********`,
        isLeaf: true,
      });
    }

    if (!input.bankName) {
      resInfo.data.data.push({
        label: `Bank name ********* parent - label`,
        value: `Bank name ********* parent val`,
        swift: `SWIFT_*********_parent`,
        bankCode: 1*********,
        isLeaf: false,
      });
    }
    return resInfo;
  },
});

MockAjax.mock([
  {
    url: new RegExp('/profile/upload_profile'),
    method: 'POST',
    async response(payload) {
      const { default: res } = await import('./profile.upload.js');
      return res;
    },
  },
]);

MockAjax.mock([
  {
    url: new RegExp('/seller/profile/shipping'),
    method: 'GET',
    async response(payload) {
      const { default: res } = await import('./seller.shipping.js');
      // return {};
      return res;
    },
  },
]);

MockAjax.mock([
  {
    url: new RegExp('/sellerConfig/getDeliveryOptionsData'),
    method: 'GET',
    async response(payload) {
      const { default: res } = await import('./sellerConfig.getDeliveryOptionsData.js');
      // return {};
      return res;
    },
  },
]);

MockAjax.mtop({
  mtop: 'mtop.global.merchant.get.seller.profile',
  method: 'GET',
  async response() {
    const { default: res } = await import('./get.seller.profile.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.profile.render.lazada',
  method: 'GET',
  async response(params) {
    let mockInfo = await import('./account.render.js');
    if (params.groupId === 2) {
      mockInfo = await import('./license.render.js');
    } else if (params.groupId === 5) {
      mockInfo = await import('./bank.render.js');
    }
    return mockInfo.default;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.profile.update.lazada',
  method: 'POST',
  async response() {
    const { default: res } = await import('./sellerprofile.update.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.profile.update.precheck.lazada',
  method: 'POST',
  async response() {
    const { default: res } = await import('./sellerprofile.precheck.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.login.account.render',
  method: 'GET',
  async response() {
    const { default: res } = await import('./login.account.render.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.sellermodifylog.read',
  method: 'POST',
  async response() {
    const { default: res } = await import('./sellermodifylog.read.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.sellermodifylog.list',
  method: 'GET',
  async response() {
    const { default: res } = await import('./sellermodifylog.list.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.otp.userinfo',
  method: 'GET',
  async response() {
    const { default: res } = await import('./otp.userinfo.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.otp.code.send',
  method: 'GET',
  async response() {
    const { default: res } = await import('./otp.code.send.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.otp.code.verify',
  method: 'POST',
  async response() {
    const { default: res } = await import('./otp.code.verify.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.sellerprofile.credential.update',
  method: 'GET',
  async response() {
    const { default: res } = await import('./credential.update.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.sellerprofile.update',
  method: 'GET',
  async response() {
    const { default: res } = await import('./sellerprofile.update.js');
    return res;
  },
});

// sellerId=*********&categoryId=********&search=&channel=Lazada
MockAjax.mtop({
  mtop: 'mtop.lazada.finance.sellerfund.seller.commission.query',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./finance.commission.js');
    return res({ level: params.categoryId ? '2' : '1' });
  },
});

/*********** logistic invoice number start ************/

MockAjax.mtop({
  mtop: 'mtop.lazada.seller.order.query.invoice.number',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./order.query.invoice.number.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.seller.order.update.invoice.number',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./order.update.invoice.number.js');
    return res;
  },
});

/*********** logistic invoice number end ************/

/*********** logistic holiday start ************/

// Holiday Mode for per WH
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.holiday.per',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.holiday.per.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.holiday.init',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.holiday.init.js');
    return res;
  },
});

// Holiday mode with store closure
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.holiday.all',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.holiday.all.js');
    return res;
  },
});

// Holiday mode with store open & only fulfilment extension
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.new.holiday.all',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.new.holiday.all.js');
    return res;
  },
});

// targetTable: settingAllNewWhTable/settingPerWhTable
// holidayMode: false
// code: SG101BB-WH-10003
// date: 2021-06-01 00:00:00,2021-07-01 00:00:00
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.holiday.update',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.holiday.update.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.new.holiday.check',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.new.holiday.check.js');
    return res;
  },
});

/*********** logistic holiday end ************/

/*********** logistic operating hours start ************/

// pickupStopExtId  213
// warehouseCode DEFAULT/dropshipping/SG101BB-WH-10002
// eventTarget scheduleData
// async false
MockAjax.mtop({
  mtop: 'mtop.alibaba.logistics.firstmile.operationtime.getoperationtime',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./firstmile.operationtime.getoperationtime.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.alibaba.logistics.firstmile.operationtime.saveoperationtime',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./firstmile.operationtime.saveoperationtime.js');
    return res;
  },
});

/*********** logistic operating hours end ************/

/*********** logistic delivery service start ************/

MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.cod.query',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./code.query.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.geocode.update',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./deliveryservice.geocode.update.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.deliveryservice.subscribe',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./deliveryservice.subscribe.js');
    return res;
  },
});

// the online api is using GET, let's try to use post
MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.deliveryservice.unsubscribe',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./deliveryservice.unsubscribe.js');
    return res;
  },
});

// operatorType: seller
MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.deliveryservice.query',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./deliveryservice.query.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.asc.seller.product.deliveryoptions.query',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./product.deliveryoptions.query.js');
    return res;
  },
});

/*********** logistic delivery service end ************/

/*********** logistic Shipping Provider start ************/

// TODO 页面渲染没有接口，数据都在 window.olympicsProtalModel 里面

//reason: page.common.sellerConfig.fm.3plReason.reason4 / page.common.sellerConfig.fm.3plReason.reason8
// fmAllocationId: ********
MockAjax.mock([
  {
    url: new RegExp('/seller/profile/changeShippingProvider'),
    method: 'POST',
    async response(payload) {
      const { default: res } = await import('./profile.changeShippingProvider.js');
      return res;
    },
  },
]);

/*********** logistic Shipping Provider end ************/

/*********** logistic Service area start ************/

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.init.province',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.init.province.js');
    return res;
  },
});

// divisionCode R80020282
MockAjax.mtop({
  mtop: 'mtop.global.inventory.service.area.division',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./service.area.division.js');
    return res;
  },
});

// divisionCode R80020282
// warehouseName: xxx    for search keyword
MockAjax.mtop({
  mtop: 'mtop.global.inventory.service.area.list',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./service.area.list.js');
    return res;
  },
});

// {"divisionCode":"R80020282","warehouseCodeData":"[{\"canDelete\":false,\"divisionCode\":\"R80020282\",\"priorityScore\":1,\"warehouseCode\":\"PLATFORM_WHS_PRIORITY\",\"warehouseName\":\"FBL\"},{\"canDelete\":true,\"divisionCode\":\"R80020282\",\"priorityScore\":2,\"warehouseCode\":\"dropshipping\",\"warehouseName\":\"leyoutest131-1234\"},{\"canDelete\":true,\"divisionCode\":\"R80020282\",\"priorityScore\":3,\"warehouseCode\":\"SG101BB-WH-10002\"}]","operateType":"SORT","warehouseCodeList":"[\"PLATFORM_WHS_PRIORITY\",\"SG101BB-WH-10002\",\"dropshipping\"]"}
// warehouseCode: dropshipping/xxxx
// operateType DELETE/ADD
// divisionCode R80020282
MockAjax.mtop({
  mtop: 'mtop.global.inventory.service.area.update',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./service.area.update.js');
    return res;
  },
});

// divisionCode R80020293
MockAjax.mtop({
  mtop: 'mtop.global.inventory.service.area.names',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./service.area.names.js');
    return res;
  },
});

// Remove All Configuration 好像不生效
// divisionCode R80020293
MockAjax.mtop({
  mtop: 'mtop.global.inventory.service.area.disable',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./service.area.disable.js');
    return res;
  },
});

/*********** logistic Service area end ************/

/*********** logistic warehouse start ************/

MockAjax.mtop({
  mtop: 'mtop.lazada.rc.multi.address.init',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.init2.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.init',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.init.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.names',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.names.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.rc.multi.address.warehouse.list',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.warehouse.list2.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.warehouse.list',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.warehouse.list.js');
    return res;
  },
});

// "addressId":"3878528"
MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.edit',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.edit.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.multi.address.save',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./multi.address.save.js');
    return res;
  },
});

/*********** logistic warehouse end ************/

/*********** shipping provider start ************/

MockAjax.mtop({
  mtop: 'mtop.lazada.seller.fulfillment.shipping.provider.list.get',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./shipping-provider.js');
    return res;
  },
});

/*********** shipping provider start ************/

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.sellerprofile.bpms.list',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./sellerprofile.bpms.list.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.seller.task.status',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./seller.task.status.js');
    return await new Promise(resolve => {
      setTimeout(() => {
        resolve(res);
      }, 1000);
    });
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.onboard.cb.registration.getjoingoglobal',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./registration.getjoingoglobal.js');
    return res;
  },
});
MockAjax.mtop({
  mtop: 'mtop.lazada.merchant.social.media.facebook.status.get',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./facebook.status.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.merchant.social.media.facebook.profile.get',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./facebook.profile.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.lazada.merchant.social.media.facebook.unbind',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./facebook.unbind.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.merchant.subaccount.user.no.permission.message',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./nopermission.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.seller.member.getuserinfo',
  method: 'GET',
  async response(params = {}) {
    const { default: res } = await import('./member.userInfo.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.seller.chats.app.list',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./chat.app.list.js');
    return res;
  },
});

MockAjax.mtop({
  mtop: 'mtop.global.seller.identification.verificationtoken',
  method: 'POST',
  async response(params = {}) {
    const { default: res } = await import('./verifytoken.js');
    return res;
  },
});
