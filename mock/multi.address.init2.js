export default {
  api: 'mtop.lazada.rc.multi.address.init',
  data: {
    data: {
      // 卖家是否打了多仓的标
      isMultiAddress: true,

      // 是否在白名单
      allowMultiFeature: true,

      // 是否跨境账号
      isCrossBoard: true,
      // 当前国家
      venture: 'MY',
      warehouseNameList: [
        { label: 'dropshipping', value: 'dropshipping' },
        { label: '乐游测试新增', value: 'SG101BB-WH-10002' },
        { label: '乐游新增数据2', value: 'SG101BB-WH-10003' },
        { label: '乐游新增3', value: 'SG101BB-WH-10004' },
        { label: '乐游1新增444', value: 'SG101BB-WH-10005' },
        { label: '乐游新增5555', value: 'SG101BB-WH-10006' },
        { label: '森淼测试02', value: 'SG101BB-WH-10007' },
        { label: '森淼测试03', value: 'SG101BB-WH-10008' },
        { label: '森淼测试04', value: 'SG101BB-WH-10009' },
        { label: '森淼测试06', value: 'SG101BB-WH-10010' },
        { label: '恶趣味我去饿', value: 'SG101BB-WH-10011' },
        { label: '森淼测试08', value: 'SG101BB-WH-10012' },
        { label: '森淼测试读开写开01', value: 'SG101BB-WH-10013' },
        { label: '森淼测试读开写开02', value: 'SG101BB-WH-10014' },
        { label: '森淼测试002', value: 'SG101BB-WH-10015' },
        { label: '森淼测试', value: 'SG101BB-WH-10016' },
        { label: '错的咐热', value: 'SG101BB-WH-10017' },
        { label: 'senmiao测试01读开写开', value: 'SG101BB-WH-10018' },
        { label: '森淼测试0001111', value: 'SG101BB-WH-10019' },
        { label: '21321321', value: 'SG101BB-WH-10020' },
        { label: 'の', value: 'SG101BB-WH-10021' },
        { label: 'の32131', value: 'SG101BB-WH-10022' },
        { label: '3213', value: 'SG101BB-WH-10023' },
        { label: '321321senmia', value: 'SG101BB-WH-10024' },
        { label: '3213213', value: 'SG101BB-WH-10025' },
        { label: '3213213213', value: 'SG101BB-WH-10026' },
        { label: '4234', value: 'SG101BB-WH-10027' },
        { label: '非人非', value: 'SG101BB-WH-10028' },
        { label: '得分发分威风', value: 'SG101BB-WH-10029' },
        { label: '得分发分威风的', value: 'SG101BB-WH-10030' },
        { label: '森淼测试9241', value: 'SG101BB-WH-10031' },
        { label: '森淼测试9241', value: 'SG101BB-WH-10032' },
        { label: '森淼测试9242', value: 'SG101BB-WH-10033' },
        { label: '森淼测试9243', value: 'SG101BB-WH-10034' },
        { label: 'test35', value: 'SG101BB-WH-10035' },
        { label: 'test36', value: 'SG101BB-WH-10036' },
        { label: 'test37', value: 'SG101BB-WH-10037' },
        { label: 'test38', value: 'SG101BB-WH-10038' },
        { label: 'test39', value: 'SG101BB-WH-10039' },
        { label: 'test40', value: 'SG101BB-WH-10040' },
        { label: 'test41', value: 'SG101BB-WH-10041' },
        { label: 'test42', value: 'SG101BB-WH-10042' },
        { label: 'test43', value: 'SG101BB-WH-10043' },
        { label: 'test44', value: 'SG101BB-WH-10044' },
        { label: 'test45', value: 'SG101BB-WH-10045' },
        { label: 'test46', value: 'SG101BB-WH-10046' },
        { label: 'test47', value: 'SG101BB-WH-10047' },
        { label: 'test48', value: 'SG101BB-WH-10048' },
        { label: 'test49', value: 'SG101BB-WH-10049' },
        { label: 'test50', value: 'SG101BB-WH-10050' },
        { label: '', value: 'SG101BB-WH-10051' },
        { label: '', value: 'SG101BB-WH-10052' },
        { label: '', value: 'SG101BB-WH-10053' },
        { label: '', value: 'SG101BB-WH-10054' },
        { label: '', value: 'SG101BB-WH-10055' },
        { label: '', value: 'SG101BB-WH-10056' },
        { label: '', value: 'SG101BB-WH-10057' },
        { label: '', value: 'SG101BB-WH-10058' },
        { label: '', value: 'SG101BB-WH-10059' },
        { label: '', value: 'SG101BB-WH-10060' },
        { label: '', value: 'SG101BB-WH-10061' },
        { label: '', value: 'SG101BB-WH-10062' },
        { label: '', value: 'SG101BB-WH-10063' },
      ],
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
