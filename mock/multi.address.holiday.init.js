export default {
  api: 'mtop.global.merchant.subaccount.multi.address.holiday.init',
  data: {
    data: [
      { minDate: '2021-12-03', name: 'holidayModeDate' },
      [
        { name: 'countryTab', uiType: 'Tab', value: '0', visible: false },
        { name: 'multiAddressTab', uiType: 'Tab', value: 'true', visible: true },
        { name: 'inWhiteListTab', uiType: 'Tab', value: 'true', visible: true },
        {
          name: 'secondTab',
          options: [
            {
              content: 'Seller Account',
              href: '/profile/your_profile?activeKey=0&generalKey=1',
              name: 'sellerAcount',
              target: '_self',
              uiType: 'Link',
              value: '1',
            },
            {
              content: 'Business Information',
              href: '/profile/your_profile?activeKey=0&generalKey=2',
              name: 'businessInfomation',
              target: '_self',
              uiType: 'Link',
              value: '2',
            },
            {
              content: 'Bank Account',
              href: '/profile/your_profile?activeKey=0&generalKey=5',
              name: 'bannerAccount',
              target: '_self',
              uiType: 'Link',
              value: '3',
            },
            {
              content: 'Warehouse Address',
              href: '/apps/warehouse/index?activeKey=0&secondTab=4',
              name: 'warehouse',
              target: '_self',
              uiType: 'Link',
              value: '4',
            },
            {
              content: 'Service Area',
              href: '/apps/servicearea/index?activeKey=0&secondTab=5',
              name: 'serviceArea',
              target: '_self',
              uiType: 'Link',
              value: '5',
            },
            {
              content: 'Store List',
              href: '/apps/account/profile/store',
              name: 'storeList',
              target: '_self',
              uiType: 'Link',
              value: '7',
            },
            {
              content: 'Operating Hours',
              href: '/apps/account/profile/schedule',
              name: 'operationHours',
              target: '_self',
              uiType: 'Link',
              value: '6',
            },
          ],
        },
        {
          name: 'groupId',
          options: [
            { label: 'Seller Account', name: 'sellerAccount', target: '_self', uiType: 'Link', value: '1' },
            { label: 'Business Information', name: 'businessInfomation', target: '_self', uiType: 'Link', value: '2' },
            { label: 'Bank Account', name: 'bannerAccount', target: '_self', uiType: 'Link', value: '5' },
            {
              href: '/apps/warehouse/index?activeKey=0&secondTab=4',
              label: 'Warehouse Address',
              name: 'warehouse',
              target: '_self',
              uiType: 'Link',
              value: 'secondTab4',
            },
            {
              href: '/apps/servicearea/index?activeKey=0&secondTab=5',
              label: 'Service Area',
              name: 'serviceArea',
              target: '_self',
              uiType: 'Link',
              value: 'secondTab5',
            },
            {
              href: '/apps/account/profile/store',
              label: 'Store List',
              name: 'storeList',
              target: '_self',
              uiType: 'Link',
              value: '7',
            },
            {
              href: '/apps/account/profile/schedule',
              label: 'Operating Hours',
              name: 'operationHours',
              target: '_self',
              uiType: 'Link',
              value: '6',
            },
          ],
        },
        { extData: {}, name: 'allPerTableContainer', type: 'CommonContainer', visible: true },
        { extData: {}, name: 'newAllWhTableContainer', type: 'CommonContainer', visible: true },
        { href: 'https://www.lazada.sg', name: 'instructionsBtn', target: '_blank', uiType: 'Link' },
      ],
    ],
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
