export default {
  api: 'mtop.global.merchant.subaccount.profile.render.lazada',
  data: {
    data: {
      fields: [
        {
          disabled: true,
          history: false,
          label: 'Seller Type',
          name: '0',
          needOtp: false,
          needQc: false,
          uiType: 'Input',
          value: 'Individual',
        },
        {
          disabled: false,
          fieldName: 'individual_name_company',
          history: true,
          label: 'Legal Name / Company Name',
          name: '441',
          needOtp: false,
          needQc: true,
          required: false,
          uiType: 'Input',
          value: 'CÔNG TY TNHH MỘT THÀNH VIÊN ABC',
        },
        {
          disabled: false,
          fieldName: 'individual_postcode',
          history: true,
          label: 'Postal Code',
          name: '447',
          needOtp: false,
          needQc: false,
          required: false,
          tips: 'This should match the details stated in the ID card (if any)',
          uiType: 'Input',
          value: 'SaiGon Centre',
        },
        {
          disabled: false,
          fieldName: 'individual_business_reg_number',
          history: true,
          label: 'Business Registration Number',
          name: '450',
          needOtp: false,
          needQc: false,
          required: false,
          uiType: 'Input',
          value: '0987654321',
        },
        {
          dataSource: [
            {
              label: 'Yes',
              value: '1',
            },
          ],
          disabled: false,
          fieldName: 'individual_vat_registered',
          history: true,
          label: 'VAT Enterprise',
          name: '38',
          needOtp: false,
          needQc: false,
          required: true,
          uiType: 'RadioGroup',
          value: '0',
        },
        {
          disabled: false,
          fieldName: 'individual_seller_first_name',
          history: true,
          label: 'First Name',
          name: '503',
          needOtp: false,
          needQc: false,
          required: true,
          tips: 'The operational PIC name.',
          uiType: 'Input',
          value: 'NGUYỄN VĂN HOÀNG1',
        },
        {
          dataSource: [
            {
              label: 'Personal ID',
              value: 'Personal ID',
            },
            {
              label: 'Passport',
              value: 'Passport',
            },
          ],
          disabled: false,
          fieldName: 'individual_identification_type',
          history: true,
          label: 'ID Type',
          name: '634',
          needOtp: false,
          needQc: true,
          priority: 2,
          required: true,
          uiType: 'Select',
          value: 'Personal ID',
        },
        {
          disabled: false,
          fieldName: 'individual_citizen_identification_number',
          history: true,
          label: 'ID/Passport number',
          name: '540',
          needOtp: false,
          needQc: true,
          priority: 3,
          required: true,
          tips: 'This should match the details stated in the ID card (if any)',
          uiType: 'Input',
          value: '12345678',
        },
        {
          accept:
            'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          disabled: false,
          fieldName: 'individual_document_business_info',
          history: true,
          label: 'Upload ID - Front Side',
          limit: 1,
          maxSize: 5242880,
          name: '451',
          needOtp: false,
          needQc: true,
          priority: 4,
          required: true,
          tips: 'Allows image files, PDF and MS word',
          uiType: 'Upload',
          value: {
            downloadUrl: '',
            mime: 'image/png',
            name: 'TEST.png',
            uploadId: '*********',
          },
        },
        {
          accept:
            'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          disabled: false,
          fieldName: 'individual_document_business_info_2',
          history: true,
          label: 'Upload ID - Back Side',
          limit: 1,
          maxSize: 5242880,
          name: '633',
          needOtp: false,
          needQc: true,
          priority: 5,
          required: true,
          tips: 'explanation to seller',
          uiType: 'Upload',
          value: {
            downloadUrl: '',
            mime: 'image/png',
            name: 'TEST.png',
            uploadId: '*********',
          },
        },
        {
          disabled: false,
          fieldName: 'individual_vat_number',
          history: true,
          label: 'Tax Registration Number',
          name: '475',
          needOtp: false,
          needQc: false,
          priority: 6,
          required: false,
          uiType: 'Input',
          value: '*********',
        },
        {
          dataSource: [
            {
              label: 'Vietnam',
              value: 'VN',
            },
          ],
          disabled: false,
          fieldName: 'individual_country',
          history: true,
          label: 'Country/ Region',
          name: '446',
          needOtp: false,
          needQc: true,
          priority: 8,
          required: true,
          tips: 'This should match the details stated in the ID card (if any)',
          uiType: 'Select',
          value: 'VN',
        },
        {
          dataSource: [
            {
              label: 'Lai Châu',
              value: '4386',
            },
            {
              label: 'Lào Cai',
              value: '4387',
            },
            {
              label: 'Hà Giang',
              value: '4388',
            },
            {
              label: 'Cao Bằng',
              value: '4389',
            },
            {
              label: 'Sơn La',
              value: '4390',
            },
            {
              label: 'Yên Bái',
              value: '4391',
            },
            {
              label: 'Tuyên Quang',
              value: '4392',
            },
            {
              label: 'Lạng Sơn',
              value: '4393',
            },
            {
              label: 'Quảng Ninh',
              value: '4394',
            },
            {
              label: 'Hòa Bình',
              value: '4395',
            },
            {
              label: 'Hà Tây',
              value: '4396',
            },
            {
              label: 'Ninh Bình',
              value: '4397',
            },
            {
              label: 'Thái Bình',
              value: '4398',
            },
            {
              label: 'Thanh Hóa',
              value: '4399',
            },
            {
              label: 'Nghệ An',
              value: '4400',
            },
            {
              label: 'Hà Tĩnh',
              value: '4401',
            },
            {
              label: 'Quảng Bình',
              value: '4402',
            },
            {
              label: 'Quảng Trị',
              value: '4403',
            },
            {
              label: 'Thừa Thiên - Huế',
              value: '4404',
            },
            {
              label: 'Quảng Nam',
              value: '4405',
            },
            {
              label: 'Kon Tum',
              value: '4406',
            },
            {
              label: 'Quảng Ngãi',
              value: '4407',
            },
            {
              label: 'Gia Lai',
              value: '4408',
            },
            {
              label: 'Bình Định',
              value: '4409',
            },
            {
              label: 'Phú Yên',
              value: '4410',
            },
            {
              label: 'Đắk Lắk',
              value: '4411',
            },
            {
              label: 'Khánh Hòa',
              value: '4412',
            },
            {
              label: 'Lâm Đồng',
              value: '4413',
            },
            {
              label: 'Ninh Thuận',
              value: '4414',
            },
            {
              label: 'Tây Ninh',
              value: '4415',
            },
            {
              label: 'Đồng Nai',
              value: '4416',
            },
            {
              label: 'Bình Thuận',
              value: '4417',
            },
            {
              label: 'Long An',
              value: '4418',
            },
            {
              label: 'Bà Rịa - Vũng Tàu',
              value: '4419',
            },
            {
              label: 'An Giang',
              value: '4420',
            },
            {
              label: 'Đồng Tháp',
              value: '4421',
            },
            {
              label: 'Tiền Giang',
              value: '4422',
            },
            {
              label: 'Kiên Giang',
              value: '4423',
            },
            {
              label: 'Cần Thơ',
              value: '4424',
            },
            {
              label: 'Vĩnh Long',
              value: '4425',
            },
            {
              label: 'Bến Tre',
              value: '4426',
            },
            {
              label: 'Trà Vinh',
              value: '4427',
            },
            {
              label: 'Sóc Trăng',
              value: '4428',
            },
            {
              label: 'Bắc Kạn',
              value: '4429',
            },
            {
              label: 'Bắc Giang',
              value: '4430',
            },
            {
              label: 'Bạc Liêu',
              value: '4431',
            },
            {
              label: 'Bắc Ninh',
              value: '4432',
            },
            {
              label: 'Bình Dương',
              value: '4433',
            },
            {
              label: 'Bình Phước',
              value: '4434',
            },
            {
              label: 'Cà Mau',
              value: '4435',
            },
            {
              label: 'Đà Nẵng',
              value: '4436',
            },
            {
              label: 'Hải Dương',
              value: '4437',
            },
            {
              label: 'Hải Phòng',
              value: '4438',
            },
            {
              label: 'Hà Nam',
              value: '4439',
            },
            {
              label: 'Hà Nội',
              value: '4440',
            },
            {
              label: 'Hồ Chí Minh',
              value: '4441',
            },
            {
              label: 'Hưng Yên',
              value: '4442',
            },
            {
              label: 'Nam Định',
              value: '4443',
            },
            {
              label: 'Phú Thọ',
              value: '4444',
            },
            {
              label: 'Thái Nguyên',
              value: '4445',
            },
            {
              label: 'Vĩnh Phúc',
              value: '4446',
            },
            {
              label: 'Điện Biên',
              value: '4447',
            },
            {
              label: 'Đắk Nông',
              value: '4448',
            },
            {
              label: 'Hậu Giang',
              value: '4449',
            },
          ],
          disabled: false,
          fieldName: 'individual_country_region',
          history: true,
          label: 'Country Region',
          name: '576',
          needOtp: false,
          needQc: false,
          priority: 9,
          required: false,
          tips: 'This should match the details stated in the ID card (if any)',
          uiType: 'Select',
          value: 4386,
        },
        {
          dataSource: [],
          disabled: false,
          fieldName: 'individual_location_level_1',
          history: false,
          iso: 'VN',
          label: 'Address',
          name: '558',
          needOtp: false,
          needQc: false,
          priority: 10,
          required: true,
          uiType: 'Address',
          value: ['R1873533', 'R7141890', 'R80001218'],
        },
        {
          disabled: false,
          fieldName: 'individual_address1',
          history: true,
          label: 'Địa chỉ riêng',
          name: '443',
          needOtp: false,
          needQc: false,
          priority: 13,
          required: true,
          tips: 'This should match the details stated in the ID card (if any)',
          uiType: 'Input',
          value: '13 NGUYỄN THỊ THẬP, PHƯỜNG BÌNH THUẬN, QUẬN 7, TPHCM',
        },
      ],
      globalDisabled: false,
    },
  },
  ret: ['SUCCESS::success'],
  v: '1.0',
};
