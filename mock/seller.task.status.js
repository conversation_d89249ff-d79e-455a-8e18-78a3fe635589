export default {
  api: 'mtop.global.merchant.subaccount.seller.task.status',
  data: {
    data: {
      profileStrength: '33%',
      taskList: [
        {
          isBusinessExperience: false,
          taskCode: 'WAREHOUSE',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/todo/detail/address',
          taskName: 'Warehouse Info',
          taskPriority: 1,
          taskStatus: 'done',
          visible: true,
        },
        {
          fieldNames: 'business_reg_number,individual_citizen_identification_number',
          isBusinessExperience: false,
          taskCode: 'BUSINESS_INFO',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/business',
          taskName: 'Business Info',
          taskPriority: 2,
          taskStatus: 'done',
          visible: true,
        },
        {
          fieldNames: 'bank_account_nr,individual_bank_account_nr',
          isBusinessExperience: false,
          taskCode: 'BANK_INFO',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/bank',
          taskName: 'Bank Info',
          taskPriority: 3,
          taskStatus: 'done',
          visible: true,
        },
        {
          desc: 'Online store experience',
          isBusinessExperience: true,
          taskCode: 'ONLINE_STORE',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=ONLINE_STORE',
          taskName: 'Online store experience',
          taskPriority: 4,
          taskStatus: 'undone',
          visible: true,
        },
        {
          desc: 'Offline store experience',
          isBusinessExperience: true,
          taskCode: 'OFFLINE_STORE',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=OFFLINE_STORE',
          taskName: 'Offline store experience',
          taskPriority: 5,
          taskStatus: 'undone',
          visible: true,
        },
        {
          desc: 'Supply Chain',
          isBusinessExperience: true,
          taskCode: 'SUPPLY_CHAIN',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=SUPPLY_CHAIN',
          taskName: 'Supply Chain',
          taskPriority: 6,
          taskStatus: 'undone',
          visible: true,
        },
        {
          desc: 'Social Media',
          isBusinessExperience: true,
          taskCode: 'SOCIAL_MEDIA',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=SOCIAL_MEDIA',
          taskName: 'Social Media',
          taskPriority: 7,
          taskStatus: 'undone',
          visible: true,
        },
        {
          desc: 'Lazada Store Plan',
          isBusinessExperience: true,
          taskCode: 'STORE_MANAGEMENT',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=STORE_MANAGEMENT',
          taskName: 'Lazada Store Plan',
          taskPriority: 8,
          taskStatus: 'undone',
          visible: true,
        },
        {
          desc: 'Brand Info',
          isBusinessExperience: true,
          taskCode: 'BRAND_INFO',
          taskLink: 'https://sellercenter-staging.lazada.sg/apps/setting/experience?formCode=BRAND_INFO',
          taskName: 'Brand Info',
          taskPriority: 9,
          taskStatus: 'undone',
          visible: true,
        },
      ],
      warningMsg:
        '30% sellers register in same day with you has finished all the profile information and get professional ops now',
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
