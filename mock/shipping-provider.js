export default {
  api: 'mtop.lazada.seller.fulfillment.shipping.provider.list.get',
  data: {
    repeated: 'false',
    module: {
      shippingDataList: [
        {
          spillShippingProviderName:
            'PICKUPP  SG, UrbanFoxSG, SpeedPost, SimplyPost, UrbanFoxSG, uParcel SG, SpeedPost, Singpost Postal Mail, The Lorry SG, SpeedPost, SimplyPost, CJSG, CJSG, Grab SG, PICKUPP  SG, UrbanFoxSG, SpeedPost, SimplyPost, UrbanFoxSG, uParcel SG, SpeedPost, Singpost Postal Mail, The Lorry SG, SpeedPost, SimplyPost, CJSG, CJSG, Grab SG',
          isBlackPeriod: true,
          blackOutReason: '12.12 Campaign Config Freeze 2021 (Regional)',
          pickUpType: 'Drop-off',
          fmAllocationId: 5819674,
          dopLink:
            'https://www.ninjavan.co/en-sg/ninja-points?shippingProviderCode=0e2b7f50-2124-481f-b1c3-d1db0875f175&addressId=R80026916',
          shippingProviderName: 'NinjaVanSG',
          migrationType: 'Default',
          blackDate: '02-12-2021 00:00:00-27-12-2021 00:00:00',
          warehouseCode: 'dropshipping',
          solutionCode: 'LAZADA_STANDARD_SG',
          '3pl': true,
        },
        {
          spillShippingProviderName:
            'PICKUPP  SG, UrbanFoxSG, SpeedPost, SimplyPost, UrbanFoxSG, uParcel SG, SpeedPost, NinjaVanSG, The Lorry SG, SpeedPost, SimplyPost, CJSG, CJSG, NinjaVanSG, Grab SG, PICKUPP  SG, UrbanFoxSG, SpeedPost, SimplyPost, UrbanFoxSG, uParcel SG, SpeedPost, NinjaVanSG, The Lorry SG, SpeedPost, SimplyPost, CJSG, CJSG, NinjaVanSG, Grab SG',
          isBlackPeriod: true,
          blackOutReason: '12.12 Campaign Config Freeze 2021 (Regional)',
          pickUpType: 'Drop-off',
          fmAllocationId: 7804453,
          dopLink: null,
          shippingProviderName: 'Singpost Postal Mail',
          migrationType: 'Default',
          blackDate: '02-12-2021 00:00:00-27-12-2021 00:00:00',
          warehouseCode: 'dropshipping',
          solutionCode: 'LAZADA_ECONOMY_SG',
          '3pl': true,
        },
      ],
      'changeReasonList': [
        {
          reasonKey: 'page.common.sellerConfig.fm.3plReason.reason4',
          reasonValue: 'Drop-off point location is inconvenient',
          reasonDesc:
            'Kindly note the following before proceeding to submit this request:<br/>\n<br/>\n\n<ul style="list-style-type:disc">\n\n<li>A period of 2 months needs to be observed between changes of 3PL request else your request will be rejected.</br></li>\n<li>You can check out the list of drop off points next to you by clicking <a href="https://www.ninjavan.co/en-sg/ninja-points#find-a-ninja-point">here</a>. Lazada will check and verify the requests prior to approval. Please note that if approved, you will be permanently moved to pickups and Lazada will allocate the best possible 3PL partner for you.</br></li>\n<li>Standard service 3PL partners will only pickup parcels within 30 kg and 150 cm dimension on any side.</br></li>\n\n</ul>',
        },
        {
          reasonKey: 'page.common.sellerConfig.fm.3plReason.reason5',
          reasonValue: 'Unsatisfied with service - ops related (damages/lost)',
          reasonDesc:
            '<p>We are sorry for the inconvenience caused. Kindly note the following before proceeding to submit this request.</p>\n<ul>\n\n<li>To ensure smooth operations, <strong>we do not allow a change of 3PL partners</strong>. If you had a bad experience, you can share your feedback with us <a href="/xiniu/survey/nh8pX7rEM"><strong>here</strong></a>. We will bring your feedback to the 3PL to improve their service with you.</br></li>\n\n<li><strong>Any requests submitted will be rejected.</strong></br></li>\n\n<li>Lazada will allocate the best possible 3PL partner for you based on your volume and location. Lazada reserves the right, in its sole discretion, to use any method or route to perform the Logistics Services, including to sub-contract all or part of the Logistics Services, and to use any sub-contractor which Lazada deems appropriate.<br>(Click <a href="https://university.lazada.sg/course/learn?id=1562&type=article">here</a> to see marketplace agreement)</br></li>\n\n\n</ul>',
        },
        {
          reasonKey: 'page.common.sellerConfig.fm.3plReason.reason7',
          reasonValue: 'Change from Pick-up to Drop-off',
          reasonDesc:
            'Kindly note the following before proceeding to submit this request:<br/>\n<br/>\n\n<ul style="list-style-type:disc">\n\n<li>A period of 2 months needs to be observed between changes of 3PL request else your request will be rejected.</li></br>\n<li>You may click <a href="https://www.ninjavan.co/en-sg/ninja-points#find-a-ninja-point">here</a> and check the list of drop off points to see if there are any convenient drop off points located next to you.</li></br>\n<li>There is a drop off point limitation of 5KG and sum of dimensions have to be <=80cm. If most of your parcels do not fit the drop off point criteria, we recommend that you stay on pickup for your own convenience.</li></br>\n\n</ul>',
        },
        {
          reasonKey: 'page.common.sellerConfig.fm.3plReason.reason8',
          reasonValue: 'Change from Drop-off to Pick-up',
          reasonDesc:
            'Kindly note the following before proceeding to submit this request:<br/>\n<br/>\n\n<ul style="list-style-type:disc">\n\n<li>A period of 2 months needs to be observed between changes of 3PL request else your request will be rejected.</li></br>\n<li>Standard service 3PL partners will only pickup parcels within 30 kg and 150 cm dimension on any side.</li></br>\n<li>Please note that Lazada will allocate the best possible 3PL partner for you.</li></br>\n\n<br/>\nLazada reserves the right, in its sole discretion, to use any method or route to perform the Logistics Services, including to sub-contract all or part of the Logistics Services, and to use any sub-contractor which Lazada deems appropriate .<br/>\n\n<br/>\n\nClick <a href="https://university.lazada.sg/course/learn?spm=lzd-university-sg-pc.lzd-university-sg-pc-list.courserlist_courses.1.518a49844j9NtY&id=1562&type=article">here</a> to see marketplace agreement.\n\n</ul>',
        },
      ],
      historyDataList: [
        {
          reqTimeStamp: '06-12-2021 11:00:02',
          reason: '-',
          actionStatus: 'Effective',
          beforeShippingProviderName: 'NinjaVanSG',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'NinjaVanSG',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
        {
          reqTimeStamp: '03-12-2021 10:31:41',
          reason: 'litian-test',
          actionStatus: 'Pre-Effective',
          beforeShippingProviderName: 'NinjaVanSG',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'NinjaVanSG',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
        {
          reqTimeStamp: '18-10-2021 14:13:36',
          reason: '-',
          actionStatus: 'Pending',
          beforeShippingProviderName: 'NinjaVanSG',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'NinjaVanSG',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
        {
          reqTimeStamp: '18-10-2021 14:13:35',
          reason: '-',
          actionStatus: 'Pending',
          beforeShippingProviderName: 'Singpost Postal Mail',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'Singpost Postal Mail',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
        {
          reqTimeStamp: '14-10-2021 16:58:55',
          reason: '-',
          actionStatus: 'Pending',
          beforeShippingProviderName: 'NinjaVanSG',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'NinjaVanSG',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
        {
          reqTimeStamp: '14-10-2021 16:58:55',
          reason: '-',
          actionStatus: 'Pending',
          beforeShippingProviderName: 'Singpost Postal Mail',
          pickUpType: 'Drop-off',
          afterShippingProviderName: 'Singpost Postal Mail',
          migrationType: 'Default',
          warehouseCode: 'dropshipping',
        },
      ],
    },
    retry: 'false',
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
