export default {
  api: 'mtop.lazada.asc.seller.deliveryservice.subscribe',
  data: {
    data: [
      {
        eventType: 'patchSchema',
        patchs: [
          {
            dataSource: [
              {
                blockSubscriptionText: "Default Logistics Service Can't be Unsubscribed!",
                reasonOptions: [],
                serviceName: 'Lazada Standard',
                solutionCode: 'LAZADA_STANDARD_SG',
                statusCode: 1,
                statusText: 'Subscribed',
                validateGeoCodes: false,
                warehouseId: 'dropshipping',
              },
              {
                reasonOptions: [
                  {
                    label: 'Products not suitable for the delivery type',
                    value: 'Products not suitable for the delivery type',
                  },
                  { label: 'Moving to LazMall', value: 'Moving to LazMall' },
                  { label: 'Others (please describe)', value: 'other' },
                ],
                serviceName: 'Lazada Economy',
                solutionCode: 'LAZADA_ECONOMY_SG',
                statusCode: 1,
                statusText: 'Subscribed',
                validateGeoCodes: false,
                warehouseId: 'dropshipping',
              },
            ],
            name: 'table',
          },
        ],
      },
    ],
    type: 'actions',
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
