{"data": {"shippingData": [{"spillShippingProviderName": null, "isBlackPeriod": false, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 2702736, "shippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "dropshipping", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12747901, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10002", "solutionCode": "LAZADA_STANDARD_SG", "3pl": false}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12747908, "shippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10003", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12747919, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10004", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12747921, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10005", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12747925, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10006", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984302, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10007", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984318, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10008", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984360, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10010", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984695, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10013", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984796, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10016", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984970, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10017", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12984984, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10018", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12985068, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10021", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12985164, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10023", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12985229, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10025", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12985234, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10026", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12986322, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10027", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12986327, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10028", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 12986394, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10029", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 14916814, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10032", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 14916828, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10033", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16845656, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10035", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16846693, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10036", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848378, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10041", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848390, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10042", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848477, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10044", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848505, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10045", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848514, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10046", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848560, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10047", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16848572, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10048", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 16849259, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10050", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}, {"spillShippingProviderName": null, "isBlackPeriod": true, "blackOutReason": "12.12 Campaign Config Freeze 2021 (Regional)", "pickUpType": "Pick-up", "fmAllocationId": 46670345, "shippingProviderName": null, "migrationType": "<PERSON><PERSON><PERSON>", "blackDate": "02-12-2021 00:00:00-27-12-2021 00:00:00", "warehouseCode": "SG101BB-WH-10011", "solutionCode": "LAZADA_STANDARD_SG", "3pl": true}], "3plReason": [{"reasonKey": "page.common.sellerConfig.fm.3plReason.reason4", "reasonValue": "接收站太远了", "reasonDesc": "<br />\n如果你目前的物流公司接收站 (DOP) 距离仓库地址太远，请选择此原因。请注意，你可以前往指定物流公司的任何一间接收站交递包裹。<br /><br />\n\n条件：<br />\n1. 距离你仓库地址最靠近的指定物流公司接收站超过了5公里；<br /><br />\n\n找出最靠近的接收站:<br />\na. LEX：点击<a href=\"https://map-my.lel.asia\" target=\"_blank\">链接</a>并输入你的仓库地址 <br />\nb. Non-LEX：请前往有关物流公司的网站查看更多信息<br />"}, {"reasonKey": "page.common.sellerConfig.fm.3plReason.reason5", "reasonValue": "Unsatisfied with service - Ops related", "reasonDesc": "如果你目前的物流公司为Non-LEX，但你想要开始使用LEX服务，请选择此原因。</br></br>\n\n条件： </br>\n1. 单件包裹必须不超过20公斤；长/宽/高度不超过70cm (非大型包裹)；</br>\n2. 仓库地址位于LEX覆盖范围内；</br>\n3. 距离仓库地址的5公里内至少有一间LEX接收站 (DOP) *</br>\n\n*如果商家愿意前往超过5公里外的LEX接收站 (DOP) 交递包裹，也可以提交申请</br></br>\n\n提交前注意事项：</br>\na. 确保已经更新仓库地址； </br>\nb. DOP地址不应该是你的仓库地址，因为如果送货失败，包裹将会被退还到你的注册地址； </br>\nc. 如果你的每日平均包裹数量达到15件 (同一个仓库地址)，LEX将会为你安排上门取件服务。至于每日平均包裹数量没有达到15件的商家，则必须将包裹交递到最靠近的接收站 (DOP)。"}, {"reasonKey": "page.common.sellerConfig.fm.3plReason.reason7", "reasonValue": "上门取件更改为交递到接收站", "reasonDesc": "<br />\n我已经阅读并明白物流更改申请的条件，同时按照我目前的情况选择了最佳原因。我明白且确认：<br />\n1. 我的包裹是非大型包裹 (单件不超过20公斤；长/宽/高度不超过70公分)；<br />\n2. 在距离我仓库地址的5公里内有至少一间指定物流公司的接收站；<br />\n\na. 对于LEX，请在<a href=\"https://map-my.lel.asia\" target=\"_blank\">此处</a>上查看地图。<br />\nb. 对于non-LEX，请访问运输提供商的网站以获取更多信息<br /> <br />\n\n如果您在LEX DOP范围之内，则可能会被分配给LEX <br />"}, {"reasonKey": "page.common.sellerConfig.fm.3plReason.reason8", "reasonValue": "交递到接收站更改为上门取件", "reasonDesc": "<br/>\n如果你目前是将包裹交递到接收站，但想要使用上门取件服务，请选择此原因。注：所有大型包裹都会有直接上门取件的服务，无论包裹数量多少。<br/><br/>\n\n条件：<br/>\n1. Marketplace: 你在连续4个星期内的每日平均包裹数量至少为10件. Lazmall: 你在连续4个星期内的每日平均包裹数量至少为6件；<br/>\n2. 你的包裹是非大型包裹，即单件不超过20公斤；长/宽/高度不超过70公分。<br/>"}], "requestHisData": [{"reqTimeStamp": "03-12-2021 12:44:53", "reason": "Your request can not be accomodated this time.", "actionStatus": "Rejected", "beforeShippingProviderName": "NinjaVanSG", "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "dropshipping"}, {"reqTimeStamp": "03-12-2021 12:39:07", "reason": "Your request can not be accomodated this time.", "actionStatus": "Rejected", "beforeShippingProviderName": "NinjaVanSG", "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10003"}, {"reqTimeStamp": "01-12-2021 11:20:25", "reason": "交递到接收站更改为上门取件", "actionStatus": "Pending", "beforeShippingProviderName": "NinjaVanSG", "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10003"}, {"reqTimeStamp": "29-11-2021 14:00:08", "reason": "-", "actionStatus": "Effective", "beforeShippingProviderName": null, "pickUpType": "Pick-up", "afterShippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10003"}, {"reqTimeStamp": "26-11-2021 13:43:40", "reason": "-", "actionStatus": "Pre-Effective", "beforeShippingProviderName": null, "pickUpType": "Pick-up", "afterShippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10003"}, {"reqTimeStamp": "26-11-2021 13:43:40", "reason": "交递到接收站更改为上门取件", "actionStatus": "Pending", "beforeShippingProviderName": null, "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10003"}, {"reqTimeStamp": "26-11-2021 13:42:50", "reason": "接收站太远了", "actionStatus": "Pending", "beforeShippingProviderName": null, "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10002"}, {"reqTimeStamp": "26-11-2021 13:25:59", "reason": "接收站太远了", "actionStatus": "Pending", "beforeShippingProviderName": "NinjaVanSG", "pickUpType": "Pick-up", "afterShippingProviderName": "-", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "dropshipping"}, {"reqTimeStamp": "11-10-2021 11:22:26", "reason": "-", "actionStatus": "Pending", "beforeShippingProviderName": null, "pickUpType": "Drop-off", "afterShippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10011"}, {"reqTimeStamp": "25-09-2021 18:51:41", "reason": "-", "actionStatus": "Pending", "beforeShippingProviderName": null, "pickUpType": "Drop-off", "afterShippingProviderName": "NinjaVanSG", "migrationType": "<PERSON><PERSON><PERSON>", "warehouseCode": "SG101BB-WH-10050"}]}, "success": true, "errorCode": null, "errorMsg": null, "msgCode": null, "msgInfo": null}