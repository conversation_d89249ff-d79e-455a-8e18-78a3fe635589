export default {
  api: 'mtop.global.merchant.subaccount.sellerprofile.render',
  data: {
    data: {
      globalDisabled: false,
      fields: [
        {
          label: 'ID Seller',
          fieldName: 'seller_id',
          name: '0',
          require: true,
          uiType: 'Input',
          value: 'IDJ2DOSW',
          tips: '',
          history: true,
          disabled: true,
          needOtp: false,
        },
        {
          label: 'First and Last Name111',
          fieldName: 'name',
          name: '1',
          require: true,
          uiType: 'Input',
          value: '<PERSON>',
          tips: 'The operational PIC name.',
          history: true,
          disabled: false,
          needOtp: true,
        },
        {
          label: 'Contact Email address',
          fieldName: 'email',
          name: '2',
          require: true,
          uiType: 'ContactText',
          value: '<EMAIL>',
          tips: "The operational PIC's primary email address. <PERSON><PERSON><PERSON> will use this email address to stay in touch",
          history: true,
          disabled: false,
          needOtp: false,
        },
        {
          label: 'Contact Mobile Phone Number',
          fieldName: 'phone',
          name: '3',
          require: true,
          uiType: 'ContactText',
          prefix: '+62',
          value: '**********',
          tips:
            '\u003Cp\u003EContact Mobile Phone Number is used by <PERSON><PERSON><PERSON> to stay in touch, it is the Operational&nbsp;PIC\'s primary phone.\u003Cbr\u003ELogin Phone is used for authentication purposes and can be changed under\u003Cem\u003E "Profile /Account Settings"\u003C/em\u003E\u003C/p\u003E',
          history: true,
          disabled: false,
          needOtp: false,
        },
        {
          label: 'Display Name / Shop Name',
          fieldName: 'shop_name',
          name: '4',
          require: true,
          uiType: 'ShopNameText',
          prefix: '',
          value: 'Lazada Retail Test Seller',
          tips: 'This name will be visible on seller center and to the buyers on the website',
          history: true,
          disabled: false,
          needOtp: false,
        },
        {
          disabled: false,
          fieldName: 'other_contact_email',
          history: true,
          label: 'Other Contact Email address',
          name: '2',
          needOtp: false,
          needQc: false,
          priority: 3,
          required: true,
          tips: 'The email address of the person in charge. Miravia will use this email address to stay in touch',
          uiType: 'ContactTextList',
          value: '[{"email":"<EMAIL>"},{"email": "<EMAIL>"}]',
        },
        {
          disabled: false,
          fieldName: 'other_contact_phone',
          history: true,
          label: 'Other Contact Mobile Phone Number',
          name: '10',
          needOtp: false,
          needQc: false,
          prefix: '+65',
          priority: 4,
          required: false,
          tips:
            '<p>Contact Mobile Phone Number is used by Lazada to stay in touch, it is the phone of the person in charge. <br>Login Phone is used for authentication purposes and can be changed under<em> "Profile /Account Settings"</em></p>',
          uiType: 'ContactTextList',
          value:
            '[{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********},{"phone":***********}]',
        },
      ],
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
