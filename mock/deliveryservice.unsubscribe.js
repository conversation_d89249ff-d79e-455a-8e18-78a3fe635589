export default {
  api: 'mtop.lazada.asc.seller.deliveryservice.unsubscribe',
  data: {
    data: [
      {
        eventType: 'patchSchema',
        patchs: [
          {
            dataSource: [
              {
                blockSubscriptionText: "Default Logistics Service Can't be Unsubscribed!",
                reasonOptions: [],
                serviceName: 'Lazada Standard',
                solutionCode: 'LAZADA_STANDARD_SG',
                statusCode: 1,
                statusText: 'Subscribed',
                validateGeoCodes: false,
                warehouseId: 'dropshipping',
              },
              {
                reasonOptions: [
                  {
                    label: 'Products not suitable for the delivery type',
                    value: 'Products not suitable for the delivery type',
                  },
                  { label: 'Moving to LazMall', value: 'Moving to LazMall' },
                  { label: 'Others (please describe)', value: 'other' },
                ],
                serviceName: 'Lazada Economy',
                solutionCode: 'LAZADA_ECONOMY_SG',
                statusCode: 0,
                statusText: 'Unsubscribed',
                validateGeoCodes: false,
                warehouseId: 'dropshipping',
              },
              {
                blockSubscriptionText: "Default Logistics Service Can't be Unsubscribed!",
                reasonOptions: [],
                serviceName: 'Lazada Standard',
                solutionCode: 'LAZADA_STANDARD_SG',
                statusCode: 1,
                statusText: 'Subscribed',
                validateGeoCodes: false,
                warehouseId: 'SG101BB-WH-10002',
              },
              {
                reasonOptions: [
                  {
                    label: 'Products not suitable for the delivery type',
                    value: 'Products not suitable for the delivery type',
                  },
                  { label: 'Moving to LazMall', value: 'Moving to LazMall' },
                  { label: 'Others (please describe)', value: 'other' },
                ],
                serviceName: 'Lazada Instant',
                solutionCode: 'LAZADA_INSTANT_SG',
                statusCode: 0,
                statusText: 'Unsubscribed',
                validateGeoCodes: true,
                warehouseId: 'SG101BB-WH-10002',
              },
            ],
            name: 'table',
          },
        ],
      },
    ],
    type: 'actions',
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
