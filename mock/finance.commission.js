export default ({ level = '1' }) => {
  const ran = Math.ceil(Math.random(), 10000);
  return {
    api: 'mtop.lazada.finance.sellerfund.seller.commission.query',
    data: {
      success: true,
      message: 'xxxxx',
      data: [
        { categoryId: '10000335' + ran, categoryName: 'Bedding & Bath' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '10000336' + ran,
          categoryName: 'Furniture & Organization' + ran,
          level,
          resultingCommission: '5.00',
        },
        {
          categoryId: '10000337' + ran,
          categoryName: 'Tools & Home Improvement' + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '10000338' + ran, categoryName: 'LandC Equipment' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10000339' + ran, categoryName: 'Kitchen & Dining' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10000340' + ran, categoryName: 'Stationery & Craft' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '10002958' + ran,
          categoryName: 'Free Sample (Flexi Combo)' + ran,
          level,
          resultingCommission: '5.00',
        },
        {
          categoryId: '10002959' + ran,
          categoryName: 'Special Digital Products' + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '10002964' + ran, categoryName: 'Service Product' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10003007' + ran, categoryName: 'Surprise Box' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100047' + ran, categoryName: 'Services' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100058' + ran, categoryName: 'Digital Utilities' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100083' + ran, categoryName: 'Lighting & Décor' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100245' + ran, categoryName: 'Outdoor & Garden' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100380' + ran, categoryName: 'Monitors & Printers' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100386' + ran, categoryName: 'Data Storage' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100387' + ran, categoryName: 'Audio' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100412' + ran, categoryName: 'Smart Devices' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100539' + ran, categoryName: 'Household Supplies' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '10100758' + ran,
          categoryName: "Men's Shoes and Clothing" + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '10100869' + ran, categoryName: 'Health' + ran, level, resultingCommission: '5.00' },
        { categoryId: '10100871' + ran, categoryName: 'Large Appliances' + ran, level, resultingCommission: '3.00' },
        { categoryId: '10282' + ran, categoryName: 'Pet Supplies' + ran, level, resultingCommission: '5.00' },
        { categoryId: '1438' + ran, categoryName: 'Beauty' + ran, level, resultingCommission: '5.00' },
        { categoryId: '147' + ran, categoryName: 'Televisions & Videos' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '1819' + ran,
          categoryName: "Women's Shoes and Clothing" + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '1902' + ran, categoryName: 'Bags and Travel' + ran, level, resultingCommission: '5.00' },
        { categoryId: '1970' + ran, categoryName: 'Sports & Outdoors' + ran, level, resultingCommission: '5.00' },
        { categoryId: '2' + ran, categoryName: 'Mobiles & Tablets' + ran, level, resultingCommission: '3.00' },
        { categoryId: '240' + ran, categoryName: 'Cameras & Drones' + ran, level, resultingCommission: '5.00' },
        { categoryId: '275' + ran, categoryName: 'Small Appliances' + ran, level, resultingCommission: '5.00' },
        { categoryId: '3214' + ran, categoryName: 'Motors' + ran, level, resultingCommission: '5.00' },
        { categoryId: '3374' + ran, categoryName: 'Media, Music & Books' + ran, level, resultingCommission: '5.00' },
        { categoryId: '3752' + ran, categoryName: 'Groceries' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '42062201' + ran,
          categoryName: 'Electronics Accessories' + ran,
          level,
          resultingCommission: '5.00',
        },
        {
          categoryId: '42062401' + ran,
          categoryName: 'Sports Shoes and Clothing' + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '54' + ran, categoryName: 'Computers & Laptops' + ran, level, resultingCommission: '5.00' },
        { categoryId: '6684' + ran, categoryName: 'Digital Goods' + ran, level, resultingCommission: '5.00' },
        {
          categoryId: '7686' + ran,
          categoryName: 'Watches Sunglasses Jewellery' + ran,
          level,
          resultingCommission: '5.00',
        },
        { categoryId: '8706' + ran, categoryName: 'Mother & Baby' + ran, level, resultingCommission: '5.00' },
        { categoryId: '9067' + ran, categoryName: 'Toys & Games' + ran, level, resultingCommission: '5.00' },
      ],
    },
    ret: ['SUCCESS::调用成功'],
    v: '1.0',
  };
};
