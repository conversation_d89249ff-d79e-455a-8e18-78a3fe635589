export default {
  api: 'mtop.global.merchant.subaccount.login.account.render',
  data: {
    data: {
      fields: [
        {
          label: 'Login Email address',
          fieldName: 'email',
          name: '2',
          require: true,
          uiType: 'ContactText',
          value: '<EMAIL>',
          tips: '',
          disabled: false,
          needOtp: false,
          history: false,
        },
        {
          label: 'Login Phone Number',
          fieldName: 'phone',
          name: '3',
          require: true,
          uiType: 'ContactText',
          prefix: '+62',
          value: '',
          tips: '',
          disabled: false,
          needOtp: false,
          history: false,
        },
        {
          label: 'Password',
          fieldName: 'password',
          name: '4',
          require: true,
          uiType: 'PasswordText',
          value: '',
          tips: 'Password needs at least 8 characters, sincluding 1 letter, 1 number and 1 symbol',
          disabled: false,
          needOtp: false,
          history: false,
        },
      ],
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
