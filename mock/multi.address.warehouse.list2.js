export default {
  api: 'mtop.lazada.rc.multi.address.warehouse.list',
  data: {
    data: {
      dataSource: [
        ...Object.keys(Array.apply(null, { length: 10 })).map(item => {
          const addressId = Math.ceil(Math.random() * 100000);
          return {
            addressId,
            code: `CODE_${addressId}`,
            isDefault: true,
            name: `NAME_${addressId}`,
            address: `Singapore 65  Hillview  Dairy  Farm  Bukit  Panjang  Choa  Chu  Kang 650387 ${addressId}`,
            phone: '99324443',
            email: '<EMAIL>',
          };
        }),
      ],
      pageInfo: { current: 1, pageSize: 9, total: 63 },
    },
  },
  ret: ['SUCCESS::调用成功'],
  v: '1.0',
};
