{"name": "lazada-seller-account", "tnpm": {"mode": "npminstall"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "author": {"name": "", "email": ""}, "maintainers": [{"name": "", "email": ""}], "scripts": {"start": "lzd start", "vite": "lzd vite", "build": "lzd build", "prettier": "pretty-quick --staged"}, "pre-commit": ["prettier"], "dependencies": {"@ahooksjs/use-url-state": "^2.5.6", "@ali/filebroker": "^0.2.6", "@ali/global-locale": "^1.0.5", "@ali/global-string-format": "^1.0.7", "@alife/asc-campaign-components": "^1.0.3", "@alife/asc-components": "^1.30.8", "@alife/asc-logistics-components": "^1.1.14", "@alife/form-track-plugin": "^1.1.0", "@alife/intl-util": "^1.0.131", "@alife/lago-utils": "^1.0.2", "@alife/lazada-asc-theme": "^3.23.0", "@alife/lazada-i18n": "^1.0.11", "@alife/workstation-utils": "^1.14.2", "@amap/amap-jsapi-loader": "^1.0.1", "@formily/core": "^2.0.8", "@formily/next": "^2.0.8", "@formily/react": "^2.0.8", "@formily/shared": "^2.3.1", "@vmojs/decorator": "^1.0.15", "ahooks": "^3.3.0", "bizcharts": "^4.1.16", "classnames": "^2.3.0", "dayjs": "^1.11.11", "lodash": "4.17.21", "lodash-es": "^4.17.21", "react-router": "^5.2.0", "react-router-dom": "^5.2.0"}, "devDependencies": {"@ali/eslint-plugin-comments": "^1.0.0", "@alifd/next": "^1.22.16", "@types/lodash": "^4.14.192", "@types/lodash-es": "^4.17.11", "@types/react": "^16.8.0", "@types/react-dom": "^16.8.0", "babel-eslint": "^8.1.2", "eslint": "^5.10.0", "eslint-config-ali": "^4.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-react": "^7.21.5", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "pre-commit": "^1.2.2", "prettier": "^1.17.0", "pretty-quick": "^1.11.0", "react": "^16.8.0", "react-dom": "^16.8.0", "sass": "^1.32.8"}, "repository": "**************************:lazada/lazada-seller-account.git"}