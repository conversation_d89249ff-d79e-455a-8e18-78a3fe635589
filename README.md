# lazada-seller-account

## Project introduction

> Tips: Profile page otp process is diffrent with email/phone update otp process

- https://yuque.antfin-inc.com/docs/share/fc9ace20-85ab-46eb-b8e7-6c2cf5420cc6?# 《ASC Profile Entries》
- https://yuque.antfin-inc.com/docs/share/a30ea022-7e45-4471-b50e-970bc5b0aa85?# 《Logistic APIs》
- https://yuque.antfin-inc.com/docs/share/2dece8d1-7368-4512-a243-c42313f6f5d4?# 《Logistic 梳理》
- https://yuque.antfin-inc.com/docs/share/c1b403bd-01ca-457f-9209-ca30c4d233bb?# 《Profile APIs》
- https://yuque.antfin-inc.com/docs/share/ad1f3774-7cb9-4676-885b-6fd60cc909c7?# 《Profile 更新 OTP 流程》

## Develop

### Vite

We already support [Vite](http://vitejs.dev/) to develop to increase your efficiency!

You can just use `lzd vite` to start your project with [Vite](http://vitejs.dev/)!

### Multiple Languages

Use [lzd-plugin-must](https://npm.alibaba-inc.com/package/@ali/lzd-plugin-must) to transform your code automatically.

You can just right like `#Some words#` wrap with `#` and then just run `lzd must extract`. then your code will change to multiple language usage.

The detail step is :

1. Make sure that you have already installed [lzd](https://npm.alibaba-inc.com/package/@ali/lzd)
2. Use `lzd must extract` to scan & transform your code into a multiple languages usage.
   eg. '#Hello Guys#' -> i18n.formatMessage({id:'xxxxx.hello-guys', defaultMessage:'Hello Guys'})
3. Config your medusa appName in `lzd.config.js`.
4. Use `lzd must uplaod` to upload your multiple languages to [medusa platform](https://mds-portal.alibaba-inc.com/)

## Run server

```
$ lzd start
```

**You can try Vite to quick start your project, it's a beta feature.**

```
$ lzd vite
```

## Publish to daily cdn

> You need to switch you branch format as daily/x.y.z

```
$ lzd publish -d
```

## Publish to online cdn

> You need to publish the version on daily cdn first

```
$ lzd publish -o
```

## Publish to DADA platform

1. Go to [DADA platform](https://dada.alibaba-inc.com/dada/all)
2. Create a new page.
3. Config your CDN address like this.

```json5
{
  hfType: 'lsc', // Menu
  loader: true,
  dadaConfig: {
    externals: [
      // Externals like theme and other resources
      '//{HOST}/intl-dada/dada-theme-lazada/0.0.15/index.css',
    ],
    // Your CDN address, {HOST} can switch different env cdn prefix automatically
    resource: '//{HOST}/lazada/product-quality/1.0.0/index',
  },
}
```
